apiVersion: v1
kind: ConfigMap
metadata:
  name: security-context-template
  namespace: kube-system
  labels:
    app: security-templates
    component: security-context
data:
  security-context.yaml: |
    # Security Context Template for Kubernetes Deployments
    # Apply this to all deployments to ensure container security
    
    securityContext:
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
      fsGroup: 1000
      seccompProfile:
        type: RuntimeDefault
      supplementalGroups: [1000]
    
    containers:
    - name: app
      securityContext:
        allowPrivilegeEscalation: false
        readOnlyRootFilesystem: true
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        capabilities:
          drop:
          - ALL
          add:
          - NET_BIND_SERVICE
        seccompProfile:
          type: RuntimeDefault
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 500m
          memory: 512Mi
      volumeMounts:
      - name: tmp
        mountPath: /tmp
      - name: var-cache
        mountPath: /var/cache
      - name: var-run
        mountPath: /var/run
    
    volumes:
    - name: tmp
      emptyDir: {}
    - name: var-cache
      emptyDir: {}
    - name: var-run
      emptyDir: {}
    
    # Pod Security Standards
    podSecurityContext:
      runAsNonRoot: true
      runAsUser: 1000
      runAsGroup: 1000
      fsGroup: 1000
      seccompProfile:
        type: RuntimeDefault
      supplementalGroups: [1000]
    
    # Network Policy Template
    networkPolicy:
      apiVersion: networking.k8s.io/v1
      kind: NetworkPolicy
      metadata:
        name: app-network-policy
      spec:
        podSelector:
          matchLabels:
            app: app-name
        policyTypes:
        - Ingress
        - Egress
        ingress:
        - from:
          - podSelector:
              matchLabels:
                app: allowed-app
          ports:
          - protocol: TCP
            port: 8080
        egress:
        - to:
          - podSelector:
              matchLabels:
                app: database
          ports:
          - protocol: TCP
            port: 3306
        - to: []
          ports:
          - protocol: TCP
            port: 53
          - protocol: UDP
            port: 53
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-security-standards
  namespace: kube-system
  labels:
    app: security-templates
    component: pod-security
data:
  pod-security-standards.yaml: |
    # Pod Security Standards Configuration
    # Enforce security standards across all namespaces
    
    # Restricted Pod Security Standard
    apiVersion: v1
    kind: Namespace
    metadata:
      name: tenant-namespace
      labels:
        pod-security.kubernetes.io/enforce: restricted
        pod-security.kubernetes.io/audit: restricted
        pod-security.kubernetes.io/warn: restricted
    
    # Security Context Constraints
    securityContextConstraints:
      allowHostDirVolumePlugin: false
      allowHostIPC: false
      allowHostNetwork: false
      allowHostPID: false
      allowHostPorts: false
      allowPrivilegedContainer: false
      allowedCapabilities: []
      defaultAddCapabilities: []
      requiredDropCapabilities:
      - ALL
      runAsUser:
        type: MustRunAsNonRoot
      seLinuxContext:
        type: MustRunAs
      fsGroup:
        type: MustRunAs
        ranges:
        - min: 1000
          max: 65535
      supplementalGroups:
        type: MustRunAs
        ranges:
        - min: 1000
          max: 65535
      volumes:
      - configMap
      - downwardAPI
      - emptyDir
      - persistentVolumeClaim
      - projected
      - secret
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: resource-quotas-template
  namespace: kube-system
  labels:
    app: security-templates
    component: resource-quotas
data:
  resource-quotas.yaml: |
    # Resource Quotas Template
    # Limit resource usage per tenant namespace
    
    apiVersion: v1
    kind: ResourceQuota
    metadata:
      name: tenant-resource-quota
      namespace: tenant-namespace
    spec:
      hard:
        requests.cpu: "2"
        requests.memory: 4Gi
        limits.cpu: "4"
        limits.memory: 8Gi
        persistentvolumeclaims: "10"
        pods: "20"
        services: "10"
        secrets: "20"
        configmaps: "20"
        count/deployments.apps: "10"
        count/replicasets.apps: "20"
        count/jobs.batch: "5"
        count/cronjobs.batch: "2"
    ---
    apiVersion: v1
    kind: LimitRange
    metadata:
      name: tenant-limit-range
      namespace: tenant-namespace
    spec:
      limits:
      - default:
          cpu: 500m
          memory: 512Mi
        defaultRequest:
          cpu: 100m
          memory: 128Mi
        type: Container
      - max:
          cpu: "2"
          memory: 2Gi
        min:
          cpu: 50m
          memory: 64Mi
        type: Container
      - max:
          storage: 10Gi
        min:
          storage: 1Gi
        type: PersistentVolumeClaim
