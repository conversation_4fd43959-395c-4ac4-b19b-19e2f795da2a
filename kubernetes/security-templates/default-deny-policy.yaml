apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: default
  labels:
    app: security-policies
    component: default-deny
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns
  namespace: default
  labels:
    app: security-policies
    component: dns-access
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-istio-system
  namespace: default
  labels:
    app: security-policies
    component: istio-access
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-isolation-template
  namespace: kube-system
  labels:
    app: security-templates
    component: tenant-isolation
spec:
  podSelector:
    matchLabels:
      tenant: "TENANT_ID_PLACEHOLDER"
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from same tenant
  - from:
    - podSelector:
        matchLabels:
          tenant: "TENANT_ID_PLACEHOLDER"
    - namespaceSelector:
        matchLabels:
          name: "tenant-TENANT_ID_PLACEHOLDER"
  # Allow traffic from Istio system
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: security-monitoring
  egress:
  # Allow traffic to same tenant
  - to:
    - podSelector:
        matchLabels:
          tenant: "TENANT_ID_PLACEHOLDER"
    - namespaceSelector:
        matchLabels:
          name: "tenant-TENANT_ID_PLACEHOLDER"
  # Allow traffic to Istio system
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  # Allow HTTPS to external services
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow database access (RDS)
  - to: []
    ports:
    - protocol: TCP
      port: 3306
  # Allow S3 access (HTTPS)
  - to: []
    ports:
    - protocol: TCP
      port: 443
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-access-policy
  namespace: kube-system
  labels:
    app: security-templates
    component: database-access
spec:
  podSelector:
    matchLabels:
      component: backend
  policyTypes:
  - Egress
  egress:
  # Allow database connections
  - to: []
    ports:
    - protocol: TCP
      port: 3306  # MySQL/Aurora
    - protocol: TCP
      port: 5432  # PostgreSQL
  # Allow Redis connections
  - to: []
    ports:
    - protocol: TCP
      port: 6379
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: frontend-access-policy
  namespace: kube-system
  labels:
    app: security-templates
    component: frontend-access
spec:
  podSelector:
    matchLabels:
      component: frontend
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow HTTP/HTTPS traffic from ingress
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 8080
  egress:
  # Allow traffic to backend services
  - to:
    - podSelector:
        matchLabels:
          component: backend
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9000
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-access-policy
  namespace: kube-system
  labels:
    app: security-templates
    component: monitoring-access
spec:
  podSelector:
    matchLabels:
      component: monitoring
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow Prometheus scraping
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    - namespaceSelector:
        matchLabels:
          name: security-monitoring
    ports:
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 3000
  egress:
  # Allow scraping targets
  - to: []
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 9100
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: deny-cross-tenant-communication
  namespace: kube-system
  labels:
    app: security-templates
    component: cross-tenant-deny
spec:
  podSelector:
    matchExpressions:
    - key: tenant
      operator: Exists
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Deny all cross-tenant communication
  - from:
    - podSelector:
        matchExpressions:
        - key: tenant
          operator: DoesNotExist
  egress:
  # Deny all cross-tenant communication
  - to:
    - podSelector:
        matchExpressions:
        - key: tenant
          operator: DoesNotExist
