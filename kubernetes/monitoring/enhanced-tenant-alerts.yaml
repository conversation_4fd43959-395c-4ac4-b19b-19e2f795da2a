apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: enhanced-tenant-alerts
  namespace: monitoring
  labels:
    prometheus: k8s
    role: alert-rules
    tier: enhanced
spec:
  groups:
  - name: tenant.sla.rules
    interval: 30s
    rules:
    # SLA-based alerting rules
    - alert: TenantSLAViolationResponseTime
      expr: |
        (
          histogram_quantile(0.95,
            sum(rate(nginx_http_request_duration_seconds_bucket{namespace=~"tenant-.*"}[5m]))
            by (namespace, le)
          ) * 1000
        ) > 500
      for: 2m
      labels:
        severity: critical
        sla: response_time
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "SLA violation: Response time for {{ $labels.namespace }}"
        description: "95th percentile response time for {{ $labels.namespace }} is {{ $value }}ms, exceeding SLA of 500ms for more than 2 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/sla-response-time"
        dashboard_url: "https://grafana.architrave.com/d/tenant-performance/tenant-performance-dashboard?var-tenant_id={{ $labels.namespace | reReplaceAll \"tenant-\" \"\" }}"

    - alert: TenantSLAViolationAvailability
      expr: |
        (
          sum(rate(nginx_http_requests_total{namespace=~"tenant-.*", status=~"2..|3.."}[5m])) by (namespace) /
          sum(rate(nginx_http_requests_total{namespace=~"tenant-.*"}[5m])) by (namespace)
        ) < 0.995
      for: 1m
      labels:
        severity: critical
        sla: availability
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "SLA violation: Availability for {{ $labels.namespace }}"
        description: "Availability for {{ $labels.namespace }} is {{ $value | humanizePercentage }}, below SLA of 99.5% for more than 1 minute."
        runbook_url: "https://docs.architrave.com/runbooks/sla-availability"

    - alert: TenantSLAViolationErrorRate
      expr: |
        (
          sum(rate(nginx_http_requests_total{namespace=~"tenant-.*", status=~"5.."}[5m])) by (namespace) /
          sum(rate(nginx_http_requests_total{namespace=~"tenant-.*"}[5m])) by (namespace)
        ) > 0.01
      for: 2m
      labels:
        severity: warning
        sla: error_rate
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "SLA violation: Error rate for {{ $labels.namespace }}"
        description: "Error rate for {{ $labels.namespace }} is {{ $value | humanizePercentage }}, exceeding SLA of 1% for more than 2 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/sla-error-rate"

  - name: tenant.performance.rules
    interval: 30s
    rules:
    # Performance-based alerting rules
    - alert: TenantHighCPUUsage
      expr: |
        sum(rate(container_cpu_usage_seconds_total{namespace=~"tenant-.*", container!="POD", container!=""}[5m])) by (namespace) /
        sum(kube_pod_container_resource_limits_cpu_cores{namespace=~"tenant-.*"}) by (namespace) > 0.8
      for: 5m
      labels:
        severity: warning
        category: performance
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High CPU usage for {{ $labels.namespace }}"
        description: "CPU usage for {{ $labels.namespace }} is {{ $value | humanizePercentage }}, above 80% for more than 5 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/high-cpu-usage"

    - alert: TenantHighMemoryUsage
      expr: |
        sum(container_memory_usage_bytes{namespace=~"tenant-.*", container!="POD", container!=""}) by (namespace) /
        sum(kube_pod_container_resource_limits_memory_bytes{namespace=~"tenant-.*"}) by (namespace) > 0.85
      for: 5m
      labels:
        severity: warning
        category: performance
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High memory usage for {{ $labels.namespace }}"
        description: "Memory usage for {{ $labels.namespace }} is {{ $value | humanizePercentage }}, above 85% for more than 5 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/high-memory-usage"

    - alert: TenantPodCrashLooping
      expr: |
        rate(kube_pod_container_status_restarts_total{namespace=~"tenant-.*"}[15m]) > 0
      for: 5m
      labels:
        severity: critical
        category: reliability
        tenant: "{{ $labels.namespace }}"
        pod: "{{ $labels.pod }}"
      annotations:
        summary: "Pod crash looping in {{ $labels.namespace }}"
        description: "Pod {{ $labels.pod }} in {{ $labels.namespace }} is crash looping with {{ $value }} restarts in the last 15 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/pod-crash-looping"

  - name: tenant.database.rules
    interval: 30s
    rules:
    # Database performance alerting rules
    - alert: TenantDatabaseHighConnections
      expr: |
        mysql_global_status_threads_connected{namespace=~"tenant-.*"} /
        mysql_global_variables_max_connections{namespace=~"tenant-.*"} > 0.8
      for: 3m
      labels:
        severity: warning
        category: database
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High database connection usage for {{ $labels.namespace }}"
        description: "Database connection usage for {{ $labels.namespace }} is {{ $value | humanizePercentage }}, above 80% for more than 3 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/high-db-connections"

    - alert: TenantDatabaseSlowQueries
      expr: |
        rate(mysql_global_status_slow_queries{namespace=~"tenant-.*"}[5m]) > 0.1
      for: 5m
      labels:
        severity: warning
        category: database
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High slow query rate for {{ $labels.namespace }}"
        description: "Slow query rate for {{ $labels.namespace }} is {{ $value }} queries/second for more than 5 minutes."
        runbook_url: "https://docs.architrave.com/runbooks/slow-queries"

    - alert: TenantDatabaseConnectionPoolExhausted
      expr: |
        mysql_global_status_threads_connected{namespace=~"tenant-.*"} >=
        mysql_global_variables_max_connections{namespace=~"tenant-.*"}
      for: 1m
      labels:
        severity: critical
        category: database
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "Database connection pool exhausted for {{ $labels.namespace }}"
        description: "All database connections are in use for {{ $labels.namespace }}. New connections will be rejected."
        runbook_url: "https://docs.architrave.com/runbooks/connection-pool-exhausted"

  - name: tenant.scaling.rules
    interval: 30s
    rules:
    # Scaling and capacity alerting rules
    - alert: TenantScalingEventFrequent
      expr: |
        increase(kube_hpa_status_current_replicas{namespace=~"tenant-.*"}[10m]) > 3 or
        increase(kube_hpa_status_current_replicas{namespace=~"tenant-.*"}[10m]) < -3
      for: 1m
      labels:
        severity: info
        category: scaling
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "Frequent scaling events for {{ $labels.namespace }}"
        description: "{{ $labels.namespace }} has scaled by {{ $value }} replicas in the last 10 minutes, indicating potential instability."
        runbook_url: "https://docs.architrave.com/runbooks/frequent-scaling"

    - alert: TenantMaxReplicasReached
      expr: |
        kube_hpa_status_current_replicas{namespace=~"tenant-.*"} >=
        kube_hpa_spec_max_replicas{namespace=~"tenant-.*"}
      for: 5m
      labels:
        severity: warning
        category: scaling
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "Maximum replicas reached for {{ $labels.namespace }}"
        description: "{{ $labels.namespace }} has reached maximum replicas ({{ $value }}). Consider increasing limits or investigating high load."
        runbook_url: "https://docs.architrave.com/runbooks/max-replicas-reached"

  - name: tenant.cost.rules
    interval: 300s
    rules:
    # Cost monitoring alerting rules
    - alert: TenantHighCostUsage
      expr: |
        (
          sum(kube_pod_container_resource_requests_cpu_cores{namespace=~"tenant-.*"}) by (namespace) * 0.0464 * 24 * 30 +
          sum(kube_pod_container_resource_requests_memory_bytes{namespace=~"tenant-.*"}) by (namespace) / 1024 / 1024 / 1024 * 0.00506 * 24 * 30
        ) > 200
      for: 10m
      labels:
        severity: warning
        category: cost
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High estimated monthly cost for {{ $labels.namespace }}"
        description: "Estimated monthly cost for {{ $labels.namespace }} is ${{ $value }}, exceeding budget threshold of $200."
        runbook_url: "https://docs.architrave.com/runbooks/high-cost-usage"
        dashboard_url: "https://grafana.architrave.com/d/tenant-cost-tracking/tenant-cost-tracking-dashboard?var-tenant_id={{ $labels.namespace | reReplaceAll \"tenant-\" \"\" }}"

    - alert: TenantCostSpike
      expr: |
        (
          sum(kube_pod_container_resource_requests_cpu_cores{namespace=~"tenant-.*"}) by (namespace) * 0.0464 +
          sum(kube_pod_container_resource_requests_memory_bytes{namespace=~"tenant-.*"}) by (namespace) / 1024 / 1024 / 1024 * 0.00506
        ) >
        (
          sum(kube_pod_container_resource_requests_cpu_cores{namespace=~"tenant-.*"} offset 1h) by (namespace) * 0.0464 +
          sum(kube_pod_container_resource_requests_memory_bytes{namespace=~"tenant-.*"} offset 1h) by (namespace) / 1024 / 1024 / 1024 * 0.00506
        ) * 1.5
      for: 5m
      labels:
        severity: info
        category: cost
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "Cost spike detected for {{ $labels.namespace }}"
        description: "Hourly cost for {{ $labels.namespace }} has increased by more than 50% compared to 1 hour ago."
        runbook_url: "https://docs.architrave.com/runbooks/cost-spike"

  - name: tenant.security.rules
    interval: 60s
    rules:
    # Security alerting rules
    - alert: TenantUnauthorizedAccess
      expr: |
        sum(rate(nginx_http_requests_total{namespace=~"tenant-.*", status="403"}[5m])) by (namespace) > 0.1
      for: 2m
      labels:
        severity: warning
        category: security
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "High rate of unauthorized access attempts for {{ $labels.namespace }}"
        description: "{{ $labels.namespace }} is receiving {{ $value }} 403 responses per second, indicating potential unauthorized access attempts."
        runbook_url: "https://docs.architrave.com/runbooks/unauthorized-access"

    - alert: TenantSuspiciousTraffic
      expr: |
        sum(rate(nginx_http_requests_total{namespace=~"tenant-.*"}[5m])) by (namespace) >
        sum(rate(nginx_http_requests_total{namespace=~"tenant-.*"}[5m] offset 1h)) by (namespace) * 3
      for: 5m
      labels:
        severity: info
        category: security
        tenant: "{{ $labels.namespace }}"
      annotations:
        summary: "Suspicious traffic spike for {{ $labels.namespace }}"
        description: "Traffic to {{ $labels.namespace }} is {{ $value }}x higher than 1 hour ago, which may indicate a DDoS attack or unusual activity."
        runbook_url: "https://docs.architrave.com/runbooks/suspicious-traffic"
