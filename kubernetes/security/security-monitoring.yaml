apiVersion: v1
kind: Namespace
metadata:
  name: security-monitoring
  labels:
    name: security-monitoring
    istio-injection: enabled
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-rules
  namespace: security-monitoring
data:
  tenant_security_rules.yaml: |
    # Custom Falco rules for tenant security monitoring
    
    - rule: Unauthorized Cross-Tenant Access
      desc: Detect access between different tenant namespaces
      condition: >
        k8s_audit and
        ka.target.namespace != ka.user.name and
        ka.target.namespace contains "tenant-" and
        not ka.user.name in (system_users)
      output: >
        Unauthorized cross-tenant access detected
        (user=%ka.user.name target_namespace=%ka.target.namespace 
         source_ip=%ka.source_ips verb=%ka.verb uri=%ka.uri.param)
      priority: CRITICAL
      tags: [tenant-isolation, security]
    
    - rule: Suspicious Database Access
      desc: Detect suspicious database access patterns
      condition: >
        spawned_process and
        proc.name in (mysql, psql, sqlcmd) and
        not proc.pname in (php-fpm, node, python, java)
      output: >
        Suspicious database access detected
        (user=%user.name command=%proc.cmdline container=%container.name 
         image=%container.image.repository)
      priority: HIGH
      tags: [database, security]
    
    - rule: Privilege Escalation Attempt
      desc: Detect attempts to escalate privileges
      condition: >
        spawned_process and
        (proc.name in (sudo, su, doas) or
         proc.cmdline contains "chmod +s" or
         proc.cmdline contains "setuid")
      output: >
        Privilege escalation attempt detected
        (user=%user.name command=%proc.cmdline container=%container.name)
      priority: CRITICAL
      tags: [privilege-escalation, security]
    
    - rule: Sensitive File Access
      desc: Detect access to sensitive files
      condition: >
        open_read and
        (fd.name startswith "/etc/passwd" or
         fd.name startswith "/etc/shadow" or
         fd.name startswith "/root/.ssh" or
         fd.name contains "id_rsa" or
         fd.name contains "id_dsa")
      output: >
        Sensitive file access detected
        (user=%user.name file=%fd.name container=%container.name 
         command=%proc.cmdline)
      priority: HIGH
      tags: [file-access, security]
    
    - rule: Network Anomaly Detection
      desc: Detect unusual network connections
      condition: >
        inbound_outbound and
        not fd.sport in (80, 443, 22, 3306, 5432, 6379, 9090, 9093) and
        not container.name contains "istio"
      output: >
        Unusual network connection detected
        (container=%container.name connection=%fd.name 
         sport=%fd.sport dport=%fd.dport)
      priority: MEDIUM
      tags: [network, security]

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-security-rules
  namespace: security-monitoring
data:
  security_alerts.yaml: |
    groups:
    - name: security.rules
      rules:
      
      # Critical Security Alerts
      - alert: CriticalSecurityEvent
        expr: increase(falco_events{priority="Critical"}[5m]) > 0
        for: 0m
        labels:
          severity: critical
          category: security
        annotations:
          summary: "Critical security event detected"
          description: "Falco detected a critical security event: {{ $labels.rule }}"
          runbook_url: "https://wiki.company.com/security/incident-response"
      
      - alert: HighSecurityEvent
        expr: increase(falco_events{priority="High"}[5m]) > 2
        for: 2m
        labels:
          severity: high
          category: security
        annotations:
          summary: "Multiple high-severity security events"
          description: "{{ $value }} high-severity security events in the last 5 minutes"
      
      # Authentication and Authorization
      - alert: FailedAuthenticationSpike
        expr: increase(kubernetes_audit_total{verb="create",objectRef_resource="tokenreviews"}[5m]) > 10
        for: 1m
        labels:
          severity: warning
          category: authentication
        annotations:
          summary: "Spike in failed authentication attempts"
          description: "{{ $value }} failed authentication attempts in the last 5 minutes"
      
      - alert: UnauthorizedAPIAccess
        expr: increase(kubernetes_audit_total{responseStatus_code=~"401|403"}[5m]) > 5
        for: 1m
        labels:
          severity: warning
          category: authorization
        annotations:
          summary: "Unauthorized API access attempts"
          description: "{{ $value }} unauthorized API access attempts detected"
      
      # Container Security
      - alert: PrivilegedContainerRunning
        expr: kube_pod_container_status_running{container=~".*"} and on(pod, namespace) kube_pod_spec_containers_security_context_privileged == 1
        for: 0m
        labels:
          severity: high
          category: container-security
        annotations:
          summary: "Privileged container detected"
          description: "Privileged container {{ $labels.container }} running in namespace {{ $labels.namespace }}"
      
      - alert: ContainerRunningAsRoot
        expr: kube_pod_container_status_running{container=~".*"} and on(pod, namespace) kube_pod_spec_containers_security_context_run_as_user == 0
        for: 5m
        labels:
          severity: medium
          category: container-security
        annotations:
          summary: "Container running as root"
          description: "Container {{ $labels.container }} in namespace {{ $labels.namespace }} is running as root"
      
      # Network Security
      - alert: SuspiciousNetworkActivity
        expr: increase(container_network_receive_bytes_total[5m]) > 100000000 or increase(container_network_transmit_bytes_total[5m]) > 100000000
        for: 2m
        labels:
          severity: medium
          category: network-security
        annotations:
          summary: "Suspicious network activity detected"
          description: "High network traffic detected for container {{ $labels.name }}"
      
      # Resource Abuse
      - alert: CPUUsageSpike
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
          category: resource-abuse
        annotations:
          summary: "High CPU usage detected"
          description: "Container {{ $labels.name }} CPU usage is {{ $value }}%"
      
      - alert: MemoryUsageSpike
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
          category: resource-abuse
        annotations:
          summary: "High memory usage detected"
          description: "Container {{ $labels.name }} memory usage is {{ $value }}%"
      
      # Database Security
      - alert: DatabaseConnectionSpike
        expr: increase(mysql_global_status_connections[5m]) > 100
        for: 2m
        labels:
          severity: warning
          category: database-security
        annotations:
          summary: "Database connection spike detected"
          description: "{{ $value }} new database connections in the last 5 minutes"
      
      - alert: DatabaseSlowQueries
        expr: increase(mysql_global_status_slow_queries[5m]) > 10
        for: 2m
        labels:
          severity: warning
          category: database-security
        annotations:
          summary: "Database slow queries detected"
          description: "{{ $value }} slow queries detected in the last 5 minutes"

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-dashboard
  namespace: security-monitoring
data:
  security-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Security Overview Dashboard",
        "tags": ["security", "monitoring"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Security Events by Severity",
            "type": "stat",
            "targets": [
              {
                "expr": "sum by (priority) (increase(falco_events[1h]))",
                "legendFormat": "{{ priority }}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {
                  "mode": "thresholds"
                },
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": null},
                    {"color": "yellow", "value": 5},
                    {"color": "red", "value": 10}
                  ]
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Failed Authentication Attempts",
            "type": "graph",
            "targets": [
              {
                "expr": "increase(kubernetes_audit_total{responseStatus_code=~\"401|403\"}[5m])",
                "legendFormat": "Failed Auth"
              }
            ]
          },
          {
            "id": 3,
            "title": "Container Security Violations",
            "type": "table",
            "targets": [
              {
                "expr": "kube_pod_container_status_running and on(pod, namespace) kube_pod_spec_containers_security_context_privileged == 1",
                "format": "table"
              }
            ]
          },
          {
            "id": 4,
            "title": "Network Security Events",
            "type": "graph",
            "targets": [
              {
                "expr": "increase(falco_events{tags=~\".*network.*\"}[5m])",
                "legendFormat": "Network Events"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-event-processor
  namespace: security-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: security-event-processor
  template:
    metadata:
      labels:
        app: security-event-processor
    spec:
      serviceAccountName: security-monitor
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 2000
      containers:
      - name: event-processor
        image: security/event-processor:latest
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        env:
        - name: PROMETHEUS_URL
          value: "http://prometheus:9090"
        - name: ALERTMANAGER_URL
          value: "http://alertmanager:9093"
        - name: SLACK_WEBHOOK_URL
          valueFrom:
            secretKeyRef:
              name: security-alerts
              key: slack-webhook-url
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: cache
          mountPath: /app/cache
      volumes:
      - name: tmp
        emptyDir: {}
      - name: cache
        emptyDir: {}

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: security-monitor
  namespace: security-monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: security-monitor
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["security.istio.io"]
  resources: ["peerauthentications", "authorizationpolicies"]
  verbs: ["get", "list", "watch"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: security-monitor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: security-monitor
subjects:
- kind: ServiceAccount
  name: security-monitor
  namespace: security-monitoring
