#!/usr/bin/env python3
"""
Comprehensive System Verification
Checks for issues, runs SELECT statements, verifies feature flags, health checks
Verifies backend, frontend, nginx, database
"""

import subprocess
import sys
import time
import tempfile
from datetime import datetime

def run_command(command, timeout=15):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def check_system_issues():
    """Check for system issues."""
    print_header("SYSTEM ISSUES CHECK")
    
    issues = []
    
    # Check kubectl connectivity
    success, stdout, stderr = run_command("kubectl version --client", 10)
    if success:
        print_result("kubectl Client", True, "Working")
    else:
        issues.append("kubectl client not working")
        print_result("kubectl Client", False, "Not working")
    
    # Check cluster connectivity
    success, stdout, stderr = run_command("kubectl cluster-info --request-timeout=5s", 8)
    if success:
        print_result("Cluster Connectivity", True, "Working")
    else:
        issues.append("Cluster connectivity issues")
        print_result("Cluster Connectivity", False, "Connection issues")
    
    # Check tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant- | wc -l", 8)
    if success:
        tenant_count = stdout
        print_result("Tenant Namespaces", True, f"{tenant_count} tenant namespaces found")
    else:
        issues.append("Cannot get tenant namespaces")
        print_result("Tenant Namespaces", False, "Cannot get namespaces")
    
    return issues

def run_database_select_statements():
    """Run SELECT statements on database."""
    print_header("DATABASE SELECT STATEMENTS")
    
    # Create database test pod
    db_test_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-select-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_test_yaml)
        pod_file = f.name
    
    try:
        # Create pod
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if not success:
            print_result("Database Test Pod Creation", False, "Could not create pod")
            return []
        
        print_result("Database Test Pod Creation", True, "Pod created")
        
        # Wait for pod to be ready
        time.sleep(10)
        success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/db-select-test --timeout=30s")
        if not success:
            print_result("Database Test Pod Ready", False, "Pod not ready")
            return []
        
        print_result("Database Test Pod Ready", True, "Pod ready")
        
        # Database connection details
        db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        db_name = "architrave"
        
        # Test SELECT statements
        select_queries = [
            ("SELECT 1 as test_connection;", "Connection Test"),
            ("SELECT NOW() as current_time;", "Current Time"),
            ("SELECT VERSION() as mysql_version;", "MySQL Version"),
            ("SHOW DATABASES;", "Show Databases"),
            ("USE architrave; SHOW TABLES;", "Show Tables"),
            ("USE architrave; SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave';", "Table Count"),
            ("USE architrave; SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave' LIMIT 5;", "Sample Tables"),
        ]
        
        results = []
        for query, test_name in select_queries:
            success, stdout, stderr = run_command(
                f"kubectl exec db-select-test -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\"", 15)
            
            if success and stdout:
                print_result(f"SELECT - {test_name}", True, f"Query executed successfully")
                results.append(True)
                if "table_count" in query:
                    # Extract table count
                    lines = stdout.split('\n')
                    if len(lines) > 1:
                        print(f"   Table count: {lines[1]}")
            else:
                print_result(f"SELECT - {test_name}", False, f"Query failed: {stderr}")
                results.append(False)
        
        return results
        
    finally:
        # Cleanup
        run_command("kubectl delete pod db-select-test --ignore-not-found=true")
        import os
        os.unlink(pod_file)

def verify_feature_flags():
    """Verify feature flags in onboarding script."""
    print_header("FEATURE FLAGS VERIFICATION")
    
    # Check onboarding script feature flags
    success, stdout, stderr = run_command("grep -n 'skip-db-import\\|skip-s3-setup\\|skip-dns\\|skip-monitoring\\|skip-istio' tenant-management/scripts/advanced_tenant_onboard.py")
    
    if success:
        flags_found = stdout.count('skip-')
        print_result("Onboarding Feature Flags", True, f"Found {flags_found} skip flags")
        
        # Check specific flags
        expected_flags = ['skip-db-import', 'skip-s3-setup', 'skip-dns', 'skip-monitoring', 'skip-istio']
        for flag in expected_flags:
            if flag in stdout:
                print_result(f"Flag: --{flag}", True, "Available")
            else:
                print_result(f"Flag: --{flag}", False, "Not found")
    else:
        print_result("Onboarding Feature Flags", False, "Could not check flags")
    
    # Check offboarding script feature flags
    success, stdout, stderr = run_command("grep -n 'force\\|verify\\|debug' tenant-management/scripts/advanced_tenant_offboard.py")
    
    if success:
        flags_found = stdout.count('--')
        print_result("Offboarding Feature Flags", True, f"Found flags in script")
    else:
        print_result("Offboarding Feature Flags", False, "Could not check flags")

def check_backend_health():
    """Check backend health."""
    print_header("BACKEND HEALTH CHECK")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant-", 8)
    if not success:
        print_result("Backend Health Check", False, "Cannot get namespaces")
        return []
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()][:3]  # Check first 3
    results = []
    
    for ns in tenant_namespaces:
        tenant_id = ns.replace('tenant-', '')
        
        # Check backend deployment
        success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers --request-timeout=5s | grep backend", 8)
        if success and stdout:
            deployment_name = stdout.split()[0]
            print_result(f"Backend Deployment - {tenant_id}", True, f"Found: {deployment_name}")
            results.append(True)
            
            # Check backend pods
            success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers --request-timeout=5s | grep backend", 8)
            if success and stdout:
                pod_status = stdout.split()[2] if len(stdout.split()) > 2 else "Unknown"
                is_running = "Running" in pod_status
                print_result(f"Backend Pod - {tenant_id}", is_running, f"Status: {pod_status}")
                results.append(is_running)
            else:
                print_result(f"Backend Pod - {tenant_id}", False, "No backend pods found")
                results.append(False)
                
            # Check backend service
            success, stdout, stderr = run_command(f"kubectl get service -n {ns} --no-headers --request-timeout=5s | grep backend", 8)
            if success and stdout:
                service_name = stdout.split()[0]
                print_result(f"Backend Service - {tenant_id}", True, f"Found: {service_name}")
                results.append(True)
            else:
                print_result(f"Backend Service - {tenant_id}", False, "No backend service found")
                results.append(False)
        else:
            print_result(f"Backend Deployment - {tenant_id}", False, "No backend deployment found")
            results.append(False)
    
    return results

def check_frontend_nginx_health():
    """Check frontend and nginx health."""
    print_header("FRONTEND & NGINX HEALTH CHECK")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant-", 8)
    if not success:
        print_result("Frontend/Nginx Health Check", False, "Cannot get namespaces")
        return []
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()][:3]  # Check first 3
    results = []
    
    for ns in tenant_namespaces:
        tenant_id = ns.replace('tenant-', '')
        
        # Check frontend deployment
        success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers --request-timeout=5s | grep frontend", 8)
        if success and stdout:
            deployment_name = stdout.split()[0]
            print_result(f"Frontend Deployment - {tenant_id}", True, f"Found: {deployment_name}")
            results.append(True)
            
            # Check frontend pods
            success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers --request-timeout=5s | grep frontend", 8)
            if success and stdout:
                pod_status = stdout.split()[2] if len(stdout.split()) > 2 else "Unknown"
                is_running = "Running" in pod_status
                print_result(f"Frontend Pod - {tenant_id}", is_running, f"Status: {pod_status}")
                results.append(is_running)
            else:
                print_result(f"Frontend Pod - {tenant_id}", False, "No frontend pods found")
                results.append(False)
        else:
            print_result(f"Frontend Deployment - {tenant_id}", False, "No frontend deployment found")
            results.append(False)
        
        # Check nginx deployment
        success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers --request-timeout=5s | grep nginx", 8)
        if success and stdout:
            deployment_name = stdout.split()[0]
            print_result(f"Nginx Deployment - {tenant_id}", True, f"Found: {deployment_name}")
            results.append(True)
        else:
            print_result(f"Nginx Deployment - {tenant_id}", False, "No nginx deployment found")
            results.append(False)
    
    return results

def verify_ecr_images():
    """Verify ECR images are being used."""
    print_header("ECR IMAGES VERIFICATION")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant-", 8)
    if not success:
        print_result("ECR Images Check", False, "Cannot get namespaces")
        return []
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()][:2]  # Check first 2
    
    ecr_images = {
        'frontend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41',
        'backend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test',
        'rabbitmq': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02',
        'nginx': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl'
    }
    
    results = []
    
    for ns in tenant_namespaces:
        tenant_id = ns.replace('tenant-', '')
        
        # Check deployments for ECR images
        success, stdout, stderr = run_command(f"kubectl get deployments -n {ns} -o yaml --request-timeout=10s", 12)
        if success:
            for component, image in ecr_images.items():
                if image in stdout:
                    print_result(f"ECR Image - {component} ({tenant_id})", True, f"Using correct ECR image")
                    results.append(True)
                else:
                    print_result(f"ECR Image - {component} ({tenant_id})", False, f"Not using ECR image")
                    results.append(False)
        else:
            print_result(f"ECR Images Check - {tenant_id}", False, "Cannot get deployment info")
            results.extend([False] * 4)
    
    return results

def main():
    """Main verification function."""
    print("🔍 COMPREHENSIVE SYSTEM VERIFICATION")
    print("=" * 60)
    print(f"Verification started at: {datetime.now()}")
    
    all_results = []
    
    # Run all checks
    issues = check_system_issues()
    db_results = run_database_select_statements()
    verify_feature_flags()
    backend_results = check_backend_health()
    frontend_results = check_frontend_nginx_health()
    ecr_results = verify_ecr_images()
    
    # Combine results
    all_results.extend(db_results)
    all_results.extend(backend_results)
    all_results.extend(frontend_results)
    all_results.extend(ecr_results)
    
    # Print summary
    print_header("VERIFICATION SUMMARY")
    
    if issues:
        print("❌ ISSUES FOUND:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ NO MAJOR ISSUES FOUND")
    
    if all_results:
        passed = sum(all_results)
        total = len(all_results)
        success_rate = (passed / total) * 100
        
        print(f"\n📊 OVERALL RESULTS:")
        print(f"  Tests passed: {passed}/{total}")
        print(f"  Success rate: {success_rate:.1f}%")
        
        if success_rate >= 70:
            print("\n🎉 SYSTEM VERIFICATION SUCCESSFUL!")
            return 0
        else:
            print("\n⚠️ SYSTEM VERIFICATION ISSUES DETECTED")
            return 1
    else:
        print("\n⚠️ NO RESULTS TO ANALYZE")
        return 1

if __name__ == "__main__":
    sys.exit(main())
