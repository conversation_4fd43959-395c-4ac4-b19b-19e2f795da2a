#!/bin/bash

# Web-based Tenant Management Interface Deployment Script
# This script deploys the enhanced React-based tenant management UI

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
UI_NAMESPACE="tenant-management"
UI_SERVICE_NAME="tenant-management-ui"
API_SERVICE_NAME="tenant-management-api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if docker is available
    if ! command -v docker &> /dev/null; then
        log_error "docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Build and push UI Docker image
build_ui_image() {
    log_info "Building tenant management UI Docker image..."
    
    cd "$PROJECT_ROOT/ui"
    
    # Build the React application
    if [ -f "package.json" ]; then
        log_info "Installing dependencies..."
        npm install
        
        log_info "Building React application..."
        npm run build
        
        # Build Docker image
        log_info "Building Docker image..."
        docker build -t tenant-management-ui:latest .
        
        # Tag for local registry (adjust as needed)
        docker tag tenant-management-ui:latest localhost:5000/tenant-management-ui:latest
        
        log_success "UI Docker image built successfully"
    else
        log_error "package.json not found in UI directory"
        exit 1
    fi
    
    cd - > /dev/null
}

# Deploy namespace and RBAC
deploy_namespace_and_rbac() {
    log_info "Creating namespace and RBAC..."
    
    # Create namespace
    kubectl create namespace $UI_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    # Create ServiceAccount, ClusterRole, and ClusterRoleBinding
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-management-sa
  namespace: $UI_NAMESPACE
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-management-role
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies", "ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["monitoring.coreos.com"]
  resources: ["servicemonitors", "prometheusrules"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["keda.sh"]
  resources: ["scaledobjects"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-management-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-management-role
subjects:
- kind: ServiceAccount
  name: tenant-management-sa
  namespace: $UI_NAMESPACE
EOF
    
    log_success "Namespace and RBAC created"
}

# Deploy backend API
deploy_backend_api() {
    log_info "Deploying tenant management API..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $API_SERVICE_NAME
  namespace: $UI_NAMESPACE
  labels:
    app: $API_SERVICE_NAME
spec:
  replicas: 2
  selector:
    matchLabels:
      app: $API_SERVICE_NAME
  template:
    metadata:
      labels:
        app: $API_SERVICE_NAME
    spec:
      serviceAccountName: tenant-management-sa
      containers:
      - name: api
        image: node:16-alpine
        ports:
        - containerPort: 3001
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3001"
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        command:
        - /bin/sh
        - -c
        - |
          cat > /app/server.js << 'EOL'
          const express = require('express');
          const cors = require('cors');
          const k8s = require('@kubernetes/client-node');
          
          const app = express();
          const port = process.env.PORT || 3001;
          
          // Kubernetes client setup
          const kc = new k8s.KubeConfig();
          kc.loadFromCluster();
          const k8sApi = kc.makeApiClient(k8s.CoreV1Api);
          const k8sAppsApi = kc.makeApiClient(k8s.AppsV1Api);
          
          app.use(cors());
          app.use(express.json());
          
          // Health check
          app.get('/health', (req, res) => {
            res.json({ status: 'healthy', timestamp: new Date().toISOString() });
          });
          
          // Get all tenants
          app.get('/api/tenants', async (req, res) => {
            try {
              const namespaces = await k8sApi.listNamespace();
              const tenants = namespaces.body.items
                .filter(ns => ns.metadata.name.startsWith('tenant-'))
                .map(ns => ({
                  id: ns.metadata.name.replace('tenant-', ''),
                  name: ns.metadata.labels?.['tenant-name'] || ns.metadata.name,
                  status: ns.status.phase.toLowerCase(),
                  tier: ns.metadata.labels?.tier || 'standard',
                  created: ns.metadata.creationTimestamp,
                  namespace: ns.metadata.name
                }));
              res.json(tenants);
            } catch (error) {
              res.status(500).json({ error: error.message });
            }
          });
          
          // Get tenant details
          app.get('/api/tenants/:id', async (req, res) => {
            try {
              const tenantId = req.params.id;
              const namespace = \`tenant-\${tenantId}\`;
              
              const [nsResponse, podsResponse, deploymentsResponse] = await Promise.all([
                k8sApi.readNamespace(namespace),
                k8sApi.listNamespacedPod(namespace),
                k8sAppsApi.listNamespacedDeployment(namespace)
              ]);
              
              const tenant = {
                id: tenantId,
                name: nsResponse.body.metadata.labels?.['tenant-name'] || namespace,
                status: nsResponse.body.status.phase.toLowerCase(),
                tier: nsResponse.body.metadata.labels?.tier || 'standard',
                created: nsResponse.body.metadata.creationTimestamp,
                namespace: namespace,
                pods: podsResponse.body.items.length,
                deployments: deploymentsResponse.body.items.length,
                resources: {
                  pods: podsResponse.body.items.map(pod => ({
                    name: pod.metadata.name,
                    status: pod.status.phase,
                    ready: pod.status.conditions?.find(c => c.type === 'Ready')?.status === 'True'
                  }))
                }
              };
              
              res.json(tenant);
            } catch (error) {
              res.status(500).json({ error: error.message });
            }
          });
          
          // Create tenant
          app.post('/api/tenants', async (req, res) => {
            try {
              const { tenantId, tenantName, tier = 'standard' } = req.body;
              const namespace = \`tenant-\${tenantId}\`;
              
              const namespaceManifest = {
                metadata: {
                  name: namespace,
                  labels: {
                    'tenant-name': tenantName,
                    'tenant': tenantId,
                    'tier': tier,
                    'managed-by': 'tenant-management-ui'
                  }
                }
              };
              
              await k8sApi.createNamespace(namespaceManifest);
              res.json({ message: 'Tenant created successfully', tenantId, namespace });
            } catch (error) {
              res.status(500).json({ error: error.message });
            }
          });
          
          // Delete tenant
          app.delete('/api/tenants/:id', async (req, res) => {
            try {
              const tenantId = req.params.id;
              const namespace = \`tenant-\${tenantId}\`;
              
              await k8sApi.deleteNamespace(namespace);
              res.json({ message: 'Tenant deleted successfully', tenantId });
            } catch (error) {
              res.status(500).json({ error: error.message });
            }
          });
          
          app.listen(port, () => {
            console.log(\`Tenant Management API listening on port \${port}\`);
          });
          EOL
          
          cd /app
          npm init -y
          npm install express cors @kubernetes/client-node
          node server.js
        workingDir: /app
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: $API_SERVICE_NAME
  namespace: $UI_NAMESPACE
  labels:
    app: $API_SERVICE_NAME
spec:
  selector:
    app: $API_SERVICE_NAME
  ports:
  - port: 3001
    targetPort: 3001
    name: http
  type: ClusterIP
EOF
    
    log_success "Backend API deployed"
}

# Deploy frontend UI
deploy_frontend_ui() {
    log_info "Deploying tenant management UI..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $UI_SERVICE_NAME
  namespace: $UI_NAMESPACE
  labels:
    app: $UI_SERVICE_NAME
spec:
  replicas: 2
  selector:
    matchLabels:
      app: $UI_SERVICE_NAME
  template:
    metadata:
      labels:
        app: $UI_SERVICE_NAME
    spec:
      containers:
      - name: ui
        image: nginx:alpine
        ports:
        - containerPort: 80
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d
        - name: ui-content
          mountPath: /usr/share/nginx/html
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: nginx-config
        configMap:
          name: ui-nginx-config
      - name: ui-content
        configMap:
          name: ui-content
---
apiVersion: v1
kind: Service
metadata:
  name: $UI_SERVICE_NAME
  namespace: $UI_NAMESPACE
  labels:
    app: $UI_SERVICE_NAME
spec:
  selector:
    app: $UI_SERVICE_NAME
  ports:
  - port: 80
    targetPort: 80
    name: http
  type: ClusterIP
EOF
    
    log_success "Frontend UI deployed"
}

# Create ConfigMaps for UI
create_ui_configmaps() {
    log_info "Creating UI configuration..."
    
    # Nginx configuration
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-nginx-config
  namespace: $UI_NAMESPACE
data:
  default.conf: |
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # API proxy
        location /api/ {
            proxy_pass http://$API_SERVICE_NAME:3001;
            proxy_set_header Host \$host;
            proxy_set_header X-Real-IP \$remote_addr;
            proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto \$scheme;
        }
        
        # React app
        location / {
            try_files \$uri \$uri/ /index.html;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }
        
        # Static assets
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
EOF
    
    # Basic UI content (placeholder)
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-content
  namespace: $UI_NAMESPACE
data:
  index.html: |
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Tenant Management System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 30px; }
            .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
            .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
            .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🏢 Tenant Management System</h1>
                <p>Enhanced web-based tenant management interface</p>
            </div>
            
            <div class="status success">
                ✅ Tenant Management UI is successfully deployed!
            </div>
            
            <div class="status info">
                ℹ️ The React-based UI will be available once the build process is complete.
            </div>
            
            <h2>Features</h2>
            <ul>
                <li>📊 Real-time tenant dashboard with performance metrics</li>
                <li>🚀 Self-service tenant onboarding</li>
                <li>⚙️ Resource management and auto-scaling configuration</li>
                <li>💰 Cost tracking and billing integration</li>
                <li>📈 SLA monitoring and alerting</li>
                <li>🔍 Log aggregation with tenant isolation</li>
            </ul>
            
            <h2>API Endpoints</h2>
            <ul>
                <li><code>GET /api/tenants</code> - List all tenants</li>
                <li><code>GET /api/tenants/:id</code> - Get tenant details</li>
                <li><code>POST /api/tenants</code> - Create new tenant</li>
                <li><code>DELETE /api/tenants/:id</code> - Delete tenant</li>
            </ul>
            
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Build and deploy the React application</li>
                <li>Configure authentication and authorization</li>
                <li>Set up monitoring and alerting</li>
                <li>Integrate with cost tracking systems</li>
            </ol>
        </div>
    </body>
    </html>
EOF
    
    log_success "UI configuration created"
}

# Deploy ingress
deploy_ingress() {
    log_info "Deploying ingress for UI access..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: tenant-management-ingress
  namespace: $UI_NAMESPACE
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: tenant-management.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: $UI_SERVICE_NAME
            port:
              number: 80
EOF
    
    log_success "Ingress deployed"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Check if pods are running
    kubectl wait --for=condition=ready pod -l app=$UI_SERVICE_NAME -n $UI_NAMESPACE --timeout=300s
    kubectl wait --for=condition=ready pod -l app=$API_SERVICE_NAME -n $UI_NAMESPACE --timeout=300s
    
    # Get service information
    UI_SERVICE_IP=$(kubectl get service $UI_SERVICE_NAME -n $UI_NAMESPACE -o jsonpath='{.spec.clusterIP}')
    API_SERVICE_IP=$(kubectl get service $API_SERVICE_NAME -n $UI_NAMESPACE -o jsonpath='{.spec.clusterIP}')
    
    log_success "Deployment verification completed"
    log_info "UI Service IP: $UI_SERVICE_IP"
    log_info "API Service IP: $API_SERVICE_IP"
    log_info "Access the UI at: http://tenant-management.local (add to /etc/hosts)"
}

# Main execution
main() {
    local skip_build=false
    local skip_prerequisites=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-build)
                skip_build=true
                shift
                ;;
            --skip-prerequisites)
                skip_prerequisites=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --skip-build         Skip building Docker image"
                echo "  --skip-prerequisites Skip prerequisite checks"
                echo "  -h, --help           Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    log_info "Starting tenant management UI deployment..."
    
    if [ "$skip_prerequisites" = false ]; then
        check_prerequisites
    fi
    
    if [ "$skip_build" = false ]; then
        build_ui_image
    fi
    
    deploy_namespace_and_rbac
    deploy_backend_api
    create_ui_configmaps
    deploy_frontend_ui
    deploy_ingress
    verify_deployment
    
    log_success "Tenant management UI deployment completed!"
    log_info "Next steps:"
    log_info "1. Add 'tenant-management.local' to your /etc/hosts file"
    log_info "2. Access the UI at http://tenant-management.local"
    log_info "3. Configure authentication and authorization"
    log_info "4. Integrate with monitoring and alerting systems"
}

# Run main function
main "$@"
