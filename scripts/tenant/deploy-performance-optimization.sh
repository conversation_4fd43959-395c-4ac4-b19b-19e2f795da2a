#!/bin/bash

# Enhanced Tenant Performance Optimization Deployment Script
# This script deploys performance optimization features for high-traffic scenarios

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
MONITORING_NAMESPACE="monitoring"
TENANT_NAMESPACE_PREFIX="tenant-"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi

    # Check if helm is available
    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed or not in PATH"
        exit 1
    fi

    # Check cluster connectivity
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    log_success "Prerequisites check passed"
}

# Deploy enhanced autoscaling
deploy_enhanced_autoscaling() {
    log_info "Deploying enhanced autoscaling configurations..."

    # Install KEDA if not already installed
    if ! kubectl get namespace keda-system &> /dev/null; then
        log_info "Installing KEDA..."
        helm repo add kedacore https://kedacore.github.io/charts
        helm repo update
        helm install keda kedacore/keda --namespace keda-system --create-namespace

        # Wait for KEDA to be ready
        kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=keda-operator -n keda-system --timeout=300s
        log_success "KEDA installed successfully"
    else
        log_info "KEDA already installed"
    fi

    # Install Vertical Pod Autoscaler if not already installed
    if ! kubectl get crd verticalpodautoscalers.autoscaling.k8s.io &> /dev/null; then
        log_info "Installing Vertical Pod Autoscaler..."
        git clone https://github.com/kubernetes/autoscaler.git /tmp/autoscaler || true
        cd /tmp/autoscaler/vertical-pod-autoscaler
        ./hack/vpa-install.sh
        cd - > /dev/null
        log_success "VPA installed successfully"
    else
        log_info "VPA already installed"
    fi

    log_success "Enhanced autoscaling deployed"
}

# Deploy performance monitoring
deploy_performance_monitoring() {
    log_info "Deploying performance monitoring components..."

    # Create monitoring namespace if it doesn't exist
    kubectl create namespace $MONITORING_NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

    # Deploy enhanced Prometheus rules
    if [ -f "$PROJECT_ROOT/kubernetes/monitoring/enhanced-tenant-alerts.yaml" ]; then
        log_info "Deploying enhanced alerting rules..."
        kubectl apply -f "$PROJECT_ROOT/kubernetes/monitoring/enhanced-tenant-alerts.yaml"
        log_success "Enhanced alerting rules deployed"
    fi

    # Deploy Grafana dashboards
    if [ -d "$PROJECT_ROOT/monitoring/grafana-dashboards" ]; then
        log_info "Deploying Grafana dashboards..."

        # Create ConfigMaps for dashboards
        for dashboard in "$PROJECT_ROOT/monitoring/grafana-dashboards"/*.json; do
            if [ -f "$dashboard" ]; then
                dashboard_name=$(basename "$dashboard" .json)
                kubectl create configmap "grafana-dashboard-$dashboard_name" \
                    --from-file="$dashboard" \
                    --namespace=$MONITORING_NAMESPACE \
                    --dry-run=client -o yaml | \
                kubectl label --local -f - grafana_dashboard=1 -o yaml | \
                kubectl apply -f -
                log_info "Deployed dashboard: $dashboard_name"
            fi
        done

        log_success "Grafana dashboards deployed"
    fi

    log_success "Performance monitoring deployed"
}

# Deploy database connection pooling optimization
deploy_database_optimization() {
    log_info "Deploying database connection pooling optimization..."

    # Create ConfigMap for optimized database configuration
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: database-optimization-config
  namespace: $MONITORING_NAMESPACE
data:
  mysql-optimization.cnf: |
    [mysqld]
    # Connection pooling optimization
    max_connections = 200
    max_user_connections = 50
    thread_cache_size = 16
    table_open_cache = 4000
    table_definition_cache = 2000

    # Performance optimization
    innodb_buffer_pool_size = 1G
    innodb_log_file_size = 256M
    innodb_flush_log_at_trx_commit = 2
    innodb_flush_method = O_DIRECT

    # Query cache (if using MySQL < 8.0)
    query_cache_type = 1
    query_cache_size = 128M
    query_cache_limit = 2M

    # Slow query log
    slow_query_log = 1
    slow_query_log_file = /var/log/mysql/slow.log
    long_query_time = 1
EOF

    log_success "Database optimization configuration deployed"
}

# Deploy cost tracking components
deploy_cost_tracking() {
    log_info "Deploying cost tracking components..."

    # Create ServiceMonitor for cost metrics
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-cost-metrics
  namespace: $MONITORING_NAMESPACE
  labels:
    app: cost-tracking
spec:
  selector:
    matchLabels:
      app: kube-state-metrics
  endpoints:
  - port: http-metrics
    interval: 30s
    path: /metrics
    honorLabels: true
  namespaceSelector:
    matchNames:
    - $MONITORING_NAMESPACE
EOF

    # Deploy cost calculation script as CronJob
    cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-cost-calculator
  namespace: $MONITORING_NAMESPACE
spec:
  schedule: "0 */6 * * *"  # Every 6 hours
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: cost-calculator
            image: bitnami/kubectl:latest
            command:
            - /bin/bash
            - -c
            - |
              # Calculate costs for all tenant namespaces
              for ns in \$(kubectl get namespaces -l tenant -o name | cut -d/ -f2); do
                cpu_requests=\$(kubectl get pods -n \$ns -o jsonpath='{.items[*].spec.containers[*].resources.requests.cpu}' | tr ' ' '\n' | sed 's/m//' | awk '{sum+=\$1} END {print sum/1000}')
                memory_requests=\$(kubectl get pods -n \$ns -o jsonpath='{.items[*].spec.containers[*].resources.requests.memory}' | tr ' ' '\n' | sed 's/Mi//' | awk '{sum+=\$1} END {print sum/1024}')

                # Calculate hourly cost (example rates)
                cpu_cost=\$(echo "\$cpu_requests * 0.0464" | bc -l)
                memory_cost=\$(echo "\$memory_requests * 0.00506" | bc -l)
                total_cost=\$(echo "\$cpu_cost + \$memory_cost" | bc -l)

                echo "Tenant: \$ns, CPU Cost: \$cpu_cost, Memory Cost: \$memory_cost, Total: \$total_cost"

                # Store metrics (this would typically push to a metrics endpoint)
                kubectl annotate namespace \$ns cost.architrave.com/hourly="\$total_cost" --overwrite
              done
          restartPolicy: OnFailure
EOF

    log_success "Cost tracking components deployed"
}

# Apply tenant-specific optimizations
apply_tenant_optimizations() {
    local tenant_id="$1"
    local tier="${2:-standard}"

    if [ -z "$tenant_id" ]; then
        log_warning "No tenant ID provided, skipping tenant-specific optimizations"
        return
    fi

    log_info "Applying optimizations for tenant: $tenant_id (tier: $tier)"

    local tenant_namespace="${TENANT_NAMESPACE_PREFIX}${tenant_id}"

    # Check if tenant namespace exists
    if ! kubectl get namespace "$tenant_namespace" &> /dev/null; then
        log_warning "Tenant namespace $tenant_namespace does not exist"
        return
    fi

    # Set tier-specific configurations
    case "$tier" in
        "basic")
            MIN_REPLICAS=1
            MAX_REPLICAS=3
            CPU_TARGET=80
            MEMORY_TARGET=80
            VPA_MIN_CPU="50m"
            VPA_MAX_CPU="200m"
            VPA_MIN_MEMORY="64Mi"
            VPA_MAX_MEMORY="256Mi"
            ;;
        "standard")
            MIN_REPLICAS=2
            MAX_REPLICAS=5
            CPU_TARGET=70
            MEMORY_TARGET=75
            VPA_MIN_CPU="100m"
            VPA_MAX_CPU="500m"
            VPA_MIN_MEMORY="128Mi"
            VPA_MAX_MEMORY="512Mi"
            ;;
        "premium")
            MIN_REPLICAS=3
            MAX_REPLICAS=10
            CPU_TARGET=60
            MEMORY_TARGET=70
            VPA_MIN_CPU="200m"
            VPA_MAX_CPU="1000m"
            VPA_MIN_MEMORY="256Mi"
            VPA_MAX_MEMORY="1Gi"
            ;;
        *)
            log_warning "Unknown tier: $tier, using standard configuration"
            MIN_REPLICAS=2
            MAX_REPLICAS=5
            CPU_TARGET=70
            MEMORY_TARGET=75
            VPA_MIN_CPU="100m"
            VPA_MAX_CPU="500m"
            VPA_MIN_MEMORY="128Mi"
            VPA_MAX_MEMORY="512Mi"
            ;;
    esac

    # Apply enhanced autoscaling configuration
    if [ -f "$PROJECT_ROOT/kubernetes/autoscaling/enhanced-tenant-autoscaling.yaml" ]; then
        log_info "Applying enhanced autoscaling for $tenant_id..."

        # Substitute variables and apply
        envsubst <<EOF | kubectl apply -f -
$(cat "$PROJECT_ROOT/kubernetes/autoscaling/enhanced-tenant-autoscaling.yaml" | \
  sed "s/\${TENANT_NAMESPACE}/$tenant_namespace/g" | \
  sed "s/\${TENANT_ID}/$tenant_id/g" | \
  sed "s/\${TENANT_TIER}/$tier/g" | \
  sed "s/\${MIN_REPLICAS}/$MIN_REPLICAS/g" | \
  sed "s/\${MAX_REPLICAS}/$MAX_REPLICAS/g" | \
  sed "s/\${CPU_TARGET_UTILIZATION}/$CPU_TARGET/g" | \
  sed "s/\${MEMORY_TARGET_UTILIZATION}/$MEMORY_TARGET/g" | \
  sed "s/\${VPA_MIN_CPU}/$VPA_MIN_CPU/g" | \
  sed "s/\${VPA_MAX_CPU}/$VPA_MAX_CPU/g" | \
  sed "s/\${VPA_MIN_MEMORY}/$VPA_MIN_MEMORY/g" | \
  sed "s/\${VPA_MAX_MEMORY}/$VPA_MAX_MEMORY/g")
EOF

        log_success "Enhanced autoscaling applied for $tenant_id"
    fi

    # Label namespace for monitoring
    kubectl label namespace "$tenant_namespace" \
        tenant="$tenant_id" \
        tier="$tier" \
        monitoring="enabled" \
        --overwrite

    log_success "Optimizations applied for tenant: $tenant_id"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."

    # Check KEDA
    if kubectl get pods -n keda-system -l app.kubernetes.io/name=keda-operator | grep -q Running; then
        log_success "KEDA is running"
    else
        log_warning "KEDA may not be running properly"
    fi

    # Check VPA
    if kubectl get pods -n kube-system -l app=vpa-recommender | grep -q Running; then
        log_success "VPA is running"
    else
        log_warning "VPA may not be running properly"
    fi

    # Check Prometheus rules
    if kubectl get prometheusrules -n $MONITORING_NAMESPACE enhanced-tenant-alerts &> /dev/null; then
        log_success "Enhanced alerting rules are deployed"
    else
        log_warning "Enhanced alerting rules may not be deployed"
    fi

    # Check Grafana dashboards
    dashboard_count=$(kubectl get configmaps -n $MONITORING_NAMESPACE -l grafana_dashboard=1 --no-headers | wc -l)
    if [ "$dashboard_count" -gt 0 ]; then
        log_success "Grafana dashboards deployed: $dashboard_count"
    else
        log_warning "No Grafana dashboards found"
    fi

    log_success "Deployment verification completed"
}

# Main execution
main() {
    local tenant_id=""
    local tier="standard"
    local skip_prerequisites=false

    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tenant-id)
                tenant_id="$2"
                shift 2
                ;;
            --tier)
                tier="$2"
                shift 2
                ;;
            --skip-prerequisites)
                skip_prerequisites=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --tenant-id ID        Apply optimizations to specific tenant"
                echo "  --tier TIER          Tenant tier (basic|standard|premium)"
                echo "  --skip-prerequisites Skip prerequisite checks"
                echo "  -h, --help           Show this help message"
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done

    log_info "Starting performance optimization deployment..."

    if [ "$skip_prerequisites" = false ]; then
        check_prerequisites
    fi

    deploy_enhanced_autoscaling
    deploy_performance_monitoring
    deploy_database_optimization
    deploy_cost_tracking

    if [ -n "$tenant_id" ]; then
        apply_tenant_optimizations "$tenant_id" "$tier"
    fi

    verify_deployment

    log_success "Performance optimization deployment completed!"
    log_info "Next steps:"
    log_info "1. Access Grafana dashboards for performance monitoring"
    log_info "2. Configure alerting channels in AlertManager"
    log_info "3. Review and adjust autoscaling parameters based on usage patterns"
    log_info "4. Monitor cost tracking metrics and set up budget alerts"
    log_info "5. Deploy web-based tenant management interface with: ./deploy-tenant-ui.sh"
    log_info "6. Configure log aggregation with tenant isolation"
}

# Run main function
main "$@"
