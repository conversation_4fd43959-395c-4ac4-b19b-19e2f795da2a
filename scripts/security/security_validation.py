#!/usr/bin/env python3
"""
Comprehensive Security Validation Framework
This script validates security configurations across the entire infrastructure
"""

import os
import re
import json
import subprocess
import logging
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

class SecurityLevel(Enum):
    CRITICAL = "CRITICAL"
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"
    INFO = "INFO"

@dataclass
class SecurityFinding:
    category: str
    title: str
    description: str
    severity: SecurityLevel
    file_path: str
    line_number: Optional[int] = None
    recommendation: str = ""
    cve_id: Optional[str] = None

class SecurityValidator:
    def __init__(self):
        self.findings: List[SecurityFinding] = []
        self.base_path = os.getcwd()
    
    def add_finding(self, finding: SecurityFinding):
        """Add a security finding to the results"""
        self.findings.append(finding)
        logger.warning(f"{finding.severity.value}: {finding.title} in {finding.file_path}")
    
    def validate_hardcoded_secrets(self) -> None:
        """Scan for hardcoded secrets and credentials"""
        logger.info("Scanning for hardcoded secrets...")
        
        secret_patterns = [
            (r'password\s*=\s*["\'][^"\']+["\']', "Hardcoded password detected"),
            (r'secret\s*=\s*["\'][^"\']+["\']', "Hardcoded secret detected"),
            (r'api_key\s*=\s*["\'][^"\']+["\']', "Hardcoded API key detected"),
            (r'aws_access_key_id\s*=\s*["\'][^"\']+["\']', "Hardcoded AWS access key detected"),
            (r'aws_secret_access_key\s*=\s*["\'][^"\']+["\']', "Hardcoded AWS secret key detected"),
            (r'["\'][A-Za-z0-9+/]{40,}["\']', "Potential base64 encoded secret"),
            (r'["\'][A-Za-z0-9]{32,}["\']', "Potential API key or token"),
        ]
        
        for root, dirs, files in os.walk(self.base_path):
            # Skip .git and other hidden directories
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.endswith(('.py', '.tf', '.yaml', '.yml', '.json', '.sh')):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for line_num, line in enumerate(lines, 1):
                                for pattern, description in secret_patterns:
                                    if re.search(pattern, line, re.IGNORECASE):
                                        self.add_finding(SecurityFinding(
                                            category="Secrets Management",
                                            title="Hardcoded Secret Detected",
                                            description=f"{description}: {line.strip()}",
                                            severity=SecurityLevel.CRITICAL,
                                            file_path=file_path,
                                            line_number=line_num,
                                            recommendation="Move secrets to AWS Secrets Manager or environment variables"
                                        ))
                    except Exception as e:
                        logger.error(f"Error reading file {file_path}: {e}")
    
    def validate_sql_injection_risks(self) -> None:
        """Scan for potential SQL injection vulnerabilities"""
        logger.info("Scanning for SQL injection risks...")
        
        sql_injection_patterns = [
            (r'f["\'].*{.*}.*["\'].*mysql|postgres|sql', "F-string SQL query detected"),
            (r'["\'].*%s.*["\'].*execute|query', "String formatting in SQL query"),
            (r'\.format\(.*\).*execute|query', "String format in SQL query"),
            (r'\+.*["\'].*execute|query', "String concatenation in SQL query"),
        ]
        
        for root, dirs, files in os.walk(self.base_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for line_num, line in enumerate(lines, 1):
                                for pattern, description in sql_injection_patterns:
                                    if re.search(pattern, line, re.IGNORECASE):
                                        self.add_finding(SecurityFinding(
                                            category="Input Validation",
                                            title="Potential SQL Injection Risk",
                                            description=f"{description}: {line.strip()}",
                                            severity=SecurityLevel.CRITICAL,
                                            file_path=file_path,
                                            line_number=line_num,
                                            recommendation="Use parameterized queries or ORM methods"
                                        ))
                    except Exception as e:
                        logger.error(f"Error reading file {file_path}: {e}")
    
    def validate_container_security(self) -> None:
        """Validate container security configurations"""
        logger.info("Validating container security...")
        
        for root, dirs, files in os.walk(self.base_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.startswith('Dockerfile'):
                    file_path = os.path.join(root, file)
                    self._validate_dockerfile(file_path)
                elif file.endswith(('.yaml', '.yml')):
                    file_path = os.path.join(root, file)
                    self._validate_kubernetes_security(file_path)
    
    def _validate_dockerfile(self, file_path: str) -> None:
        """Validate Dockerfile security"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                has_user_directive = False
                runs_as_root = True
                
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    
                    # Check for USER directive
                    if line.startswith('USER '):
                        has_user_directive = True
                        user = line.split()[1]
                        if user != 'root' and user != '0':
                            runs_as_root = False
                    
                    # Check for dangerous patterns
                    if 'curl | sh' in line or 'wget | sh' in line:
                        self.add_finding(SecurityFinding(
                            category="Container Security",
                            title="Dangerous Pipe to Shell",
                            description=f"Piping download to shell: {line}",
                            severity=SecurityLevel.HIGH,
                            file_path=file_path,
                            line_number=line_num,
                            recommendation="Download and verify files before execution"
                        ))
                
                if not has_user_directive or runs_as_root:
                    self.add_finding(SecurityFinding(
                        category="Container Security",
                        title="Container Running as Root",
                        description="Container does not specify non-root user",
                        severity=SecurityLevel.HIGH,
                        file_path=file_path,
                        recommendation="Add USER directive with non-root user"
                    ))
        except Exception as e:
            logger.error(f"Error validating Dockerfile {file_path}: {e}")
    
    def _validate_kubernetes_security(self, file_path: str) -> None:
        """Validate Kubernetes security configurations"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # Check for missing security contexts
                if 'kind: Deployment' in content or 'kind: Pod' in content:
                    if 'securityContext:' not in content:
                        self.add_finding(SecurityFinding(
                            category="Kubernetes Security",
                            title="Missing Security Context",
                            description="Deployment/Pod missing security context",
                            severity=SecurityLevel.HIGH,
                            file_path=file_path,
                            recommendation="Add securityContext with runAsNonRoot: true"
                        ))
                    
                    if 'runAsNonRoot: true' not in content:
                        self.add_finding(SecurityFinding(
                            category="Kubernetes Security",
                            title="Missing runAsNonRoot",
                            description="Container may run as root",
                            severity=SecurityLevel.MEDIUM,
                            file_path=file_path,
                            recommendation="Set runAsNonRoot: true in securityContext"
                        ))
        except Exception as e:
            logger.error(f"Error validating Kubernetes file {file_path}: {e}")
    
    def validate_terraform_security(self) -> None:
        """Validate Terraform security configurations"""
        logger.info("Validating Terraform security...")
        
        for root, dirs, files in os.walk(self.base_path):
            dirs[:] = [d for d in dirs if not d.startswith('.')]
            
            for file in files:
                if file.endswith('.tf'):
                    file_path = os.path.join(root, file)
                    self._validate_terraform_file(file_path)
    
    def _validate_terraform_file(self, file_path: str) -> None:
        """Validate individual Terraform file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    
                    # Check for overly permissive security groups
                    if 'cidr_blocks = ["0.0.0.0/0"]' in line:
                        self.add_finding(SecurityFinding(
                            category="Network Security",
                            title="Overly Permissive Security Group",
                            description="Security group allows access from anywhere",
                            severity=SecurityLevel.HIGH,
                            file_path=file_path,
                            line_number=line_num,
                            recommendation="Restrict CIDR blocks to specific IP ranges"
                        ))
                    
                    # Check for disabled encryption
                    if 'encrypted = false' in line:
                        self.add_finding(SecurityFinding(
                            category="Encryption",
                            title="Encryption Disabled",
                            description="Resource has encryption disabled",
                            severity=SecurityLevel.HIGH,
                            file_path=file_path,
                            line_number=line_num,
                            recommendation="Enable encryption for all resources"
                        ))
        except Exception as e:
            logger.error(f"Error validating Terraform file {file_path}: {e}")
    
    def run_external_security_tools(self) -> None:
        """Run external security scanning tools"""
        logger.info("Running external security tools...")
        
        # Run tfsec if available
        try:
            result = subprocess.run(['tfsec', '--format', 'json', '.'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                self._parse_tfsec_results(result.stdout)
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("tfsec not available or timed out")
        
        # Run checkov if available
        try:
            result = subprocess.run(['checkov', '-d', '.', '--output', 'json'], 
                                  capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                self._parse_checkov_results(result.stdout)
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("checkov not available or timed out")
    
    def _parse_tfsec_results(self, output: str) -> None:
        """Parse tfsec JSON output"""
        try:
            data = json.loads(output)
            for result in data.get('results', []):
                self.add_finding(SecurityFinding(
                    category="Infrastructure Security",
                    title=result.get('rule_description', 'TFSec Finding'),
                    description=result.get('description', ''),
                    severity=SecurityLevel.HIGH,
                    file_path=result.get('location', {}).get('filename', ''),
                    line_number=result.get('location', {}).get('start_line'),
                    recommendation=result.get('resolution', '')
                ))
        except json.JSONDecodeError:
            logger.error("Failed to parse tfsec output")
    
    def _parse_checkov_results(self, output: str) -> None:
        """Parse checkov JSON output"""
        try:
            data = json.loads(output)
            for result in data.get('results', {}).get('failed_checks', []):
                self.add_finding(SecurityFinding(
                    category="Infrastructure Security",
                    title=result.get('check_name', 'Checkov Finding'),
                    description=result.get('description', ''),
                    severity=SecurityLevel.MEDIUM,
                    file_path=result.get('file_path', ''),
                    line_number=result.get('file_line_range', [0])[0],
                    recommendation=result.get('guideline', '')
                ))
        except json.JSONDecodeError:
            logger.error("Failed to parse checkov output")
    
    def generate_report(self) -> Dict:
        """Generate comprehensive security report"""
        logger.info("Generating security report...")
        
        # Categorize findings by severity
        severity_counts = {level: 0 for level in SecurityLevel}
        category_counts = {}
        
        for finding in self.findings:
            severity_counts[finding.severity] += 1
            category_counts[finding.category] = category_counts.get(finding.category, 0) + 1
        
        report = {
            "summary": {
                "total_findings": len(self.findings),
                "severity_breakdown": {level.value: count for level, count in severity_counts.items()},
                "category_breakdown": category_counts
            },
            "findings": [
                {
                    "category": f.category,
                    "title": f.title,
                    "description": f.description,
                    "severity": f.severity.value,
                    "file_path": f.file_path,
                    "line_number": f.line_number,
                    "recommendation": f.recommendation,
                    "cve_id": f.cve_id
                }
                for f in self.findings
            ]
        }
        
        return report
    
    def run_full_validation(self) -> Dict:
        """Run complete security validation"""
        logger.info("Starting comprehensive security validation...")
        
        self.validate_hardcoded_secrets()
        self.validate_sql_injection_risks()
        self.validate_container_security()
        self.validate_terraform_security()
        self.run_external_security_tools()
        
        report = self.generate_report()
        
        logger.info(f"Security validation completed. Found {len(self.findings)} issues.")
        logger.info(f"Critical: {report['summary']['severity_breakdown']['CRITICAL']}")
        logger.info(f"High: {report['summary']['severity_breakdown']['HIGH']}")
        logger.info(f"Medium: {report['summary']['severity_breakdown']['MEDIUM']}")
        
        return report

def main():
    """Main execution function"""
    validator = SecurityValidator()
    report = validator.run_full_validation()
    
    # Save report to file
    with open('security_validation_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info("Security report saved to security_validation_report.json")
    
    # Exit with error code if critical issues found
    critical_count = report['summary']['severity_breakdown']['CRITICAL']
    if critical_count > 0:
        logger.error(f"Found {critical_count} critical security issues!")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
