#!/usr/bin/env python3
"""
Security Fixes Testing Script
Tests all implemented security fixes to ensure they work correctly
"""

import os
import sys
import subprocess
import json
import logging
from typing import Dict, List, Tuple
import tempfile

# Add the tenant-management directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'tenant-management'))

try:
    from security.validators import validate_tenant_id, validate_database_name, ValidationError
    from security.credentials import get_rds_credentials
except ImportError as e:
    print(f"❌ Failed to import security modules: {e}")
    print("Please ensure you're running from the infra-provisioning root directory")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

class SecurityTestRunner:
    """Test runner for security fixes"""
    
    def __init__(self):
        self.test_results = {}
        self.passed_tests = 0
        self.failed_tests = 0
    
    def run_test(self, test_name: str, test_func) -> bool:
        """Run a single test and record results"""
        try:
            logger.info(f"Running test: {test_name}")
            result = test_func()
            if result:
                logger.info(f"✅ PASSED: {test_name}")
                self.test_results[test_name] = {"status": "PASSED", "message": "Test completed successfully"}
                self.passed_tests += 1
                return True
            else:
                logger.error(f"❌ FAILED: {test_name}")
                self.test_results[test_name] = {"status": "FAILED", "message": "Test failed"}
                self.failed_tests += 1
                return False
        except Exception as e:
            logger.error(f"❌ ERROR in {test_name}: {e}")
            self.test_results[test_name] = {"status": "ERROR", "message": str(e)}
            self.failed_tests += 1
            return False
    
    def test_input_validation(self) -> bool:
        """Test input validation functions"""
        logger.info("Testing input validation...")
        
        # Test valid inputs
        try:
            valid_tenant_id = validate_tenant_id("test-tenant-123")
            assert valid_tenant_id == "test-tenant-123"
            
            valid_db_name = validate_database_name("test_database_123")
            assert valid_db_name == "test_database_123"
            
            logger.info("✅ Valid input validation passed")
        except Exception as e:
            logger.error(f"❌ Valid input validation failed: {e}")
            return False
        
        # Test invalid inputs
        invalid_inputs = [
            ("tenant-id with spaces", "tenant id with spaces"),
            ("tenant-id-too-long", "a" * 60),
            ("tenant-id-special-chars", "tenant@#$%"),
            ("sql-injection", "'; DROP TABLE users; --"),
            ("command-injection", "tenant; rm -rf /"),
            ("xss-attempt", "<script>alert('xss')</script>")
        ]
        
        for test_name, invalid_input in invalid_inputs:
            try:
                validate_tenant_id(invalid_input)
                logger.error(f"❌ {test_name}: Should have failed but didn't")
                return False
            except ValidationError:
                logger.info(f"✅ {test_name}: Correctly rejected invalid input")
            except Exception as e:
                logger.error(f"❌ {test_name}: Unexpected error: {e}")
                return False
        
        return True
    
    def test_secrets_manager_integration(self) -> bool:
        """Test AWS Secrets Manager integration"""
        logger.info("Testing AWS Secrets Manager integration...")
        
        try:
            # Test if we can connect to AWS Secrets Manager
            import boto3
            client = boto3.client('secretsmanager', region_name='eu-central-1')
            
            # Try to get the production RDS credentials
            try:
                credentials = get_rds_credentials("production/rds/credentials")
                if credentials and 'host' in credentials:
                    logger.info("✅ Successfully retrieved credentials from Secrets Manager")
                    return True
                else:
                    logger.warning("⚠️ Credentials retrieved but missing required fields")
                    return False
            except Exception as e:
                logger.warning(f"⚠️ Could not retrieve production credentials: {e}")
                # This might be expected in test environments
                return True
                
        except ImportError:
            logger.error("❌ boto3 not installed")
            return False
        except Exception as e:
            logger.error(f"❌ AWS Secrets Manager test failed: {e}")
            return False
    
    def test_kms_encryption_enabled(self) -> bool:
        """Test if KMS encryption is enabled in Terraform"""
        logger.info("Testing KMS encryption configuration...")
        
        kms_file = "modules/kms/main.tf"
        if not os.path.exists(kms_file):
            logger.error("❌ KMS module file not found")
            return False
        
        try:
            with open(kms_file, 'r') as f:
                content = f.read()
            
            # Check if KMS key resource is uncommented
            if 'resource "aws_kms_key" "primary"' in content:
                logger.info("✅ KMS encryption is enabled")
                
                # Check for lifecycle management
                if 'prevent_destroy = true' in content:
                    logger.info("✅ KMS lifecycle protection enabled")
                else:
                    logger.warning("⚠️ KMS lifecycle protection not found")
                
                return True
            else:
                logger.error("❌ KMS encryption is not enabled")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error reading KMS configuration: {e}")
            return False
    
    def test_container_security_templates(self) -> bool:
        """Test if container security templates exist"""
        logger.info("Testing container security templates...")
        
        security_template_dir = "kubernetes/security-templates"
        if not os.path.exists(security_template_dir):
            logger.warning("⚠️ Security templates directory not found")
            return False
        
        required_files = [
            "security-context.yaml",
            "default-deny-policy.yaml"
        ]
        
        for file_name in required_files:
            file_path = os.path.join(security_template_dir, file_name)
            if os.path.exists(file_path):
                logger.info(f"✅ Found security template: {file_name}")
            else:
                logger.warning(f"⚠️ Missing security template: {file_name}")
                return False
        
        return True
    
    def test_network_policies_deployed(self) -> bool:
        """Test if network policies are deployed"""
        logger.info("Testing network policies deployment...")
        
        try:
            # Check if kubectl is available
            result = subprocess.run(['kubectl', 'version', '--client'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.warning("⚠️ kubectl not available, skipping network policy test")
                return True
            
            # Check for default deny policy
            result = subprocess.run(['kubectl', 'get', 'networkpolicy', 'default-deny-all', '-n', 'default'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ Default deny network policy is deployed")
                return True
            else:
                logger.warning("⚠️ Default deny network policy not found")
                return False
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ kubectl command timed out")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Network policy test failed: {e}")
            return False
    
    def test_security_monitoring_deployed(self) -> bool:
        """Test if security monitoring is deployed"""
        logger.info("Testing security monitoring deployment...")
        
        try:
            # Check if security monitoring namespace exists
            result = subprocess.run(['kubectl', 'get', 'namespace', 'security-monitoring'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("✅ Security monitoring namespace exists")
                
                # Check for security monitoring pods
                result = subprocess.run(['kubectl', 'get', 'pods', '-n', 'security-monitoring'], 
                                      capture_output=True, text=True, timeout=10)
                if result.returncode == 0 and result.stdout.strip():
                    logger.info("✅ Security monitoring pods are deployed")
                    return True
                else:
                    logger.warning("⚠️ No security monitoring pods found")
                    return False
            else:
                logger.warning("⚠️ Security monitoring namespace not found")
                return False
                
        except subprocess.TimeoutExpired:
            logger.warning("⚠️ kubectl command timed out")
            return False
        except Exception as e:
            logger.warning(f"⚠️ Security monitoring test failed: {e}")
            return False
    
    def test_hardcoded_credentials_removed(self) -> bool:
        """Test that hardcoded credentials have been removed"""
        logger.info("Testing for hardcoded credentials...")
        
        # Patterns to search for
        credential_patterns = [
            r'password\s*=\s*["\'][^"\']+["\']',
            r'&BZzY_<AK\(=a\*UhZ',  # The specific hardcoded password
            r'aws_access_key_id\s*=\s*["\'][^"\']+["\']',
            r'aws_secret_access_key\s*=\s*["\'][^"\']+["\']'
        ]
        
        # Files to check
        files_to_check = [
            'tenant-management/scripts/advanced_tenant_onboard.py',
            'tenant-management/scripts/advanced_tenant_offboard.py'
        ]
        
        found_credentials = False
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()
                    
                    # Check for the specific hardcoded password
                    if '&BZzY_<AK(=a*UhZ' in content:
                        logger.error(f"❌ Found hardcoded password in {file_path}")
                        found_credentials = True
                    
                    # Check for other credential patterns
                    import re
                    for pattern in credential_patterns:
                        if re.search(pattern, content, re.IGNORECASE):
                            logger.warning(f"⚠️ Potential credential pattern found in {file_path}")
                
                except Exception as e:
                    logger.error(f"❌ Error reading {file_path}: {e}")
                    return False
        
        if not found_credentials:
            logger.info("✅ No hardcoded credentials found")
            return True
        else:
            logger.error("❌ Hardcoded credentials still present")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all security tests"""
        logger.info("🔒 Starting comprehensive security testing...")
        
        tests = [
            ("Input Validation", self.test_input_validation),
            ("Secrets Manager Integration", self.test_secrets_manager_integration),
            ("KMS Encryption Enabled", self.test_kms_encryption_enabled),
            ("Container Security Templates", self.test_container_security_templates),
            ("Network Policies Deployed", self.test_network_policies_deployed),
            ("Security Monitoring Deployed", self.test_security_monitoring_deployed),
            ("Hardcoded Credentials Removed", self.test_hardcoded_credentials_removed)
        ]
        
        for test_name, test_func in tests:
            self.run_test(test_name, test_func)
        
        # Generate summary
        total_tests = len(tests)
        success_rate = (self.passed_tests / total_tests) * 100
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": self.passed_tests,
            "failed_tests": self.failed_tests,
            "success_rate": success_rate,
            "test_results": self.test_results
        }
        
        logger.info(f"🎯 Testing completed: {self.passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        
        return summary

def main():
    """Main execution function"""
    print("🔒 Security Fixes Testing Suite")
    print("=" * 50)
    
    # Check if running from correct directory
    if not os.path.exists("tenant-management/scripts/advanced_tenant_onboard.py"):
        print("❌ Please run this script from the infra-provisioning root directory")
        sys.exit(1)
    
    # Run tests
    test_runner = SecurityTestRunner()
    results = test_runner.run_all_tests()
    
    # Save results to file
    with open('security_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print("\n" + "=" * 50)
    print("📊 SECURITY TEST SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['passed_tests']}")
    print(f"Failed: {results['failed_tests']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Results saved to: security_test_results.json")
    
    if results['success_rate'] >= 80:
        print("\n✅ Security fixes are working well!")
        return 0
    else:
        print("\n⚠️ Some security tests failed. Please review the results.")
        return 1

if __name__ == "__main__":
    exit(main())
