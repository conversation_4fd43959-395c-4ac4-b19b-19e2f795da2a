= Quick Test Commands - Tenant Onboarding System
:toc: left
:toclevels: 2
:sectnums:
:icons: font
:source-highlighter: rouge

== Overview

This is a quick reference guide with the most essential commands to verify the tenant onboarding system is working correctly.

== Quick Health Check Commands

=== 1. Pod Status Check

[source,bash]
----
# Check all pods status
kubectl get pods -n tenant-production-test

# Expected: All pods should be Running with correct READY count
# Frontend: 1/1, Backend: 2/2, RabbitMQ: 2/2
----

=== 2. Frontend Health Check

[source,bash]
----
# Test frontend health endpoint
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/health

# Expected output: OK
----

=== 3. Backend Health Check

[source,bash]
----
# Check PHP-FPM is running
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- pgrep php-fpm

# Expected: Process IDs should be returned
----

=== 4. Database Connectivity Test

[source,bash]
----
# Test database connection
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
try {
    \$pdo = new PDO('mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'], \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD']);
    echo 'Database connection: SUCCESS\n';
} catch (Exception \$e) {
    echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n';
}"

# Expected output: Database connection: SUCCESS
----

=== 5. RabbitMQ Health Check

[source,bash]
----
# Test RabbitMQ ping
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl ping

# Expected output: Ping succeeded
----

=== 6. S3 Storage Test

[source,bash]
----
# Test S3 storage write permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
echo 'test' > /storage/ArchAssets/data/uploads/test-\$(date +%s).txt && echo 'S3 write: SUCCESS' || echo 'S3 write: FAILED'
"

# Expected output: S3 write: SUCCESS
----

== Architrave CLI Test Commands

=== 1. CLI Environment Setup

[source,bash]
----
# Set environment variables and test CLI
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
echo 'Environment variables set:'
env | grep MYSQL
"
----

=== 2. CLI Command Discovery

[source,bash]
----
# Discover process-new commands
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
echo 'Discovering process-new commands:'
/storage/ArchAssets/bin/architrave | grep process-new 2>/dev/null || echo 'Database connection required for CLI'
"
----

=== 3. CLI Command Execution

[source,bash]
----
# Execute document processing command
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
echo 'Executing document processing command:'
sudo -u www-data /storage/ArchAssets/bin/architrave cron:system:process-new-documents
"
----

== Troubleshooting Commands

=== 1. Check Pod Logs

[source,bash]
----
# Frontend logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-frontend --tail=20

# Backend logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend --tail=20

# RabbitMQ logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq --tail=20

# Backend init container logs (if backend is stuck in init)
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend -c init-schema --tail=50
----

=== 2. Check Events

[source,bash]
----
# Check namespace events
kubectl get events -n tenant-production-test --sort-by='.lastTimestamp'

# Check pod descriptions for issues
kubectl describe pod -n tenant-production-test -l component=backend
----

=== 3. Database Connection Debug

[source,bash]
----
# Check database environment variables
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- env | grep -E "(DB_|MYSQL_)"

# Test SSL database connection
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
try {
    \$dsn = 'mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'].';charset=utf8mb4';
    \$options = [
        PDO::MYSQL_ATTR_SSL_CA => '/etc/ssl/certs/ca-certificates.crt',
        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ];
    \$pdo = new PDO(\$dsn, \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD'], \$options);
    echo 'Database SSL connection: SUCCESS\n';
} catch (Exception \$e) {
    echo 'Database SSL connection: FAILED - ' . \$e->getMessage() . '\n';
}"
----

== Expected Results

=== Success Indicators

[cols="2,3,1"]
|===
|Test |Expected Result |Status

|Pod Status |All pods Running (Frontend: 1/1, Backend: 2/2, RabbitMQ: 2/2) |✅/⏳
|Frontend Health |Returns "OK" |✅
|Backend Health |PHP-FPM process IDs returned |✅/⏳
|Database Connection |"Database connection: SUCCESS" |⏳
|RabbitMQ Health |"Ping succeeded" |✅
|S3 Storage |"S3 write: SUCCESS" |✅
|Architrave CLI |Commands discoverable and executable |⏳
|===

=== Common Issues

[cols="2,3"]
|===
|Issue |Quick Fix

|Backend stuck in Init |Check database connectivity and SSL configuration
|"require_secure_transport=ON" error |Database requires SSL - this is expected and correct
|"using password: NO" error |Set MYSQL_* environment variables correctly
|S3 write permission denied |Check www-data (uid=33, gid=33) permissions
|RabbitMQ connection failed |Check RabbitMQ pod logs and restart if needed
|===

== Quick Restart Commands

[source,bash]
----
# Restart all components
kubectl rollout restart deployment/tenant-production-test-frontend -n tenant-production-test
kubectl rollout restart deployment/tenant-production-test-backend -n tenant-production-test
kubectl rollout restart deployment/tenant-production-test-rabbitmq -n tenant-production-test

# Check restart status
kubectl get pods -n tenant-production-test -w
----

== One-Line System Check

[source,bash]
----
# Complete system health check in one command
kubectl get pods -n tenant-production-test && \
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/health && \
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl ping && \
echo "System check completed"
----

This quick reference provides the essential commands to verify your tenant onboarding system is working correctly. For detailed testing, refer to the comprehensive testing guide.
