= Tenant Onboarding System Testing Guide
:toc: left
:toclevels: 3
:sectnums:
:icons: font
:source-highlighter: rouge

== Overview

This document provides comprehensive testing commands to verify the tenant onboarding system functionality, including SSL configuration, database connectivity, Architrave CLI commands, and all system components.

== Prerequisites

* kubectl configured with access to the Kubernetes cluster
* Tenant `production-test` deployed
* AWS credentials configured for Aurora Serverless access

== 1. Infrastructure Component Testing

=== 1.1 Pod Status Verification

[source,bash]
----
# Check all pods in tenant namespace
kubectl get pods -n tenant-production-test

# Expected output:
# NAME                                               READY   STATUS    RESTARTS   AGE
# tenant-production-test-backend-xxx                 2/2     Running   0          xxm
# tenant-production-test-frontend-xxx                1/1     Running   0          xxm
# tenant-production-test-rabbitmq-xxx                2/2     Running   0          xxm

# Check pod details with wide output
kubectl get pods -n tenant-production-test -o wide

# Check pod events for troubleshooting
kubectl get events -n tenant-production-test --sort-by='.lastTimestamp'
----

=== 1.2 Service and Endpoint Verification

[source,bash]
----
# Check services
kubectl get services -n tenant-production-test

# Check endpoints
kubectl get endpoints -n tenant-production-test

# Check service details
kubectl describe service tenant-production-test-frontend -n tenant-production-test
kubectl describe service tenant-production-test-backend -n tenant-production-test
kubectl describe service tenant-production-test-rabbitmq -n tenant-production-test
----

=== 1.3 ConfigMap and Secret Verification

[source,bash]
----
# Check ConfigMaps
kubectl get configmaps -n tenant-production-test

# Check webapp environment configuration
kubectl describe configmap webapp-env -n tenant-production-test

# Check secrets (without revealing values)
kubectl get secrets -n tenant-production-test

# Verify database credentials exist
kubectl describe secret db-credentials -n tenant-production-test
----

== 2. Frontend Component Testing

=== 2.1 Frontend Health Checks

[source,bash]
----
# Basic health check
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/health

# Expected output: OK

# Extended health check
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/api/health/extended.php

# Expected output: {"status":"healthy","check":{"basic":"y","extended":"y","elastic-search":"n"}}

# Test static file serving
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/static_test.html

# Expected output: HTML content with "Static file test"
----

=== 2.2 Frontend SSL Configuration

[source,bash]
----
# Check SSL certificates
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- ls -la /etc/nginx/ssl/

# Check nginx configuration
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- nginx -T

# Test HTTPS endpoint (if configured)
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -k -s https://localhost/health
----

=== 2.3 Frontend-Backend Communication

[source,bash]
----
# Test backend connectivity from frontend
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://webapp:8080/health.php

# Test API proxy
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/api-health

# Check backend verification logs
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- cat /tmp/backend-verification.log
----

== 3. Backend Component Testing

=== 3.1 Backend Health Checks

[source,bash]
----
# Check PHP-FPM process
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- pgrep -a php-fpm

# Test PHP execution
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -v

# Check PHP extensions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -m | grep -E "(pdo|mysql|json|curl)"

# Test backend health endpoint
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- curl -s http://localhost:9000/health-check.php
----

=== 3.2 Backend Database Connectivity

[source,bash]
----
# Check database environment variables
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- env | grep -E "(DB_|MYSQL_)"

# Test database connection with PHP
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
try {
    \$pdo = new PDO('mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'], \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD']);
    echo 'Database connection: SUCCESS\n';
} catch (Exception \$e) {
    echo 'Database connection: FAILED - ' . \$e->getMessage() . '\n';
}"

# Test database connection with SSL
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
try {
    \$dsn = 'mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'].';charset=utf8mb4';
    \$options = [
        PDO::MYSQL_ATTR_SSL_CA => '/etc/ssl/certs/ca-certificates.crt',
        PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ];
    \$pdo = new PDO(\$dsn, \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD'], \$options);
    echo 'Database SSL connection: SUCCESS\n';
} catch (Exception \$e) {
    echo 'Database SSL connection: FAILED - ' . \$e->getMessage() . '\n';
}"
----

=== 3.3 Backend Storage Testing

[source,bash]
----
# Check S3 storage mount
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- ls -la /storage/ArchAssets/data/uploads/

# Test S3 write permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- touch /storage/ArchAssets/data/uploads/test-file.txt

# Check application cache directory
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- ls -la /storage/ArchAssets/data/cache/

# Test cache write permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- touch /storage/ArchAssets/data/cache/test-cache.txt
----

== 4. RabbitMQ Component Testing

=== 4.1 RabbitMQ Health Checks

[source,bash]
----
# Basic ping test
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl ping

# Expected output: Ping succeeded

# Check RabbitMQ status
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl status

# List users
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl list_users

# Expected output: guest [administrator]
----

=== 4.2 RabbitMQ Management Interface

[source,bash]
----
# Check enabled plugins
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmq-plugins list -e

# Expected: rabbitmq_management, rabbitmq_prometheus, rabbitmq_shovel

# List queues
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl list_queues

# List exchanges
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl list_exchanges
----

=== 4.3 RabbitMQ Connectivity from Backend

[source,bash]
----
# Check RabbitMQ environment variables in backend
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- env | grep RABBITMQ

# Test RabbitMQ connectivity from backend
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
\$host = getenv('RABBITMQ_HOST');
\$port = getenv('RABBITMQ_PORT') ?: '5672';
echo 'Testing connection to ' . \$host . ':' . \$port . '\n';
\$connection = fsockopen(\$host, \$port, \$errno, \$errstr, 5);
if (\$connection) {
    echo 'RabbitMQ connection: SUCCESS\n';
    fclose(\$connection);
} else {
    echo 'RabbitMQ connection: FAILED - ' . \$errstr . '\n';
}"
----

== 5. Database Component Testing

=== 5.1 External Database Connectivity

[source,bash]
----
# Get database credentials
DB_HOST=$(kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.host}' | base64 -d)
DB_USER=$(kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.username}' | base64 -d)
DB_PASS=$(kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.password}' | base64 -d)
DB_NAME=$(kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.database}' | base64 -d)

echo "Database Host: $DB_HOST"
echo "Database User: $DB_USER"
echo "Database Name: $DB_NAME"

# Test connection (requires mysql client)
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS -e "SELECT 1 as test;" $DB_NAME
----

=== 5.2 Database Schema Verification

[source,bash]
----
# Check tables in database
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
\$pdo = new PDO('mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'], \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD']);
\$stmt = \$pdo->query('SHOW TABLES');
\$tables = \$stmt->fetchAll(PDO::FETCH_COLUMN);
echo 'Tables found: ' . count(\$tables) . '\n';
foreach(\$tables as \$table) {
    echo '- ' . \$table . '\n';
}"

# Check specific Architrave tables
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
\$pdo = new PDO('mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'], \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD']);
\$tables = ['users', 'properties', 'documents', 'tenants'];
foreach(\$tables as \$table) {
    try {
        \$stmt = \$pdo->query('SELECT COUNT(*) FROM ' . \$table);
        \$count = \$stmt->fetchColumn();
        echo \$table . ': ' . \$count . ' records\n';
    } catch (Exception \$e) {
        echo \$table . ': Table not found or error\n';
    }
}"
----

== 6. Architrave CLI Testing

=== 6.1 CLI Environment Setup

[source,bash]
----
# Check Architrave CLI exists
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- ls -la /storage/ArchAssets/bin/

# Check CLI permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- ls -la /storage/ArchAssets/bin/architrave

# Set correct environment variables for CLI
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
env | grep MYSQL
"
----

=== 6.2 CLI Command Discovery

[source,bash]
----
# Test basic CLI execution (with proper environment)
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
/storage/ArchAssets/bin/architrave --help 2>/dev/null || echo 'CLI requires database connection'
"

# Discover process-new commands
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
/storage/ArchAssets/bin/architrave | grep process-new 2>/dev/null || echo 'Database connection required'
"

# Discover rent-roll commands
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
/storage/ArchAssets/bin/architrave | grep rent-roll-view 2>/dev/null || echo 'Database connection required'
"

# Discover archclient commands
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
/storage/ArchAssets/bin/architrave | grep archclient 2>/dev/null || echo 'Database connection required'
"
----

=== 6.3 CLI Command Execution

[source,bash]
----
# Execute document processing command
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
sudo -u www-data /storage/ArchAssets/bin/architrave cron:system:process-new-documents
"

# Execute Delphi document processing
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
sudo -u www-data /storage/ArchAssets/bin/architrave arch:delphi:process-new-staged-documents
"

# Execute notification processing
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
export MYSQL_HOST=\$DB_HOST
export MYSQL_USER=\$DB_USER
export MYSQL_PASSWORD=\$DB_PASSWORD
export MYSQL_DATABASE=\$DB_NAME
cd /storage/ArchAssets
sudo -u www-data /storage/ArchAssets/bin/architrave system:process-notifications
"
----

== 7. S3 Storage Testing

=== 7.1 S3 CSI Driver Verification

[source,bash]
----
# Check S3 CSI driver pods
kubectl get pods -n kube-system | grep s3-csi

# Check S3 storage class
kubectl get storageclass | grep s3

# Check PVC status
kubectl get pvc -n tenant-production-test

# Check PV binding
kubectl get pv | grep tenant-production-test
----

=== 7.2 S3 Bucket Access Testing

[source,bash]
----
# Test S3 bucket access from backend
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
echo 'Testing S3 bucket access...'
ls -la /storage/ArchAssets/data/uploads/
echo 'test content' > /storage/ArchAssets/data/uploads/test-\$(date +%s).txt
ls -la /storage/ArchAssets/data/uploads/ | tail -5
"

# Check S3 mount permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
stat /storage/ArchAssets/data/uploads/
id www-data
"

# Test file operations
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- bash -c "
cd /storage/ArchAssets/data/uploads/
sudo -u www-data touch test-www-data.txt
sudo -u www-data ls -la test-www-data.txt
sudo -u www-data rm test-www-data.txt
"
----

== 8. Istio Service Mesh Testing

=== 8.1 Istio Configuration Verification

[source,bash]
----
# Check VirtualService
kubectl get virtualservice -n tenant-production-test

# Check DestinationRule
kubectl get destinationrule -n tenant-production-test

# Check PeerAuthentication
kubectl get peerauthentication -n tenant-production-test

# Check Istio sidecar injection
kubectl get pods -n tenant-production-test -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.spec.containers[*].name}{"\n"}{end}'
----

=== 8.2 Service Mesh Connectivity

[source,bash]
----
# Test service-to-service communication
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://webapp:8080/health.php

# Test RabbitMQ connectivity through service mesh
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- curl -s http://tenant-production-test-rabbitmq:15672/api/overview

# Check Istio proxy logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-frontend -c istio-proxy --tail=20
----

== 9. Monitoring and Metrics

=== 9.1 Prometheus Metrics

[source,bash]
----
# Check if metrics endpoints are available
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- curl -s http://localhost:9090/metrics | head -10

# Check RabbitMQ metrics
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- curl -s http://localhost:15692/metrics | head -10

# Check frontend metrics (if available)
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost:9090/metrics 2>/dev/null | head -10 || echo "Frontend metrics not available"
----

=== 9.2 Resource Usage

[source,bash]
----
# Check resource usage
kubectl top pods -n tenant-production-test

# Check resource limits and requests
kubectl describe pods -n tenant-production-test | grep -A 5 -B 5 "Limits\|Requests"

# Check namespace resource quotas
kubectl describe resourcequota -n tenant-production-test
----

== 10. Security Testing

=== 10.1 Network Policies

[source,bash]
----
# Check network policies
kubectl get networkpolicy -n tenant-production-test

# Test network isolation
kubectl run test-pod --image=busybox --rm -it --restart=Never -- nslookup tenant-production-test-frontend.tenant-production-test.svc.cluster.local

# Test cross-namespace access (should be blocked)
kubectl run test-pod --image=busybox --rm -it --restart=Never -n default -- nslookup tenant-production-test-frontend.tenant-production-test.svc.cluster.local
----

=== 10.2 RBAC and Permissions

[source,bash]
----
# Check service accounts
kubectl get serviceaccounts -n tenant-production-test

# Check role bindings
kubectl get rolebindings -n tenant-production-test

# Check cluster role bindings for tenant
kubectl get clusterrolebindings | grep tenant-production-test
----

== 11. Troubleshooting Commands

=== 11.1 Log Analysis

[source,bash]
----
# Check pod logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-frontend --tail=50
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend --tail=50
kubectl logs -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq --tail=50

# Check init container logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend -c init-schema --tail=50

# Check previous pod logs (if pods restarted)
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend --previous --tail=50
----

=== 11.2 Debug Information

[source,bash]
----
# Get detailed pod information
kubectl describe pod -n tenant-production-test -l component=frontend
kubectl describe pod -n tenant-production-test -l component=backend
kubectl describe pod -n tenant-production-test -l component=rabbitmq

# Check node information
kubectl get nodes -o wide

# Check cluster events
kubectl get events --all-namespaces --sort-by='.lastTimestamp' | tail -20
----

== 12. Performance Testing

=== 12.1 Load Testing

[source,bash]
----
# Simple load test on frontend
kubectl run load-test --image=busybox --rm -it --restart=Never -- sh -c "
for i in \$(seq 1 10); do
  time wget -q -O- http://tenant-production-test-frontend.tenant-production-test.svc.cluster.local/health
  echo \"Request \$i completed\"
done"

# Database connection pool testing
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -- php -r "
for (\$i = 1; \$i <= 5; \$i++) {
    try {
        \$pdo = new PDO('mysql:host='.\$_ENV['DB_HOST'].';port='.\$_ENV['DB_PORT'].';dbname='.\$_ENV['DB_NAME'], \$_ENV['DB_USER'], \$_ENV['DB_PASSWORD']);
        echo 'Connection ' . \$i . ': SUCCESS\n';
        \$pdo = null;
    } catch (Exception \$e) {
        echo 'Connection ' . \$i . ': FAILED\n';
    }
    sleep(1);
}"
----

== 13. Expected Results Summary

=== 13.1 Success Indicators

[cols="1,3,1"]
|===
|Component |Expected Result |Status

|Frontend Pod |1/1 Running |✅
|Backend Pod |2/2 Running |⏳
|RabbitMQ Pod |2/2 Running |✅
|Frontend Health |Returns "OK" |✅
|Database Connection |SSL connection successful |⏳
|RabbitMQ Management |Ping successful, users listed |✅
|S3 Storage |Files writable with www-data permissions |✅
|Istio Configuration |VirtualService, DestinationRule configured |✅
|Architrave CLI |Commands discoverable and executable |⏳
|===

=== 13.2 Common Issues and Solutions

[cols="2,3,3"]
|===
|Issue |Symptom |Solution

|SSL Connection Failed |"require_secure_transport=ON" error |Add SSL parameters to Doctrine config
|Pod Init Failure |Init containers stuck |Check database connectivity and credentials
|Permission Denied |S3 write failures |Verify www-data (uid=33, gid=33) permissions
|Service Unreachable |Connection timeouts |Check network policies and service configuration
|CLI Database Error |"using password: NO" |Set MYSQL_* environment variables correctly
|===

== 14. Maintenance Commands

=== 14.1 Restart Components

[source,bash]
----
# Restart frontend
kubectl rollout restart deployment/tenant-production-test-frontend -n tenant-production-test

# Restart backend
kubectl rollout restart deployment/tenant-production-test-backend -n tenant-production-test

# Restart RabbitMQ
kubectl rollout restart deployment/tenant-production-test-rabbitmq -n tenant-production-test

# Check rollout status
kubectl rollout status deployment/tenant-production-test-frontend -n tenant-production-test
----

=== 14.2 Scale Components

[source,bash]
----
# Scale frontend
kubectl scale deployment tenant-production-test-frontend --replicas=2 -n tenant-production-test

# Scale backend
kubectl scale deployment tenant-production-test-backend --replicas=2 -n tenant-production-test

# Check scaling status
kubectl get pods -n tenant-production-test -w
----

== Conclusion

This testing guide provides comprehensive verification commands for the tenant onboarding system. Use these commands systematically to verify each component and identify any issues. The system is considered fully operational when all components show ✅ status and Architrave CLI commands execute successfully.

For issues, refer to the troubleshooting section and check the logs using the provided commands.