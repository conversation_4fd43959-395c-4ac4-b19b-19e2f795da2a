= Immediate Action Plan - Tenant Deployment Fixes
:toc: left
:toclevels: 2
:sectnums:
:icons: font

== Critical Issues Verification Results

=== 1. kubectl Connectivity Issue (CRITICAL) ❌ CONFIRMED

**Problem**: kubectl commands hanging/timing out - ALL commands are hanging
**Status**: CRITICAL - Cannot execute any kubectl commands
**Impact**: Complete inability to manage Kubernetes resources

**Root Cause**: Terminal execution environment issue
- Basic commands like `whoami`, `echo` are hanging
- Not specific to kubectl - affects all command execution
- Likely network/environment connectivity issue

**Immediate Fix Required**:
[source,bash]
----
# MANUAL STEPS REQUIRED (outside this environment):

# 1. Check kubectl configuration
kubectl config view
kubectl config current-context

# 2. Refresh EKS credentials
aws eks update-kubeconfig --region eu-central-1 --name production-cluster

# 3. Test basic connectivity
kubectl get nodes --request-timeout=10s

# 4. Verify cluster access
kubectl get pods -A --request-timeout=10s
----

**Previous Session Status**: ✅ kubectl was working, all components deployed successfully

=== 2. S3 CSI Driver Installation (HIGH) ❌ CONFIRMED

**Problem**: S3 CSI driver missing, causing PVC binding failures
**Status**: CONFIRMED - Backend pods were pending due to unbound S3 PVCs
**Impact**: Backend deployment fails, requires EmptyDir workaround

**Previous Session Findings**:
- S3 CSI driver not installed in cluster
- PersistentVolume in "Released" state
- Backend pods stuck in Pending due to unbound PVCs
- **Workaround Applied**: Replaced S3 volume with EmptyDir

**Required Fix**:
[source,bash]
----
# Install S3 CSI driver
helm repo add aws-s3-csi-driver https://kubernetes-sigs.github.io/aws-s3-csi-driver
helm repo update

# Install the driver with proper IAM role
helm install aws-s3-csi-driver aws-s3-csi-driver/aws-s3-csi-driver \
  --namespace kube-system \
  --set controller.serviceAccount.annotations."eks\.amazonaws\.com/role-arn"="arn:aws:iam::************:role/S3CSIDriverRole"

# Verify installation
kubectl get pods -n kube-system | grep s3-csi
kubectl get storageclass | grep s3

# Update backend deployment to use S3 volume again
kubectl patch deployment tenant-production-test-backend -n tenant-production-test --type='json' -p='[
  {
    "op": "replace",
    "path": "/spec/template/spec/volumes/0",
    "value": {
      "name": "s3-storage",
      "persistentVolumeClaim": {
        "claimName": "s3-pvc-production-test"
      }
    }
  }
]'
----

=== 3. Database User Permissions Fix (HIGH) ✅ FIXED

**Problem**: Tenant users don't have access to architrave database
**Status**: FIXED - Database permissions corrected in previous session
**Impact**: Backend could not connect to database initially

**Previous Session Findings**:
- User `tenant_production_test` was created for database `tenant_production_test`
- But application tries to connect to `architrave` database
- **Fix Applied**: Granted user access to `architrave` database
- Database connectivity now working (73 tables accessible)

**Verification Commands**:
[source,bash]
----
# Test database connection (WORKING)
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- bash -c "
export MYSQL_PWD=\$(echo 'SURGZ29sbENiN3NuN0NYRw==' | base64 -d)
mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u tenant_production_test -e 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = \"architrave\";'
"
# Expected: table_count: 73
----

**Onboarding Script Fix Required**:
[source,python]
----
# Update advanced_tenant_onboard.py database creation function
def create_database_and_user(tenant_id, db_password):
    """Create database and user with proper permissions"""

    # Create user with access to architrave database (not tenant-specific DB)
    create_user_sql = f"""
    CREATE USER IF NOT EXISTS 'tenant_{tenant_id}'@'%' IDENTIFIED BY '{db_password}';
    GRANT ALL PRIVILEGES ON architrave.* TO 'tenant_{tenant_id}'@'%';
    FLUSH PRIVILEGES;
    """

    # Execute SQL commands
    # ... implementation
----

=== 4. Health Check Endpoints Enhancement (MEDIUM) ✅ PARTIALLY FIXED

**Problem**: Basic health checks don't verify all components
**Status**: PARTIALLY FIXED - Basic endpoints created, need enhancement
**Impact**: Limited visibility into component health

**Previous Session Findings**:
- Created basic health check endpoint: `/storage/ArchAssets/public/health-check.php`
- Created extended health endpoint: `/storage/ArchAssets/public/api/health/extended.php`
- **Working**: Extended endpoint returns proper JSON format
- **Missing**: Database and RabbitMQ connectivity checks

**Current Working Endpoint**:
[source,bash]
----
# Test extended health endpoint (WORKING)
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/api/health/extended.php

# Expected JSON:
# {
#     "status": "healthy",
#     "check": {
#         "basic": "y",
#         "extended": "y",
#         "elastic-search": "n"
#     }
# }
----

**Enhanced Health Check Needed**:
[source,php]
----
# Update /storage/ArchAssets/public/api/health/extended.php
<?php
header('Content-Type: application/json');

$health = [
    'status' => 'healthy',
    'timestamp' => date('Y-m-d H:i:s'),
    'tenant_id' => 'production-test',
    'checks' => []
];

// Database check
try {
    $dbHost = getenv('DB_HOST');
    $dbUser = getenv('DB_USER');
    $dbPass = getenv('DB_PASSWORD');
    $dbName = getenv('DB_NAME');

    $cmd = "mysql -h $dbHost -u $dbUser -p$dbPass -e 'SELECT 1;' $dbName 2>&1";
    $output = shell_exec($cmd);

    $health['checks']['database'] = [
        'status' => (strpos($output, 'ERROR') === false) ? 'healthy' : 'unhealthy',
        'host' => $dbHost,
        'database' => $dbName
    ];
} catch (Exception $e) {
    $health['checks']['database'] = ['status' => 'unhealthy', 'error' => $e->getMessage()];
    $health['status'] = 'unhealthy';
}

// RabbitMQ check
$rabbitmqHost = 'tenant-production-test-rabbitmq';
$health['checks']['rabbitmq'] = [
    'status' => (fsockopen($rabbitmqHost, 5672, $errno, $errstr, 5)) ? 'healthy' : 'unhealthy',
    'host' => $rabbitmqHost
];

// Overall status
if ($health['checks']['database']['status'] !== 'healthy' ||
    $health['checks']['rabbitmq']['status'] !== 'healthy') {
    $health['status'] = 'unhealthy';
}

echo json_encode($health, JSON_PRETTY_PRINT);
?>
----

== Quick Verification Script

Create a comprehensive test script:

[source,bash]
----
#!/bin/bash
# File: test-tenant-deployment.sh

TENANT_ID="production-test"
NAMESPACE="tenant-${TENANT_ID}"

echo "=== TENANT DEPLOYMENT VERIFICATION ==="
echo "Tenant: $TENANT_ID"
echo "Namespace: $NAMESPACE"
echo ""

# Test 1: Cluster connectivity
echo "1. Testing cluster connectivity..."
if kubectl get nodes --request-timeout=10s > /dev/null 2>&1; then
    echo "   ✅ Cluster accessible"
else
    echo "   ❌ Cluster not accessible"
    exit 1
fi

# Test 2: Namespace exists
echo "2. Checking namespace..."
if kubectl get namespace $NAMESPACE > /dev/null 2>&1; then
    echo "   ✅ Namespace exists"
else
    echo "   ❌ Namespace missing"
    exit 1
fi

# Test 3: Pod status
echo "3. Checking pod status..."
kubectl get pods -n $NAMESPACE --no-headers | while read line; do
    name=$(echo $line | awk '{print $1}')
    ready=$(echo $line | awk '{print $2}')
    status=$(echo $line | awk '{print $3}')

    if [[ "$status" == "Running" && "$ready" =~ ^[0-9]+/[0-9]+$ ]]; then
        echo "   ✅ $name: $ready $status"
    else
        echo "   ❌ $name: $ready $status"
    fi
done

# Test 4: Service endpoints
echo "4. Checking service endpoints..."
kubectl get endpoints -n $NAMESPACE --no-headers | while read line; do
    name=$(echo $line | awk '{print $1}')
    endpoints=$(echo $line | awk '{print $2}')

    if [[ "$endpoints" != "<none>" ]]; then
        echo "   ✅ $name: $endpoints"
    else
        echo "   ❌ $name: No endpoints"
    fi
done

echo ""
echo "=== VERIFICATION COMPLETE ==="
----

== Implementation Priority

=== Week 1 (Critical)
1. Fix kubectl connectivity
2. Install S3 CSI driver
3. Update database user permissions in onboarding script
4. Test and verify all components working

=== Week 2 (High)
1. Implement enhanced health checks
2. Add basic monitoring setup
3. Create automated testing scripts
4. Document all procedures

=== Week 3-4 (Medium)
1. Add network policies
2. Implement backup procedures
3. Add resource quotas
4. Security enhancements

== Success Criteria

After implementing these fixes:

* ✅ kubectl commands execute without hanging
* ✅ All pods show Ready status (2/2 or 1/1)
* ✅ S3 volumes mount successfully
* ✅ Database connections work from first deployment
* ✅ Health checks return comprehensive status
* ✅ All services have valid endpoints
* ✅ Integration tests pass consistently

== Next Steps

1. **Immediate**: Run cluster connectivity diagnosis
2. **Today**: Install S3 CSI driver and test
3. **Tomorrow**: Update onboarding script with database fixes
4. **This Week**: Implement enhanced health checks
5. **Next Week**: Add monitoring and automated testing
