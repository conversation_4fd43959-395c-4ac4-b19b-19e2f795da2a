= Comprehensive Tenant Testing Guide
:toc: left
:toclevels: 3
:sectnums:
:icons: font
:source-highlighter: rouge

== Overview

This document provides step-by-step testing procedures to validate all components of a tenant deployment after onboarding. Use these commands to verify that backend, frontend, RabbitMQ, and database are working correctly.

== Prerequisites

* kubectl configured and connected to the cluster
* Access to tenant namespace: `tenant-<TENANT_ID>`
* Database credentials available in secrets
* Understanding of tenant architecture

== Quick Status Check

=== 1. Pod Status Verification

[source,bash]
----
# Check all pods are running
kubectl get pods -n tenant-production-test -o wide

# Expected output:
# NAME                                               READY   STATUS    RESTARTS
# tenant-production-test-backend-xxx                2/2     Running   0
# tenant-production-test-frontend-xxx               1/1     Running   0  
# tenant-production-test-rabbitmq-xxx               2/2     Running   0
----

=== 2. Service and Endpoint Check

[source,bash]
----
# Verify services exist
kubectl get svc -n tenant-production-test

# Check service endpoints
kubectl get endpoints -n tenant-production-test
----

== Component Testing

=== 3. Frontend (Nginx) Testing

[source,bash]
----
# Test basic nginx health
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/health

# Expected: OK

# Test nginx configuration
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- nginx -t

# Check nginx logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-frontend --tail=10
----

=== 4. Backend (PHP-FPM) Testing

[source,bash]
----
# Test backend health check directly
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- php /storage/ArchAssets/public/health-check.php

# Expected JSON output with status: healthy

# Test PHP-FPM status
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- php-fpm -t

# Check backend logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-backend -c backend --tail=10
----

=== 5. Frontend-Backend Integration Testing

[source,bash]
----
# Test PHP processing through nginx (most important test)
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/api/health/extended.php

# Expected JSON:
# {
#     "status": "healthy",
#     "check": {
#         "basic": "y",
#         "extended": "y", 
#         "elastic-search": "n"
#     }
# }

# Test if frontend can reach backend service
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- nslookup webapp
----

=== 6. Database Connectivity Testing

[source,bash]
----
# Test database connection from backend
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- bash -c "
export MYSQL_PWD=\$(echo 'SURGZ29sbENiN3NuN0NYRw==' | base64 -d)
mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u tenant_production_test -e 'SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = \"architrave\";'
"

# Expected: table_count: 73

# Test database user permissions
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- bash -c "
export MYSQL_PWD=\$(echo 'SURGZ29sbENiN3NuN0NYRw==' | base64 -d)
mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u tenant_production_test -e 'SHOW GRANTS;'
"
----

=== 7. RabbitMQ Testing

[source,bash]
----
# Test RabbitMQ ping
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl ping

# Expected: pong

# Test RabbitMQ status
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl status

# Test RabbitMQ connectivity from backend
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- bash -c "
timeout 5 bash -c '</dev/tcp/tenant-production-test-rabbitmq/5672' && echo 'RabbitMQ connectivity: SUCCESS' || echo 'RabbitMQ connectivity: FAILED'
"

# Check RabbitMQ logs
kubectl logs -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq --tail=10
----

=== 8. Configuration Validation

[source,bash]
----
# Check database credentials secret
kubectl get secret db-credentials -n tenant-production-test -o yaml

# Decode and verify credentials
kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.database}' | base64 -d
kubectl get secret db-credentials -n tenant-production-test -o jsonpath='{.data.username}' | base64 -d

# Check ConfigMaps
kubectl get configmap -n tenant-production-test
----

=== 9. Istio Configuration Check

[source,bash]
----
# Check Istio resources
kubectl get virtualservice,destinationrule,peerauthentication -n tenant-production-test

# Verify Istio sidecar injection
kubectl get pods -n tenant-production-test -o jsonpath='{.items[*].spec.containers[*].name}'
----

=== 10. Resource Monitoring

[source,bash]
----
# Check resource usage
kubectl top pods -n tenant-production-test

# Check pod descriptions for issues
kubectl describe pod -n tenant-production-test -l component=backend
kubectl describe pod -n tenant-production-test -l component=frontend
kubectl describe pod -n tenant-production-test -l component=rabbitmq
----

== Advanced Testing

=== 11. Architrave CLI Testing

[source,bash]
----
# Test Architrave CLI functionality
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- sudo -u www-data /storage/ArchAssets/bin/architrave --version

# Test database schema validation
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- sudo -u www-data /storage/ArchAssets/vendor/bin/doctrine-module orm:validate-schema

# Test document processing command
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- sudo -u www-data /storage/ArchAssets/bin/architrave cron:system:process-new-documents --dry-run
----

=== 12. End-to-End Integration Test

[source,bash]
----
# Complete integration test script
echo "=== COMPREHENSIVE TENANT TEST ==="
echo "Tenant: production-test"
echo ""

echo "1. Pod Status:"
kubectl get pods -n tenant-production-test
echo ""

echo "2. Frontend Health:"
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/health
echo ""

echo "3. Backend Integration:"
kubectl exec -n tenant-production-test deployment/tenant-production-test-frontend -- curl -s http://localhost/api/health/extended.php
echo ""

echo "4. Database Connection:"
kubectl exec -n tenant-production-test deployment/tenant-production-test-backend -c backend -- bash -c "
export MYSQL_PWD=\$(echo 'SURGZ29sbENiN3NuN0NYRw==' | base64 -d)
mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u tenant_production_test -e 'SELECT \"Database OK\" as status;' architrave
"
echo ""

echo "5. RabbitMQ Status:"
kubectl exec -n tenant-production-test deployment/tenant-production-test-rabbitmq -c rabbitmq -- rabbitmqctl ping
echo ""

echo "=== TEST COMPLETE ==="
----

== Success Criteria

✅ **All tests should pass with:**

* All pods showing 2/2 or 1/1 Ready status
* Frontend health returning "OK"
* Extended health endpoint returning proper JSON
* Database showing 73 tables and successful connection
* RabbitMQ ping returning "pong"
* No error messages in recent logs
* All services having valid endpoints

== Troubleshooting

If any test fails, check:

1. **Pod logs** for error messages
2. **Service endpoints** for connectivity issues  
3. **Database permissions** for access problems
4. **Resource limits** for performance issues
5. **Istio configuration** for networking problems

== Docker Images Used

* **Backend**: `545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.54`
* **Frontend/Nginx**: `545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl`
* **RabbitMQ**: `545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02`
