# Tenant Health Check Guide

## Overview
This guide explains how to perform comprehensive health checks after tenant onboarding to ensure all components are working correctly.

## Prerequisites
- kubectl access to the cluster
- Tenant namespace exists and is running
- All pods are in Running state

## Health Check Procedures

### 1. Basic Pod Status Check
```bash
# Check all pods in tenant namespace
kubectl get pods -n tenant-<TENANT_ID>

# Expected output: All pods should be Running
# - tenant-<TENANT_ID>-frontend: 1/1 Running
# - tenant-<TENANT_ID>-backend: 2/2 Running  
# - tenant-<TENANT_ID>-rabbitmq: 2/2 Running
```

### 2. Application Health Check Endpoint
```bash
# Test the main health check endpoint
kubectl exec -n tenant-<TENANT_ID> <frontend-pod-name> -- curl -s http://localhost/api/health/extended.php

# Expected output:
# {"status":"healthy","check":{"basic":"y","extended":"y","elastic-search":"n"}}
```

### 3. Frontend Health Check
```bash
# Test basic frontend health
kubectl exec -n tenant-<TENANT_ID> <frontend-pod-name> -- curl -s http://localhost/health

# Expected output: OK
```

### 4. Database Connectivity Check
```bash
# Test database connection from backend
kubectl exec -n tenant-<TENANT_ID> <backend-pod-name> -c backend -- php -r "
\$db_host = getenv('DB_HOST');
\$db_name = getenv('DB_NAME');
\$db_user = getenv('DB_USER');
\$db_pass = getenv('DB_PASSWORD');
try {
    \$dsn = \"mysql:host=\$db_host;dbname=\$db_name;charset=utf8mb4\";
    \$options = [PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false];
    \$pdo = new PDO(\$dsn, \$db_user, \$db_pass, \$options);
    echo \"✅ Database connection: SUCCESS\n\";
    echo \"Host: \$db_host\n\";
    echo \"Database: \$db_name\n\";
} catch (Exception \$e) {
    echo \"❌ Database connection: FAILED - \" . \$e->getMessage() . \"\n\";
}
"

# Expected output: ✅ Database connection: SUCCESS
```

### 5. RabbitMQ Health Check
```bash
# Check RabbitMQ status
kubectl exec -n tenant-<TENANT_ID> <rabbitmq-pod-name> -c rabbitmq -- rabbitmqctl status

# Expected: Should show running status without errors
```

### 6. PHP-FPM Health Check
```bash
# Check PHP-FPM processes
kubectl exec -n tenant-<TENANT_ID> <backend-pod-name> -c backend -- ps aux | grep php-fpm

# Expected: Should show multiple php-fpm processes running
```

### 7. Service Connectivity Check
```bash
# Check if services are accessible
kubectl get svc -n tenant-<TENANT_ID>

# Test service endpoints
kubectl exec -n tenant-<TENANT_ID> <frontend-pod-name> -- curl -s http://webapp:9000 || echo "FastCGI connection test"
```

## Troubleshooting Common Issues

### Issue 1: 502 Bad Gateway on Health Check
**Symptoms**: Health check returns HTML error page instead of JSON
**Solution**: 
1. Check if health.conf exists and is properly configured
2. Verify PHP-FPM is listening on correct port
3. Ensure health check file exists in frontend container

### Issue 2: Database Connection Failed
**Symptoms**: Database connection errors
**Solution**:
1. Check database credentials in secrets
2. Verify SSL configuration
3. Test network connectivity to RDS

### Issue 3: Pod Not Running
**Symptoms**: Pods stuck in Pending/CrashLoopBackOff
**Solution**:
1. Check pod logs: `kubectl logs -n tenant-<TENANT_ID> <pod-name>`
2. Describe pod: `kubectl describe pod -n tenant-<TENANT_ID> <pod-name>`
3. Check resource limits and node capacity

## Health Check Automation Script

Create a script to automate health checks:

```bash
#!/bin/bash
TENANT_ID=$1
NAMESPACE="tenant-${TENANT_ID}"

echo "=== TENANT HEALTH CHECK: $TENANT_ID ==="

# 1. Pod Status
echo "1. Checking pod status..."
kubectl get pods -n $NAMESPACE

# 2. Health Endpoint
echo "2. Testing health endpoint..."
FRONTEND_POD=$(kubectl get pods -n $NAMESPACE -l app=tenant-${TENANT_ID}-frontend -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n $NAMESPACE $FRONTEND_POD -- curl -s http://localhost/api/health/extended.php

# 3. Database Test
echo "3. Testing database connection..."
BACKEND_POD=$(kubectl get pods -n $NAMESPACE -l app=tenant-${TENANT_ID}-backend -o jsonpath='{.items[0].metadata.name}')
kubectl exec -n $NAMESPACE $BACKEND_POD -c backend -- php -r "
try {
    \$pdo = new PDO('mysql:host='.getenv('DB_HOST').';dbname='.getenv('DB_NAME'), getenv('DB_USER'), getenv('DB_PASSWORD'), [PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false]);
    echo '✅ Database: OK\n';
} catch (Exception \$e) {
    echo '❌ Database: FAILED\n';
}
"

echo "=== HEALTH CHECK COMPLETE ==="
```

## Expected Results Summary

| Component | Check | Expected Result |
|-----------|-------|----------------|
| Frontend | /health | "OK" |
| Frontend | /api/health/extended.php | JSON with status:"healthy" |
| Backend | PHP-FPM processes | 4-5 processes running |
| Database | Connection test | Success message |
| RabbitMQ | Status check | Running without errors |
| All Pods | Status | Running state |

## Notes
- Health checks should be performed immediately after onboarding
- Re-run checks after any configuration changes
- Monitor logs if any check fails
- The health.conf file is added during deployment and not part of original image
