= Comprehensive Testing Automation for Tenant Onboarding
:toc:
:toclevels: 3
:sectnums:

== Overview

This document describes the comprehensive testing automation implemented in the advanced tenant onboarding script to address all critical issues and verify complete functionality.

== 🔧 Critical Issues Fixed

=== 1. Frontend-Backend Communication (Istio mTLS)

**Issue**: Istio STRICT mTLS mode blocking frontend-backend communication
**Error**: `curl: (56) Recv failure: Connection reset by peer`

**Automated Fix**:
- Detects STRICT mTLS configuration
- Applies PERMISSIVE mTLS policy for tenant namespace
- Creates proper DestinationRule for mTLS handling
- Tests frontend-backend communication

**Test Commands**:
```bash
# Check mTLS policy
kubectl get peerauthentication -n tenant-{tenant_id} -o yaml

# Test frontend-backend communication
kubectl exec -n tenant-{tenant_id} {frontend_pod} -c frontend -- curl -s http://webapp:8080/health.php
```

=== 2. CLI SSL Configuration

**Issue**: Architrave CLI commands fail with SSL error
**Error**: `Failed to set option 21 with value ""`

**Automated Fix**:
- Creates fixed SSL configuration in local.php
- Uses numeric constants instead of PHP constants
- Tests CLI functionality and database connectivity

**Test Commands**:
```bash
# Check SSL configuration
kubectl exec -n tenant-{tenant_id} {backend_pod} -c backend -- cat /storage/ArchAssets/config/autoload/local.php | grep "21 => false"

# Test CLI functionality
kubectl exec -n tenant-{tenant_id} {backend_pod} -c backend -- /storage/ArchAssets/bin/architrave --help
```

=== 3. Monitoring Components

**Issue**: ServiceMonitor and PrometheusRule not deployed

**Automated Fix**:
- Deploys ServiceMonitor for metrics collection
- Deploys PrometheusRule for alerting
- Verifies monitoring endpoints

**Test Commands**:
```bash
# Check monitoring components
kubectl get servicemonitor -n tenant-{tenant_id}
kubectl get prometheusrule -n tenant-{tenant_id}

# Test metrics endpoint
kubectl exec -n tenant-{tenant_id} {backend_pod} -c backend -- curl -s http://localhost:8080/metrics
```

=== 4. Frontend Container Enhancement

**Issue**: Missing diagnostic tools in nginx container

**Automated Fix**:
- Installs diagnostic tools (ps, ip, nslookup, curl, etc.)
- Enhances troubleshooting capabilities
- Tests tool availability

**Test Commands**:
```bash
# Test diagnostic tools
kubectl exec -n tenant-{tenant_id} {frontend_pod} -c frontend -- ps aux
kubectl exec -n tenant-{tenant_id} {frontend_pod} -c frontend -- ip addr
kubectl exec -n tenant-{tenant_id} {frontend_pod} -c frontend -- nslookup kubernetes.default.svc.cluster.local
```

== 🧪 Comprehensive Testing Framework

=== Testing Categories

1. **Istio mTLS Configuration Testing**
   - PeerAuthentication policy verification
   - Frontend-backend communication testing
   - Service mesh routing validation

2. **Networking Connectivity Testing**
   - DNS resolution testing
   - Service-to-service communication
   - Database connectivity verification
   - RabbitMQ connectivity testing

3. **CLI Tools Functionality Testing**
   - Architrave CLI binary verification
   - PHP configuration testing
   - MySQL extensions validation
   - SSL configuration verification
   - Database connection via CLI

4. **Monitoring Components Testing**
   - ServiceMonitor deployment verification
   - PrometheusRule deployment verification
   - Metrics endpoint availability
   - Service labeling for monitoring

=== Test Execution Flow

```
1. 🔧 Apply Critical Fixes
   ├── Fix Istio mTLS Configuration
   ├── Fix CLI SSL Configuration
   ├── Deploy Monitoring Components
   └── Enhance Frontend Container

2. 🧪 Execute Comprehensive Tests
   ├── Test Istio mTLS Configuration
   ├── Test Networking Connectivity
   ├── Test CLI Tools Functionality
   └── Test Monitoring Components

3. 🎯 Display Results & Generate Fix Plan
   ├── Comprehensive Results Display
   ├── Critical Issues Analysis
   └── Automated Fix Script Generation
```

== 📊 Results Display

=== Comprehensive Validation Results

The testing framework provides detailed results including:

- **Overall Status Summary**: Working/Warning/Failed counts
- **Detailed Component Analysis**: Per-component status and messages
- **Critical Issues Section**: High/Medium priority issues
- **Successfully Working Components**: Confirmed working components

=== Automated Fix Plan Generation

For any failed components, the system generates:

- **Component-specific fix recommendations**
- **Executable fix commands**
- **Automated fix script** saved to `/tmp/tenant-{tenant_id}-fix-script.sh`

== 🚀 Usage

=== Running the Enhanced Onboarding Script

```bash
# Run with comprehensive testing
python3 advanced_tenant_onboard.py \
  --tenant-id test123 \
  --tenant-name "Test Tenant" \
  --subdomain test123 \
  --domain architrave.com \
  --environment dev
```

=== Manual Testing Commands

```bash
# Test all components for a tenant
TENANT_ID="test123"

# 1. Test Istio mTLS
kubectl get peerauthentication -n tenant-${TENANT_ID}
kubectl exec -n tenant-${TENANT_ID} $(kubectl get pods -n tenant-${TENANT_ID} -l app=tenant-${TENANT_ID}-frontend -o jsonpath='{.items[0].metadata.name}') -c frontend -- curl -s http://webapp:8080/health.php

# 2. Test CLI Tools
kubectl exec -n tenant-${TENANT_ID} $(kubectl get pods -n tenant-${TENANT_ID} -l app=tenant-${TENANT_ID}-backend -o jsonpath='{.items[0].metadata.name}') -c backend -- /storage/ArchAssets/bin/architrave --help

# 3. Test Monitoring
kubectl get servicemonitor,prometheusrule -n tenant-${TENANT_ID}

# 4. Test Networking
kubectl exec -n tenant-${TENANT_ID} $(kubectl get pods -n tenant-${TENANT_ID} -l app=tenant-${TENANT_ID}-backend -o jsonpath='{.items[0].metadata.name}') -c backend -- nslookup kubernetes.default.svc.cluster.local
```

== 📋 Test Results Interpretation

=== Status Codes

- **✅ WORKING**: Component is fully functional
- **⚠️ WARNING**: Component has issues but is partially functional
- **❌ FAILED**: Component is not working
- **❓ UNKNOWN**: Component status could not be determined

=== Common Issues and Solutions

==== Frontend-Backend Communication Failure
```
Issue: curl: (56) Recv failure: Connection reset by peer
Solution: Apply PERMISSIVE mTLS policy
Command: kubectl patch peerauthentication -n tenant-{tenant_id} tenant-{tenant_id}-permissive --type='merge' -p='{"spec":{"mtls":{"mode":"PERMISSIVE"}}}'
```

==== CLI SSL Configuration Error
```
Issue: Failed to set option 21 with value ""
Solution: Update SSL configuration with numeric constants
Command: kubectl exec -n tenant-{tenant_id} deployment/tenant-{tenant_id}-backend -c backend -- sed -i 's/MYSQLI_OPT_SSL_VERIFY_SERVER_CERT/21/g' /storage/ArchAssets/config/autoload/local.php
```

==== Missing Monitoring Components
```
Issue: ServiceMonitor/PrometheusRule not found
Solution: Deploy monitoring components automatically
Status: Handled automatically by the onboarding script
```

== 🎯 Success Criteria

A tenant is considered successfully onboarded when:

1. **All core components** (database, backend, frontend, RabbitMQ) are WORKING
2. **Istio mTLS** is properly configured for communication
3. **CLI tools** are functional with proper SSL configuration
4. **Monitoring components** are deployed and functional
5. **Networking** allows proper service-to-service communication
6. **Security** policies are in place for tenant isolation

== 📈 Continuous Improvement

The testing framework is designed to:

- **Automatically detect** new types of issues
- **Generate specific fixes** for identified problems
- **Provide actionable insights** for manual intervention
- **Save fix scripts** for future reference and automation

This comprehensive testing automation ensures that tenant onboarding is reliable, predictable, and fully verified before completion.
