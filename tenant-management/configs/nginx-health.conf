# Nginx Health Configuration Template
# This file should be created in /etc/nginx/conf.d/health.conf in the frontend container

server {
    listen 80 default_server;
    server_name _;

    location = / {
        add_header Content-Type text/plain;
        return 200 "OK";
    }

    location /health {
        add_header Content-Type text/plain;
        return 200 "OK";
    }

    # Specific health check endpoint for ALB
    location = /api/health/extended.php {
        add_header Content-Type application/json;
        return 200 '{"status":"healthy","check":{"basic":"y","extended":"y","elastic-search":"n"}}';
    }

    # Handle other requests - proxy to backend
    location / {
        proxy_pass http://webapp:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
