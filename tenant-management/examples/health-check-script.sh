#!/bin/bash
# Tenant Health Check Script
# Usage: ./health-check-script.sh <tenant-id>

TENANT_ID=$1
NAMESPACE="tenant-${TENANT_ID}"

if [ -z "$TENANT_ID" ]; then
    echo "Usage: $0 <tenant-id>"
    exit 1
fi

echo "=== TENANT HEALTH CHECK: $TENANT_ID ==="
echo "Namespace: $NAMESPACE"
echo "Timestamp: $(date)"
echo

# 1. Pod Status Check
echo "1. Checking pod status..."
kubectl get pods -n $NAMESPACE --no-headers | while read line; do
    pod_name=$(echo $line | awk '{print $1}')
    status=$(echo $line | awk '{print $3}')
    if [ "$status" = "Running" ]; then
        echo "   ✅ $pod_name: $status"
    else
        echo "   ❌ $pod_name: $status"
    fi
done
echo

# 2. Health Endpoint Check
echo "2. Testing health endpoint..."
FRONTEND_POD=$(kubectl get pods -n $NAMESPACE -l app=tenant-${TENANT_ID}-frontend -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$FRONTEND_POD" ]; then
    HEALTH_RESPONSE=$(kubectl exec -n $NAMESPACE $FRONTEND_POD -- curl -s http://localhost/api/health/extended.php 2>/dev/null)
    if [[ "$HEALTH_RESPONSE" == *"healthy"* ]]; then
        echo "   ✅ Health endpoint: OK"
        echo "   📊 Response: $HEALTH_RESPONSE"
    else
        echo "   ❌ Health endpoint: FAILED"
        echo "   📊 Response: $HEALTH_RESPONSE"
    fi
else
    echo "   ❌ Frontend pod not found"
fi
echo

# 3. Database Connection Check
echo "3. Testing database connection..."
BACKEND_POD=$(kubectl get pods -n $NAMESPACE -l app=tenant-${TENANT_ID}-backend -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$BACKEND_POD" ]; then
    DB_TEST=$(kubectl exec -n $NAMESPACE $BACKEND_POD -c backend -- php -r "
    try {
        \$pdo = new PDO('mysql:host='.getenv('DB_HOST').';dbname='.getenv('DB_NAME'), getenv('DB_USER'), getenv('DB_PASSWORD'), [PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false]);
        echo 'SUCCESS';
    } catch (Exception \$e) {
        echo 'FAILED: ' . \$e->getMessage();
    }
    " 2>/dev/null)
    
    if [[ "$DB_TEST" == "SUCCESS" ]]; then
        echo "   ✅ Database connection: OK"
    else
        echo "   ❌ Database connection: $DB_TEST"
    fi
else
    echo "   ❌ Backend pod not found"
fi
echo

# 4. RabbitMQ Check
echo "4. Testing RabbitMQ..."
RABBITMQ_POD=$(kubectl get pods -n $NAMESPACE -l app=tenant-${TENANT_ID}-rabbitmq -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
if [ -n "$RABBITMQ_POD" ]; then
    RABBITMQ_STATUS=$(kubectl exec -n $NAMESPACE $RABBITMQ_POD -c rabbitmq -- rabbitmqctl status 2>/dev/null | grep -c "Status of node" || echo "0")
    if [ "$RABBITMQ_STATUS" -gt 0 ]; then
        echo "   ✅ RabbitMQ: OK"
    else
        echo "   ❌ RabbitMQ: Status check failed"
    fi
else
    echo "   ❌ RabbitMQ pod not found"
fi
echo

# 5. Service Check
echo "5. Checking services..."
kubectl get svc -n $NAMESPACE --no-headers | while read line; do
    svc_name=$(echo $line | awk '{print $1}')
    svc_type=$(echo $line | awk '{print $2}')
    echo "   📋 $svc_name ($svc_type)"
done
echo

echo "=== HEALTH CHECK COMPLETE ==="

# Summary
echo "=== SUMMARY ==="
TOTAL_PODS=$(kubectl get pods -n $NAMESPACE --no-headers | wc -l)
RUNNING_PODS=$(kubectl get pods -n $NAMESPACE --no-headers | grep Running | wc -l)
echo "Pods: $RUNNING_PODS/$TOTAL_PODS running"

if [ "$RUNNING_PODS" -eq "$TOTAL_PODS" ] && [[ "$HEALTH_RESPONSE" == *"healthy"* ]] && [[ "$DB_TEST" == "SUCCESS" ]]; then
    echo "🎉 Overall Status: HEALTHY"
    exit 0
else
    echo "⚠️  Overall Status: ISSUES DETECTED"
    exit 1
fi
