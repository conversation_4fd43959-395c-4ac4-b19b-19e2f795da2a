#!/usr/bin/env python3
"""
Identity & Access Management Module
Implements MFA, JWT security, API security, session management,
and fine-grained RBAC for tenant environments.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class IdentityAccessManager:
    """Manages identity and access control for tenant environments."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_rbac_config(self, tenant_id: str) -> str:
        """Create fine-grained RBAC configuration for tenant."""
        rbac_config = f"""apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-{tenant_id}-admin
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/rbac-role: "admin"
rules:
- apiGroups: [""]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["apps"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["networking.k8s.io"]
  resources: ["*"]
  verbs: ["*"]
- apiGroups: ["batch"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-{tenant_id}-developer
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/rbac-role: "developer"
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list", "create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-{tenant_id}-viewer
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/rbac-role: "viewer"
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list"]
- apiGroups: ["batch"]
  resources: ["jobs"]
  verbs: ["get", "list"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-admin-sa
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "admin"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-developer-sa
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "developer"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-viewer-sa
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "viewer"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-{tenant_id}-admin-binding
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/role-binding: "admin"
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-admin-sa
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: tenant-{tenant_id}-admin
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-{tenant_id}-developer-binding
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/role-binding: "developer"
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-developer-sa
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: tenant-{tenant_id}-developer
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-{tenant_id}-viewer-binding
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/role-binding: "viewer"
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-viewer-sa
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: tenant-{tenant_id}-viewer
  apiGroup: rbac.authorization.k8s.io
"""
        return rbac_config

    def create_jwt_security_config(self, tenant_id: str) -> str:
        """Create JWT security configuration."""
        jwt_config = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-jwt-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/jwt-config: "true"
data:
  jwt-config.json: |
    {{
      "tenant_id": "{tenant_id}",
      "issuer": "https://tenant-{tenant_id}.architrave.local/auth",
      "audience": "tenant-{tenant_id}-api",
      "algorithm": "RS256",
      "token_expiry": 3600,
      "refresh_token_expiry": 86400,
      "max_token_age": 7200,
      "require_https": true,
      "validate_issuer": true,
      "validate_audience": true,
      "validate_expiry": true,
      "validate_signature": true,
      "allowed_clock_skew": 300,
      "rate_limit": {{
        "requests_per_minute": 60,
        "burst_size": 10
      }},
      "security_headers": {{
        "X-Frame-Options": "DENY",
        "X-Content-Type-Options": "nosniff",
        "X-XSS-Protection": "1; mode=block",
        "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
      }}
    }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-auth-service
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/auth-service: "true"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-{tenant_id}-auth-service
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-auth-service
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-auth-service
      containers:
      - name: auth-service
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/auth-service:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TENANT_ID
          value: {tenant_id}
        - name: JWT_CONFIG_FILE
          value: /config/jwt-config.json
        - name: JWT_PRIVATE_KEY_FILE
          value: /certs/jwt-private.key
        - name: JWT_PUBLIC_KEY_FILE
          value: /certs/jwt-public.key
        volumeMounts:
        - name: jwt-config
          mountPath: /config
        - name: jwt-certs
          mountPath: /certs
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: jwt-config
        configMap:
          name: tenant-{tenant_id}-jwt-config
      - name: jwt-certs
        secret:
          secretName: tenant-{tenant_id}-jwt-certs
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-auth-service
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/auth-service: "true"
spec:
  selector:
    app: tenant-{tenant_id}-auth-service
  ports:
  - name: http
    port: 80
    targetPort: http
    protocol: TCP
"""
        return jwt_config

    def create_api_security_config(self, tenant_id: str) -> str:
        """Create API security configuration with rate limiting."""
        api_security = f"""apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tenant-{tenant_id}-api-security
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/api-security: "true"
spec:
  workloadSelector:
    labels:
      app: tenant-{tenant_id}-backend
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.jwt_authn
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.jwt_authn.v3.JwtAuthentication
          providers:
            tenant-{tenant_id}-jwt:
              issuer: "https://tenant-{tenant_id}.architrave.local/auth"
              audiences:
              - "tenant-{tenant_id}-api"
              remote_jwks:
                http_uri:
                  uri: "https://tenant-{tenant_id}.architrave.local/auth/.well-known/jwks.json"
                  cluster: "tenant-{tenant_id}-auth-service"
                  timeout: 5s
                cache_duration: 300s
              forward: true
              from_headers:
              - name: "Authorization"
                value_prefix: "Bearer "
          rules:
          - match:
              prefix: "/api/"
            requires:
              provider_name: "tenant-{tenant_id}-jwt"
          - match:
              prefix: "/health"
          - match:
              prefix: "/metrics"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-api-rate-limits
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/rate-limits: "true"
data:
  rate-limit-config.yaml: |
    domain: tenant-{tenant_id}-api
    descriptors:
    - key: tenant_id
      value: {tenant_id}
      rate_limit:
        unit: minute
        requests_per_unit: 1000
    - key: user_id
      rate_limit:
        unit: minute
        requests_per_unit: 100
    - key: api_key
      rate_limit:
        unit: minute
        requests_per_unit: 500
    - key: ip_address
      rate_limit:
        unit: minute
        requests_per_unit: 200
"""
        return api_security

    def apply_identity_access_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive identity and access management to tenant."""
        try:
            logger.info(f"🔐 Applying identity and access management for tenant-{tenant_id}")

            # 1. Create RBAC configuration
            rbac_config = self.create_rbac_config(tenant_id)

            # 2. Create JWT security configuration
            jwt_config = self.create_jwt_security_config(tenant_id)

            # 3. Create API security configuration
            api_security_config = self.create_api_security_config(tenant_id)

            # Apply all configurations
            configs = [
                ("rbac-config", rbac_config),
                ("jwt-security", jwt_config),
                ("api-security", api_security_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            logger.info(f"✅ Identity and access management setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply identity and access management for tenant {tenant_id}: {e}")
            return False
