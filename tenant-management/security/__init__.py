"""
Security module for tenant management
Provides secure credential management and input validation
"""

from .credentials import (
    CredentialManager,
    credential_manager,
    get_rds_credentials,
    get_tenant_credentials,
    create_tenant_credentials
)

from .validators import (
    SecurityValidator,
    ValidationError,
    validate_tenant_id,
    validate_database_name,
    sanitize_sql_input
)

# Check if advanced security modules are available (lazy import)
def _check_advanced_security():
    try:
        from .secrets_management import SecretsManager
        return True
    except ImportError:
        return False

ADVANCED_SECURITY_AVAILABLE = _check_advanced_security()

# Lazy import functions for security modules
def get_secrets_manager():
    from .secrets_management import SecretsManager, EncryptionManager
    return SecretsManager, EncryptionManager

def get_pod_security_manager():
    from .pod_security import PodSecurityManager
    return PodSecurityManager

def get_network_security_manager():
    from .network_security import NetworkSecurityManager
    return NetworkSecurityManager

def get_runtime_security_manager():
    from .runtime_security import RuntimeSecurityManager
    return RuntimeSecurityManager

def get_data_protection_manager():
    from .data_protection import DataProtectionManager
    return DataProtectionManager

def get_compliance_manager():
    from .compliance import ComplianceManager
    return ComplianceManager

def get_identity_access_manager():
    from .identity_access import IdentityAccessManager
    return IdentityAccessManager

def get_infrastructure_security_manager():
    from .infrastructure_security import InfrastructureSecurityManager
    return InfrastructureSecurityManager

__all__ = [
    'CredentialManager',
    'credential_manager',
    'get_rds_credentials',
    'get_tenant_credentials',
    'create_tenant_credentials',
    'SecurityValidator',
    'ValidationError',
    'validate_tenant_id',
    'validate_database_name',
    'sanitize_sql_input',
    'ADVANCED_SECURITY_AVAILABLE',
    'get_secrets_manager',
    'get_pod_security_manager',
    'get_network_security_manager',
    'get_runtime_security_manager',
    'get_data_protection_manager',
    'get_compliance_manager',
    'get_identity_access_manager',
    'get_infrastructure_security_manager'
]
