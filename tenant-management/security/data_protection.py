#!/usr/bin/env python3
"""
Data Protection & Encryption Module
Implements database encryption, backup encryption, data classification,
and PII protection for tenant data.
"""

import os
import json
import base64
import tempfile
import logging
from typing import Dict, List, Optional, Any
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

class DataProtectionManager:
    """Manages data protection and encryption for tenant data."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_database_encryption_config(self, tenant_id: str) -> str:
        """Create database encryption configuration for tenant."""
        db_encryption = f"""apiVersion: v1
kind: Secret
metadata:
  name: tenant-{tenant_id}-db-encryption
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/encryption: "database"
type: Opaque
data:
  master-key: {base64.b64encode(Fernet.generate_key()).decode()}
  column-key: {base64.b64encode(Fernet.generate_key()).decode()}
  backup-key: {base64.b64encode(Fernet.generate_key()).decode()}
  audit-key: {base64.b64encode(Fernet.generate_key()).decode()}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-db-encryption-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/db-config: "encryption"
data:
  encryption-config.sql: |
    -- Database encryption configuration for tenant {tenant_id}

    -- Create encryption functions
    DELIMITER $$

    CREATE FUNCTION tenant_{tenant_id.replace('-', '_')}_encrypt(data TEXT, key_name VARCHAR(50))
    RETURNS TEXT
    READS SQL DATA
    DETERMINISTIC
    BEGIN
        DECLARE encryption_key VARBINARY(255);
        DECLARE encrypted_data TEXT;

        -- Get encryption key from secure storage
        SELECT key_value INTO encryption_key
        FROM tenant_encryption_keys
        WHERE tenant_id = '{tenant_id}' AND key_name = key_name;

        -- Encrypt the data using AES
        SET encrypted_data = TO_BASE64(AES_ENCRYPT(data, encryption_key));

        RETURN encrypted_data;
    END$$

    CREATE FUNCTION tenant_{tenant_id.replace('-', '_')}_decrypt(encrypted_data TEXT, key_name VARCHAR(50))
    RETURNS TEXT
    READS SQL DATA
    DETERMINISTIC
    BEGIN
        DECLARE encryption_key VARBINARY(255);
        DECLARE decrypted_data TEXT;

        -- Get encryption key from secure storage
        SELECT key_value INTO encryption_key
        FROM tenant_encryption_keys
        WHERE tenant_id = '{tenant_id}' AND key_name = key_name;

        -- Decrypt the data using AES
        SET decrypted_data = AES_DECRYPT(FROM_BASE64(encrypted_data), encryption_key);

        RETURN decrypted_data;
    END$$

    DELIMITER ;

    -- Create tenant-specific encryption key storage
    CREATE TABLE IF NOT EXISTS tenant_encryption_keys (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(100) NOT NULL,
        key_name VARCHAR(50) NOT NULL,
        key_value VARBINARY(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_tenant_key (tenant_id, key_name),
        INDEX idx_tenant_id (tenant_id)
    ) ENGINE=InnoDB;

    -- Insert encryption keys for tenant {tenant_id}
    INSERT INTO tenant_encryption_keys (tenant_id, key_name, key_value) VALUES
    ('{tenant_id}', 'master', UNHEX(SHA2(CONCAT('{tenant_id}', 'master', NOW()), 256))),
    ('{tenant_id}', 'column', UNHEX(SHA2(CONCAT('{tenant_id}', 'column', NOW()), 256))),
    ('{tenant_id}', 'backup', UNHEX(SHA2(CONCAT('{tenant_id}', 'backup', NOW()), 256))),
    ('{tenant_id}', 'audit', UNHEX(SHA2(CONCAT('{tenant_id}', 'audit', NOW()), 256)))
    ON DUPLICATE KEY UPDATE
        key_value = VALUES(key_value),
        updated_at = CURRENT_TIMESTAMP;

    -- Create encrypted columns for sensitive data
    ALTER TABLE tenant_config
    ADD COLUMN IF NOT EXISTS encrypted_settings TEXT AFTER settings,
    ADD COLUMN IF NOT EXISTS pii_data_encrypted TEXT AFTER tenant_id;

    -- Create audit table for data access
    CREATE TABLE IF NOT EXISTS tenant_{tenant_id.replace('-', '_')}_data_audit (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tenant_id VARCHAR(100) NOT NULL,
        table_name VARCHAR(100) NOT NULL,
        operation_type ENUM('SELECT', 'INSERT', 'UPDATE', 'DELETE') NOT NULL,
        column_name VARCHAR(100),
        user_id VARCHAR(100),
        access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT,
        data_classification ENUM('PUBLIC', 'INTERNAL', 'CONFIDENTIAL', 'RESTRICTED') DEFAULT 'INTERNAL',
        INDEX idx_tenant_operation (tenant_id, operation_type),
        INDEX idx_access_time (access_time),
        INDEX idx_data_classification (data_classification)
    ) ENGINE=InnoDB;

    -- Create triggers for automatic encryption
    DELIMITER $$

    CREATE TRIGGER tenant_{tenant_id.replace('-', '_')}_encrypt_before_insert
    BEFORE INSERT ON tenant_config
    FOR EACH ROW
    BEGIN
        IF NEW.tenant_id = '{tenant_id}' AND NEW.settings IS NOT NULL THEN
            SET NEW.encrypted_settings = tenant_{tenant_id.replace('-', '_')}_encrypt(NEW.settings, 'column');
        END IF;
    END$$

    CREATE TRIGGER tenant_{tenant_id.replace('-', '_')}_encrypt_before_update
    BEFORE UPDATE ON tenant_config
    FOR EACH ROW
    BEGIN
        IF NEW.tenant_id = '{tenant_id}' AND NEW.settings IS NOT NULL THEN
            SET NEW.encrypted_settings = tenant_{tenant_id.replace('-', '_')}_encrypt(NEW.settings, 'column');
        END IF;
    END$$

    DELIMITER ;
---
apiVersion: batch/v1
kind: Job
metadata:
  name: tenant-{tenant_id}-db-encryption-setup
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/job: "db-encryption"
spec:
  template:
    spec:
      serviceAccountName: tenant-{tenant_id}-db-admin
      containers:
      - name: db-encryption-setup
        image: mysql:8.0
        command: ["/bin/sh", "-c"]
        args:
        - |
          echo "Setting up database encryption for tenant {tenant_id}"

          # Wait for database to be ready
          until mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1"; do
            echo "Waiting for database..."
            sleep 5
          done

          # Apply encryption configuration
          mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME < /config/encryption-config.sql

          echo "Database encryption setup completed for tenant {tenant_id}"
        env:
        - name: DB_HOST
          value: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        - name: DB_PORT
          value: "3306"
        - name: DB_USER
          value: "admin"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: production-rds-credentials
              key: password
        - name: DB_NAME
          value: "architrave"
        volumeMounts:
        - name: encryption-config
          mountPath: /config
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
      volumes:
      - name: encryption-config
        configMap:
          name: tenant-{tenant_id}-db-encryption-config
      restartPolicy: OnFailure
"""
        return db_encryption

    def create_backup_encryption_config(self, tenant_id: str) -> str:
        """Create backup encryption configuration."""
        backup_encryption = f"""apiVersion: v1
kind: Secret
metadata:
  name: tenant-{tenant_id}-backup-encryption
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/encryption: "backup"
type: Opaque
data:
  backup-master-key: {base64.b64encode(Fernet.generate_key()).decode()}
  s3-encryption-key: {base64.b64encode(Fernet.generate_key()).decode()}
  archive-key: {base64.b64encode(Fernet.generate_key()).decode()}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-{tenant_id}-encrypted-backup
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/backup: "encrypted"
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-{tenant_id}-backup
          containers:
          - name: encrypted-backup
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/backup-encryption:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Starting encrypted backup for tenant {tenant_id}"

              # Create encrypted database backup
              mysqldump -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD \\
                --single-transaction --routines --triggers \\
                --where="tenant_id='{tenant_id}'" \\
                $DB_NAME > /tmp/tenant-{tenant_id}-backup.sql

              # Encrypt the backup
              python3 /app/encrypt_backup.py \\
                --input-file /tmp/tenant-{tenant_id}-backup.sql \\
                --output-file /tmp/tenant-{tenant_id}-backup-encrypted.sql.enc \\
                --tenant-id {tenant_id} \\
                --encryption-key-secret tenant-{tenant_id}-backup-encryption

              # Upload to S3 with server-side encryption
              aws s3 cp /tmp/tenant-{tenant_id}-backup-encrypted.sql.enc \\
                s3://tenant-{tenant_id}-backups/$(date +%Y/%m/%d)/backup-$(date +%H%M%S).sql.enc \\
                --server-side-encryption aws:kms \\
                --ssekms-key-id alias/tenant-{tenant_id}-backup-key \\
                --metadata tenant-id={tenant_id},backup-type=encrypted,created-at=$(date -Iseconds)

              # Create backup manifest
              cat > /tmp/backup-manifest.json << EOF
              {{
                "tenant_id": "{tenant_id}",
                "backup_date": "$(date -Iseconds)",
                "backup_type": "encrypted",
                "encryption_algorithm": "AES-256-GCM",
                "s3_location": "s3://tenant-{tenant_id}-backups/$(date +%Y/%m/%d)/backup-$(date +%H%M%S).sql.enc",
                "checksum": "$(sha256sum /tmp/tenant-{tenant_id}-backup-encrypted.sql.enc | cut -d' ' -f1)",
                "size_bytes": $(stat -c%s /tmp/tenant-{tenant_id}-backup-encrypted.sql.enc)
              }}
              EOF

              # Upload manifest
              aws s3 cp /tmp/backup-manifest.json \\
                s3://tenant-{tenant_id}-backups/$(date +%Y/%m/%d)/manifest-$(date +%H%M%S).json

              echo "Encrypted backup completed for tenant {tenant_id}"
            env:
            - name: DB_HOST
              value: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
            - name: DB_PORT
              value: "3306"
            - name: DB_USER
              value: "admin"
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: production-rds-credentials
                  key: password
            - name: DB_NAME
              value: "architrave"
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
            volumeMounts:
            - name: tmp-volume
              mountPath: /tmp
            securityContext:
              allowPrivilegeEscalation: false
              runAsUser: 33
              runAsGroup: 33
              runAsNonRoot: true
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
          volumes:
          - name: tmp-volume
            emptyDir: {{}}
          restartPolicy: OnFailure
"""
        return backup_encryption

    def create_data_classification_config(self, tenant_id: str) -> str:
        """Create data classification and labeling configuration."""
        data_classification = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-data-classification
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/data-classification: "true"
data:
  classification-rules.yaml: |
    # Data Classification Rules for Tenant {tenant_id}

    classification_levels:
      - name: "PUBLIC"
        level: 1
        description: "Information that can be freely shared"
        retention_days: 2555  # 7 years
        encryption_required: false
        access_logging: false

      - name: "INTERNAL"
        level: 2
        description: "Information for internal use only"
        retention_days: 1825  # 5 years
        encryption_required: true
        access_logging: true

      - name: "CONFIDENTIAL"
        level: 3
        description: "Sensitive business information"
        retention_days: 1095  # 3 years
        encryption_required: true
        access_logging: true
        masking_required: true

      - name: "RESTRICTED"
        level: 4
        description: "Highly sensitive information (PII, financial)"
        retention_days: 365   # 1 year
        encryption_required: true
        access_logging: true
        masking_required: true
        approval_required: true

    data_types:
      - name: "personal_data"
        classification: "RESTRICTED"
        patterns:
          - "email"
          - "phone"
          - "address"
          - "ssn"
          - "passport"
        encryption_algorithm: "AES-256-GCM"

      - name: "financial_data"
        classification: "RESTRICTED"
        patterns:
          - "credit_card"
          - "bank_account"
          - "iban"
          - "payment"
        encryption_algorithm: "AES-256-GCM"

      - name: "business_data"
        classification: "CONFIDENTIAL"
        patterns:
          - "contract"
          - "pricing"
          - "strategy"
        encryption_algorithm: "AES-256-CBC"

      - name: "operational_data"
        classification: "INTERNAL"
        patterns:
          - "logs"
          - "metrics"
          - "configuration"
        encryption_algorithm: "AES-128-CBC"

    access_controls:
      PUBLIC:
        - role: "user"
          permissions: ["read"]
        - role: "admin"
          permissions: ["read", "write"]

      INTERNAL:
        - role: "employee"
          permissions: ["read"]
        - role: "manager"
          permissions: ["read", "write"]
        - role: "admin"
          permissions: ["read", "write", "delete"]

      CONFIDENTIAL:
        - role: "manager"
          permissions: ["read"]
          approval_required: false
        - role: "senior_manager"
          permissions: ["read", "write"]
          approval_required: true
        - role: "admin"
          permissions: ["read", "write", "delete"]
          approval_required: true

      RESTRICTED:
        - role: "data_officer"
          permissions: ["read"]
          approval_required: true
          mfa_required: true
        - role: "admin"
          permissions: ["read", "write"]
          approval_required: true
          mfa_required: true
          audit_required: true

  classification-scanner.py: |
    #!/usr/bin/env python3
    import re
    import json
    import yaml
    from typing import Dict, List, Tuple

    class DataClassificationScanner:
        def __init__(self, config_file: str):
            with open(config_file, 'r') as f:
                self.config = yaml.safe_load(f)

        def classify_data(self, data: str, context: str = "") -> Dict:
            """Classify data based on patterns and context."""
            classification_result = {{
                "classification": "PUBLIC",
                "confidence": 0.0,
                "data_types": [],
                "recommendations": []
            }}

            max_level = 0
            detected_types = []

            for data_type in self.config['data_types']:
                for pattern in data_type['patterns']:
                    if re.search(pattern, data, re.IGNORECASE):
                        detected_types.append(data_type['name'])
                        level = self._get_classification_level(data_type['classification'])
                        if level > max_level:
                            max_level = level
                            classification_result['classification'] = data_type['classification']

            classification_result['data_types'] = detected_types
            classification_result['confidence'] = min(len(detected_types) * 0.3, 1.0)

            # Add recommendations
            if classification_result['classification'] in ['CONFIDENTIAL', 'RESTRICTED']:
                classification_result['recommendations'].append("Enable encryption")
                classification_result['recommendations'].append("Enable access logging")

            if classification_result['classification'] == 'RESTRICTED':
                classification_result['recommendations'].append("Enable data masking")
                classification_result['recommendations'].append("Require approval for access")

            return classification_result

        def _get_classification_level(self, classification: str) -> int:
            for level_config in self.config['classification_levels']:
                if level_config['name'] == classification:
                    return level_config['level']
            return 0
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-data-classifier
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/data-classifier: "true"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-{tenant_id}-data-classifier
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-data-classifier
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-data-classifier
      containers:
      - name: data-classifier
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/data-classifier:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TENANT_ID
          value: {tenant_id}
        - name: CONFIG_FILE
          value: /config/classification-rules.yaml
        volumeMounts:
        - name: classification-config
          mountPath: /config
        - name: scanner-script
          mountPath: /app/scanner
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: classification-config
        configMap:
          name: tenant-{tenant_id}-data-classification
          items:
          - key: classification-rules.yaml
            path: classification-rules.yaml
      - name: scanner-script
        configMap:
          name: tenant-{tenant_id}-data-classification
          items:
          - key: classification-scanner.py
            path: classification-scanner.py
            mode: 0755
"""
        return data_classification

    def create_pii_protection_config(self, tenant_id: str) -> str:
        """Create PII protection and masking configuration."""
        pii_protection = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-pii-protection
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/pii-protection: "true"
data:
  pii-masking-rules.json: |
    {{
      "tenant_id": "{tenant_id}",
      "masking_rules": [
        {{
          "field_name": "email",
          "pattern": "([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\\.[a-zA-Z]{{2,}})",
          "mask_type": "email",
          "replacement": "****@$2"
        }},
        {{
          "field_name": "phone",
          "pattern": "(\\+?[1-9]\\d{{1,14}})",
          "mask_type": "phone",
          "replacement": "***-***-****"
        }},
        {{
          "field_name": "ssn",
          "pattern": "(\\d{{3}}-?\\d{{2}}-?\\d{{4}})",
          "mask_type": "ssn",
          "replacement": "***-**-****"
        }},
        {{
          "field_name": "credit_card",
          "pattern": "(\\d{{4}})\\s?(\\d{{4}})\\s?(\\d{{4}})\\s?(\\d{{4}})",
          "mask_type": "credit_card",
          "replacement": "****-****-****-$4"
        }},
        {{
          "field_name": "address",
          "pattern": "(\\d+\\s+[A-Za-z\\s]+)",
          "mask_type": "address",
          "replacement": "[REDACTED ADDRESS]"
        }}
      ],
      "anonymization_rules": [
        {{
          "field_name": "user_id",
          "method": "hash",
          "algorithm": "sha256",
          "salt": "tenant-{tenant_id}-salt"
        }},
        {{
          "field_name": "ip_address",
          "method": "subnet_mask",
          "mask_bits": 24
        }},
        {{
          "field_name": "timestamp",
          "method": "time_bucket",
          "bucket_size": "1h"
        }}
      ]
    }}

  pii-detector.py: |
    #!/usr/bin/env python3
    import re
    import json
    import hashlib
    from typing import Dict, List, Any

    class PIIDetector:
        def __init__(self, rules_file: str):
            with open(rules_file, 'r') as f:
                self.config = json.load(f)

        def detect_pii(self, text: str) -> List[Dict]:
            """Detect PII in text and return findings."""
            findings = []

            for rule in self.config['masking_rules']:
                pattern = rule['pattern']
                matches = re.finditer(pattern, text, re.IGNORECASE)

                for match in matches:
                    finding = {{
                        'field_type': rule['field_name'],
                        'start_pos': match.start(),
                        'end_pos': match.end(),
                        'matched_text': match.group(),
                        'confidence': 0.9,
                        'mask_type': rule['mask_type']
                    }}
                    findings.append(finding)

            return findings

        def mask_pii(self, text: str) -> str:
            """Mask PII in text."""
            masked_text = text

            for rule in self.config['masking_rules']:
                pattern = rule['pattern']
                replacement = rule['replacement']
                masked_text = re.sub(pattern, replacement, masked_text, flags=re.IGNORECASE)

            return masked_text

        def anonymize_data(self, data: Dict) -> Dict:
            """Anonymize data according to rules."""
            anonymized_data = data.copy()

            for rule in self.config['anonymization_rules']:
                field_name = rule['field_name']
                if field_name in anonymized_data:
                    value = anonymized_data[field_name]

                    if rule['method'] == 'hash':
                        salt = rule.get('salt', '')
                        hashed_value = hashlib.sha256((str(value) + salt).encode()).hexdigest()
                        anonymized_data[field_name] = hashed_value[:16]  # Truncate for readability

                    elif rule['method'] == 'subnet_mask':
                        # For IP addresses
                        if '.' in str(value):
                            parts = str(value).split('.')
                            if len(parts) == 4:
                                anonymized_data[field_name] = f"{{parts[0]}}.{{parts[1]}}.{{parts[2]}}.0"

                    elif rule['method'] == 'time_bucket':
                        # For timestamps - round to nearest hour
                        import datetime
                        if isinstance(value, str):
                            try:
                                dt = datetime.datetime.fromisoformat(value.replace('Z', '+00:00'))
                                rounded_dt = dt.replace(minute=0, second=0, microsecond=0)
                                anonymized_data[field_name] = rounded_dt.isoformat()
                            except:
                                pass

            return anonymized_data
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-pii-protector
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/pii-protector: "true"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-{tenant_id}-pii-protector
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-pii-protector
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-pii-protector
      containers:
      - name: pii-protector
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/pii-protector:latest
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: TENANT_ID
          value: {tenant_id}
        - name: RULES_FILE
          value: /config/pii-masking-rules.json
        volumeMounts:
        - name: pii-config
          mountPath: /config
        - name: detector-script
          mountPath: /app/detector
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: pii-config
        configMap:
          name: tenant-{tenant_id}-pii-protection
          items:
          - key: pii-masking-rules.json
            path: pii-masking-rules.json
      - name: detector-script
        configMap:
          name: tenant-{tenant_id}-pii-protection
          items:
          - key: pii-detector.py
            path: pii-detector.py
            mode: 0755
"""
        return pii_protection

    def apply_data_protection_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive data protection to tenant."""
        try:
            logger.info(f"🔐 Applying data protection and encryption for tenant-{tenant_id}")

            # 1. Create database encryption configuration
            db_encryption_config = self.create_database_encryption_config(tenant_id)

            # 2. Create backup encryption configuration
            backup_encryption_config = self.create_backup_encryption_config(tenant_id)

            # 3. Create data classification configuration
            classification_config = self.create_data_classification_config(tenant_id)

            # 4. Create PII protection configuration
            pii_protection_config = self.create_pii_protection_config(tenant_id)

            # Apply all configurations
            configs = [
                ("database-encryption", db_encryption_config),
                ("backup-encryption", backup_encryption_config),
                ("data-classification", classification_config),
                ("pii-protection", pii_protection_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            # Create necessary service accounts and RBAC
            logger.info(f"🔧 Setting up data protection service accounts for tenant-{tenant_id}")
            try:
                rbac_yaml = f"""apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-db-admin
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "db-admin"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-backup
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "backup"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-data-classifier
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "data-classifier"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-pii-protector
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "pii-protector"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-{tenant_id}-data-protection
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/role: "data-protection"
rules:
- apiGroups: [""]
  resources: ["secrets", "configmaps"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: ["batch"]
  resources: ["jobs", "cronjobs"]
  verbs: ["get", "list", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-{tenant_id}-data-protection
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/role-binding: "data-protection"
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-db-admin
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-backup
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-data-classifier
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-pii-protector
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: tenant-{tenant_id}-data-protection
  apiGroup: rbac.authorization.k8s.io
"""

                with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                    f.write(rbac_yaml)
                    temp_file = f.name

                result = run_command(f"kubectl apply -f {temp_file}", check=False)
                os.unlink(temp_file)

                if "error" not in result.lower():
                    logger.info(f"✅ Applied data protection RBAC for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Failed to apply data protection RBAC: {result}")

            except Exception as e:
                logger.warning(f"Failed to apply data protection RBAC: {e}")

            # Verify data protection components
            logger.info(f"🔍 Verifying data protection components for tenant-{tenant_id}")
            try:
                # Check if data classifier is running
                classifier_check = run_command(
                    f"kubectl get pods -n tenant-{tenant_id} -l app=tenant-{tenant_id}-data-classifier --no-headers",
                    check=False
                )

                if "Running" in classifier_check:
                    logger.info(f"✅ Data classifier is running for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Data classifier not running for tenant-{tenant_id}: {classifier_check}")

                # Check if PII protector is running
                pii_check = run_command(
                    f"kubectl get pods -n tenant-{tenant_id} -l app=tenant-{tenant_id}-pii-protector --no-headers",
                    check=False
                )

                if "Running" in pii_check:
                    logger.info(f"✅ PII protector is running for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ PII protector not running for tenant-{tenant_id}: {pii_check}")

                # Check if backup encryption job exists
                backup_check = run_command(
                    f"kubectl get cronjob -n tenant-{tenant_id} tenant-{tenant_id}-encrypted-backup --no-headers",
                    check=False
                )

                if backup_check and "No resources found" not in backup_check:
                    logger.info(f"✅ Encrypted backup job configured for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Encrypted backup job not found for tenant-{tenant_id}")

            except Exception as e:
                logger.warning(f"Data protection verification failed: {e}")

            logger.info(f"✅ Data protection setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply data protection for tenant {tenant_id}: {e}")
            return False
