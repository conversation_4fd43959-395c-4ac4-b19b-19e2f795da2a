#!/usr/bin/env python3
"""
Infrastructure Security Module
Implements node security, cluster hardening, supply chain security,
and dependency scanning for tenant infrastructure.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class InfrastructureSecurityManager:
    """Manages infrastructure security for tenant environments."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_node_security_config(self, tenant_id: str) -> str:
        """Create node security and hardening configuration."""
        node_security = f"""apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: tenant-{tenant_id}-node-security
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/node-security: "true"
spec:
  selector:
    matchLabels:
      app: tenant-{tenant_id}-node-security
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-node-security
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-node-security
      hostNetwork: true
      hostPID: true
      containers:
      - name: node-hardening
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/node-hardening:latest
        command: ["/bin/sh", "-c"]
        args:
        - |
          echo "Starting node security hardening for tenant {tenant_id}"

          # Apply CIS benchmarks
          /app/apply_cis_benchmarks.sh

          # Configure kernel parameters
          sysctl -w net.ipv4.ip_forward=0
          sysctl -w net.ipv4.conf.all.send_redirects=0
          sysctl -w net.ipv4.conf.default.send_redirects=0
          sysctl -w net.ipv4.conf.all.accept_source_route=0
          sysctl -w net.ipv4.conf.default.accept_source_route=0
          sysctl -w net.ipv4.conf.all.accept_redirects=0
          sysctl -w net.ipv4.conf.default.accept_redirects=0
          sysctl -w net.ipv4.conf.all.secure_redirects=0
          sysctl -w net.ipv4.conf.default.secure_redirects=0
          sysctl -w net.ipv4.conf.all.log_martians=1
          sysctl -w net.ipv4.conf.default.log_martians=1
          sysctl -w net.ipv4.icmp_echo_ignore_broadcasts=1
          sysctl -w net.ipv4.icmp_ignore_bogus_error_responses=1
          sysctl -w net.ipv4.conf.all.rp_filter=1
          sysctl -w net.ipv4.conf.default.rp_filter=1
          sysctl -w net.ipv4.tcp_syncookies=1

          # Configure file permissions
          chmod 600 /host/etc/ssh/sshd_config
          chmod 644 /host/etc/passwd
          chmod 644 /host/etc/group
          chmod 600 /host/etc/shadow
          chmod 600 /host/etc/gshadow

          # Monitor for changes
          while true; do
            /app/monitor_node_security.sh
            sleep 300
          done
        securityContext:
          privileged: true
        volumeMounts:
        - name: host-root
          mountPath: /host
        - name: host-proc
          mountPath: /host/proc
        - name: host-sys
          mountPath: /host/sys
        env:
        - name: TENANT_ID
          value: {tenant_id}
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
      volumes:
      - name: host-root
        hostPath:
          path: /
      - name: host-proc
        hostPath:
          path: /proc
      - name: host-sys
        hostPath:
          path: /sys
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        effect: NoSchedule
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-node-security-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/node-config: "true"
data:
  cis-benchmarks.sh: |
    #!/bin/bash
    # CIS Kubernetes Benchmark implementation for tenant {tenant_id}

    echo "Applying CIS benchmarks for tenant {tenant_id}"

    # 1.1.1 Ensure that the API server pod specification file permissions are set to 644 or more restrictive
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      chmod 644 /host/etc/kubernetes/manifests/kube-apiserver.yaml
    fi

    # 1.1.2 Ensure that the API server pod specification file ownership is set to root:root
    if [ -f /host/etc/kubernetes/manifests/kube-apiserver.yaml ]; then
      chown root:root /host/etc/kubernetes/manifests/kube-apiserver.yaml
    fi

    # 2.1.1 Ensure that the kubelet service file permissions are set to 644 or more restrictive
    if [ -f /host/etc/systemd/system/kubelet.service ]; then
      chmod 644 /host/etc/systemd/system/kubelet.service
    fi

    # 2.1.2 Ensure that the kubelet service file ownership is set to root:root
    if [ -f /host/etc/systemd/system/kubelet.service ]; then
      chown root:root /host/etc/systemd/system/kubelet.service
    fi

    # 4.1.1 Ensure that the kubelet service file permissions are set to 644 or more restrictive
    if [ -f /host/var/lib/kubelet/config.yaml ]; then
      chmod 644 /host/var/lib/kubelet/config.yaml
    fi

    # 4.1.2 Ensure that the kubelet service file ownership is set to root:root
    if [ -f /host/var/lib/kubelet/config.yaml ]; then
      chown root:root /host/var/lib/kubelet/config.yaml
    fi

    echo "CIS benchmarks applied successfully"

  security-monitoring.sh: |
    #!/bin/bash
    # Security monitoring script for tenant {tenant_id}

    echo "Running security monitoring for tenant {tenant_id} on node $NODE_NAME"

    # Check for unauthorized processes
    ps aux | grep -E "(bitcoin|monero|xmrig|cpuminer)" | grep -v grep && echo "ALERT: Crypto mining detected"

    # Check for suspicious network connections
    netstat -tulpn | grep -E ":22|:23|:3389" | grep -v "127.0.0.1" && echo "ALERT: Suspicious network connections"

    # Check file integrity
    find /host/etc -name "*.conf" -newer /tmp/last_check 2>/dev/null && echo "ALERT: Configuration files modified"

    # Update timestamp
    touch /tmp/last_check

    echo "Security monitoring completed"
"""
        return node_security

    def create_supply_chain_security_config(self, tenant_id: str) -> str:
        """Create supply chain security configuration."""
        supply_chain = f"""apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-{tenant_id}-supply-chain-scan
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/supply-chain: "true"
spec:
  schedule: "0 4 * * *"  # Daily at 4 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-{tenant_id}-supply-chain-scanner
          containers:
          - name: supply-chain-scanner
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/supply-chain-scanner:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Starting supply chain security scan for tenant {tenant_id}"

              # Scan container images for vulnerabilities
              trivy image --format json --output /tmp/image-scan.json \\
                ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test

              trivy image --format json --output /tmp/nginx-scan.json \\
                ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl

              trivy image --format json --output /tmp/rabbitmq-scan.json \\
                ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02

              # Verify image signatures with Cosign
              cosign verify --key /certs/cosign.pub \\
                ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test || echo "WARNING: Image signature verification failed"

              # Scan for dependency vulnerabilities
              syft ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test -o json > /tmp/sbom.json
              grype sbom:/tmp/sbom.json -o json > /tmp/dependency-scan.json

              # Generate supply chain report
              python3 /app/generate_supply_chain_report.py \\
                --tenant-id {tenant_id} \\
                --image-scan /tmp/image-scan.json \\
                --nginx-scan /tmp/nginx-scan.json \\
                --rabbitmq-scan /tmp/rabbitmq-scan.json \\
                --dependency-scan /tmp/dependency-scan.json \\
                --sbom /tmp/sbom.json \\
                --output /tmp/supply-chain-report.json

              # Upload to S3
              aws s3 cp /tmp/supply-chain-report.json \\
                s3://tenant-{tenant_id}-security/supply-chain/$(date +%Y/%m/%d)/report-$(date +%H%M%S).json

              echo "Supply chain security scan completed for tenant {tenant_id}"
            env:
            - name: TENANT_ID
              value: {tenant_id}
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
            volumeMounts:
            - name: tmp-volume
              mountPath: /tmp
            - name: cosign-certs
              mountPath: /certs
            securityContext:
              allowPrivilegeEscalation: false
              runAsUser: 33
              runAsGroup: 33
              runAsNonRoot: true
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
          volumes:
          - name: tmp-volume
            emptyDir: {{}}
          - name: cosign-certs
            secret:
              secretName: tenant-{tenant_id}-cosign-certs
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-supply-chain-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/supply-chain-config: "true"
data:
  allowed-registries.yaml: |
    # Allowed container registries for tenant {tenant_id}
    allowed_registries:
    - "************.dkr.ecr.eu-central-1.amazonaws.com"
    - "docker.io/library"  # Official Docker images only
    - "gcr.io/distroless"  # Distroless images
    - "quay.io/prometheus"  # Prometheus images

    blocked_registries:
    - "docker.io/*"  # Block all non-official Docker Hub images
    - "*.amazonaws.com"  # Block other AWS accounts
    - "ghcr.io/*"  # Block GitHub Container Registry

    signature_verification:
      required: true
      public_key_path: "/certs/cosign.pub"
      trusted_signers:
      - "<EMAIL>"

  vulnerability-policy.yaml: |
    # Vulnerability policy for tenant {tenant_id}
    vulnerability_policy:
      max_critical: 0
      max_high: 2
      max_medium: 10
      max_low: 50

      block_on_policy_violation: true

      exceptions:
        # Temporary exceptions (should be reviewed regularly)
        - cve: "CVE-2023-12345"
          reason: "No fix available, mitigated by network policies"
          expires: "2024-12-31"
          approved_by: "<EMAIL>"
"""
        return supply_chain

    def apply_infrastructure_security_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive infrastructure security to tenant."""
        try:
            logger.info(f"🏗️ Applying infrastructure security for tenant-{tenant_id}")

            # 1. Create node security configuration
            node_security_config = self.create_node_security_config(tenant_id)

            # 2. Create supply chain security configuration
            supply_chain_config = self.create_supply_chain_security_config(tenant_id)

            # Apply all configurations
            configs = [
                ("node-security", node_security_config),
                ("supply-chain-security", supply_chain_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            # Create necessary service accounts and RBAC
            logger.info(f"🔧 Setting up infrastructure security service accounts for tenant-{tenant_id}")
            try:
                rbac_yaml = f"""apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-node-security
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "node-security"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-supply-chain-scanner
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "supply-chain-scanner"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-{tenant_id}-infrastructure-security
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role: "infrastructure-security"
rules:
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["daemonsets"]
  verbs: ["get", "list", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-{tenant_id}-infrastructure-security
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role-binding: "infrastructure-security"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-{tenant_id}-infrastructure-security
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-node-security
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-supply-chain-scanner
  namespace: tenant-{tenant_id}
"""

                with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                    f.write(rbac_yaml)
                    temp_file = f.name

                result = run_command(f"kubectl apply -f {temp_file}", check=False)
                os.unlink(temp_file)

                if "error" not in result.lower():
                    logger.info(f"✅ Applied infrastructure security RBAC for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Failed to apply infrastructure security RBAC: {result}")

            except Exception as e:
                logger.warning(f"Failed to apply infrastructure security RBAC: {e}")

            logger.info(f"✅ Infrastructure security setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply infrastructure security for tenant {tenant_id}: {e}")
            return False
