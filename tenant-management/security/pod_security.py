#!/usr/bin/env python3
"""
Pod Security Standards Compliance Module
Implements comprehensive Pod Security Standards compliance with security contexts,
read-only root filesystem, non-root users, and capability dropping.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class PodSecurityManager:
    """Manages Pod Security Standards compliance for tenant workloads."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_restricted_security_context(self, tenant_id: str) -> Dict[str, Any]:
        """Create restricted security context configuration."""
        return {
            "runAsUser": 33,  # www-data user
            "runAsGroup": 33,  # www-data group
            "runAsNonRoot": True,
            "fsGroup": 33,
            "seccompProfile": {
                "type": "RuntimeDefault"
            },
            "supplementalGroups": [33]
        }

    def create_container_security_context(self, container_type: str = "default") -> Dict[str, Any]:
        """Create container-level security context."""
        base_context = {
            "allowPrivilegeEscalation": False,
            "runAsUser": 33,
            "runAsGroup": 33,
            "runAsNonRoot": True,
            "readOnlyRootFilesystem": True,
            "capabilities": {
                "drop": ["ALL"]
            }
        }

        # Adjust for specific container types
        if container_type == "init":
            # Init containers may need some write access
            base_context["readOnlyRootFilesystem"] = False
            base_context["capabilities"]["add"] = ["CHOWN", "FOWNER"]
        elif container_type == "nginx":
            # Nginx needs to bind to port 80
            base_context["capabilities"]["add"] = ["NET_BIND_SERVICE"]

        return base_context

    def create_pod_security_policy(self, tenant_id: str) -> str:
        """Create Pod Security Policy for tenant namespace."""
        psp_yaml = f"""apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: tenant-{tenant_id}-restricted-psp
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/policy-type: "restricted"
  annotations:
    seccomp.security.alpha.kubernetes.io/allowedProfileNames: 'runtime/default'
    seccomp.security.alpha.kubernetes.io/defaultProfileName: 'runtime/default'
    apparmor.security.beta.kubernetes.io/allowedProfileNames: 'runtime/default'
    apparmor.security.beta.kubernetes.io/defaultProfileName: 'runtime/default'
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  allowedCapabilities:
    - NET_BIND_SERVICE  # For nginx
    - CHOWN            # For init containers
    - FOWNER           # For init containers
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
    - 'csi'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAs'
    ranges:
      - min: 33
        max: 33
  runAsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 33
        max: 33
  seLinux:
    rule: 'RunAsAny'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 33
        max: 33
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 33
        max: 33
  readOnlyRootFilesystem: true
  allowedHostPaths: []
  allowedFlexVolumes: []
  allowedUnsafeSysctls: []
  forbiddenSysctls:
    - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: tenant-{tenant_id}-psp-user
  namespace: tenant-{tenant_id}
rules:
- apiGroups: ['policy']
  resources: ['podsecuritypolicies']
  verbs: ['use']
  resourceNames:
  - tenant-{tenant_id}-restricted-psp
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-{tenant_id}-psp-binding
  namespace: tenant-{tenant_id}
subjects:
- kind: ServiceAccount
  name: default
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: tenant-{tenant_id}-psp-user
  apiGroup: rbac.authorization.k8s.io
"""
        return psp_yaml

    def create_namespace_security_labels(self, tenant_id: str) -> str:
        """Create namespace with proper Pod Security Standards labels."""
        namespace_yaml = f"""apiVersion: v1
kind: Namespace
metadata:
  name: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
    security.architrave.io/compliance: "pod-security-standards"
  annotations:
    pod-security.kubernetes.io/enforce-version: "latest"
    pod-security.kubernetes.io/audit-version: "latest"
    pod-security.kubernetes.io/warn-version: "latest"
"""
        return namespace_yaml

    def create_security_context_constraints(self, tenant_id: str) -> str:
        """Create Security Context Constraints for OpenShift compatibility."""
        scc_yaml = f"""apiVersion: security.openshift.io/v1
kind: SecurityContextConstraints
metadata:
  name: tenant-{tenant_id}-restricted-scc
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/type: "scc"
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities:
- NET_BIND_SERVICE
- CHOWN
- FOWNER
defaultAddCapabilities: []
requiredDropCapabilities:
- ALL
allowedFlexVolumes: []
allowedUnsafeSysctls: []
forbiddenSysctls:
- "*"
fsGroup:
  type: MustRunAs
  ranges:
  - min: 33
    max: 33
readOnlyRootFilesystem: true
runAsUser:
  type: MustRunAs
  uid: 33
seLinuxContext:
  type: MustRunAs
supplementalGroups:
  type: MustRunAs
  ranges:
  - min: 33
    max: 33
volumes:
- configMap
- downwardAPI
- emptyDir
- persistentVolumeClaim
- projected
- secret
- csi
users:
- system:serviceaccount:tenant-{tenant_id}:default
- system:serviceaccount:tenant-{tenant_id}:tenant-{tenant_id}-backend
- system:serviceaccount:tenant-{tenant_id}:tenant-{tenant_id}-frontend
groups: []
priority: 10
"""
        return scc_yaml

    def create_admission_controller_config(self, tenant_id: str) -> str:
        """Create admission controller configuration for Pod Security Standards."""
        admission_config = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: pod-security-admission-config-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/admission-controller: "true"
data:
  admission-config.yaml: |
    apiVersion: apiserver.config.k8s.io/v1
    kind: AdmissionConfiguration
    plugins:
    - name: PodSecurity
      configuration:
        apiVersion: pod-security.admission.config.k8s.io/v1beta1
        kind: PodSecurityConfiguration
        defaults:
          enforce: "restricted"
          enforce-version: "latest"
          audit: "restricted"
          audit-version: "latest"
          warn: "restricted"
          warn-version: "latest"
        exemptions:
          usernames: []
          runtimeClasses: []
          namespaces: []
---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: tenant-{tenant_id}-security-validator
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/webhook: "true"
webhooks:
- name: tenant-{tenant_id}-security.architrave.com
  clientConfig:
    service:
      name: tenant-security-webhook
      namespace: security-system
      path: "/validate/tenant-{tenant_id}"
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["apps"]
    apiVersions: ["v1"]
    resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  namespaceSelector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail
"""
        return admission_config

    def patch_deployment_security_context(self, tenant_id: str, deployment_name: str,
                                        deployment_type: str = "backend") -> str:
        """Create security context patch for existing deployments."""

        # Get appropriate security contexts
        pod_security_context = self.create_restricted_security_context(tenant_id)
        container_security_context = self.create_container_security_context(deployment_type)

        # Create volumes for read-only root filesystem
        volumes = []
        volume_mounts = []

        if deployment_type == "backend":
            volumes = [
                {
                    "name": "tmp-volume",
                    "emptyDir": {}
                },
                {
                    "name": "var-log-volume",
                    "emptyDir": {}
                },
                {
                    "name": "var-cache-volume",
                    "emptyDir": {}
                },
                {
                    "name": "storage-volume",
                    "emptyDir": {}
                }
            ]
            volume_mounts = [
                {
                    "name": "tmp-volume",
                    "mountPath": "/tmp"
                },
                {
                    "name": "var-log-volume",
                    "mountPath": "/var/log"
                },
                {
                    "name": "var-cache-volume",
                    "mountPath": "/var/cache"
                },
                {
                    "name": "storage-volume",
                    "mountPath": "/storage"
                }
            ]
        elif deployment_type == "frontend":
            volumes = [
                {
                    "name": "nginx-cache-volume",
                    "emptyDir": {}
                },
                {
                    "name": "nginx-run-volume",
                    "emptyDir": {}
                }
            ]
            volume_mounts = [
                {
                    "name": "nginx-cache-volume",
                    "mountPath": "/var/cache/nginx"
                },
                {
                    "name": "nginx-run-volume",
                    "mountPath": "/var/run"
                }
            ]

        patch_yaml = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: {deployment_name}
  namespace: tenant-{tenant_id}
spec:
  template:
    spec:
      securityContext:
        runAsUser: {pod_security_context['runAsUser']}
        runAsGroup: {pod_security_context['runAsGroup']}
        runAsNonRoot: {str(pod_security_context['runAsNonRoot']).lower()}
        fsGroup: {pod_security_context['fsGroup']}
        seccompProfile:
          type: {pod_security_context['seccompProfile']['type']}
      volumes:
{chr(10).join(f'      - name: {vol["name"]}' + chr(10) + f'        emptyDir: {{}}' for vol in volumes)}
      containers:
      - name: {deployment_type}
        securityContext:
          allowPrivilegeEscalation: {str(container_security_context['allowPrivilegeEscalation']).lower()}
          runAsUser: {container_security_context['runAsUser']}
          runAsGroup: {container_security_context['runAsGroup']}
          runAsNonRoot: {str(container_security_context['runAsNonRoot']).lower()}
          readOnlyRootFilesystem: {str(container_security_context['readOnlyRootFilesystem']).lower()}
          capabilities:
            drop: {json.dumps(container_security_context['capabilities']['drop'])}
{f'            add: {json.dumps(container_security_context["capabilities"].get("add", []))}' if container_security_context["capabilities"].get("add") else ''}
        volumeMounts:
{chr(10).join(f'        - name: {vm["name"]}' + chr(10) + f'          mountPath: {vm["mountPath"]}' for vm in volume_mounts)}
"""
        return patch_yaml

    def create_resource_quotas(self, tenant_id: str) -> str:
        """Create resource quotas with security constraints."""
        quota_yaml = f"""apiVersion: v1
kind: ResourceQuota
metadata:
  name: tenant-{tenant_id}-security-quota
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/quota-type: "security"
spec:
  hard:
    # Pod Security Standards enforcement
    pods: "10"
    requests.cpu: "2"
    requests.memory: "4Gi"
    limits.cpu: "4"
    limits.memory: "8Gi"
    persistentvolumeclaims: "5"
    services: "5"
    secrets: "20"
    configmaps: "20"
    # Security-specific quotas
    count/pods.security.restricted: "10"
    count/secrets.tls: "5"
    count/secrets.opaque: "15"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: tenant-{tenant_id}-security-limits
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/limit-type: "security"
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
      ephemeral-storage: "100Mi"
    max:
      cpu: "2"
      memory: "4Gi"
      ephemeral-storage: "10Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
      ephemeral-storage: "50Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Pod
"""
        return quota_yaml

    def apply_pod_security_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive Pod Security Standards to tenant."""
        try:
            logger.info(f"🛡️ Applying Pod Security Standards compliance for tenant-{tenant_id}")

            # 1. Create namespace with security labels
            namespace_config = self.create_namespace_security_labels(tenant_id)

            # 2. Create Pod Security Policy
            psp_config = self.create_pod_security_policy(tenant_id)

            # 3. Create Security Context Constraints (OpenShift compatibility)
            scc_config = self.create_security_context_constraints(tenant_id)

            # 4. Create admission controller configuration
            admission_config = self.create_admission_controller_config(tenant_id)

            # 5. Create resource quotas with security constraints
            quota_config = self.create_resource_quotas(tenant_id)

            # 6. Create security patches for deployments
            backend_patch = self.patch_deployment_security_context(
                tenant_id, f"tenant-{tenant_id}-backend", "backend"
            )
            frontend_patch = self.patch_deployment_security_context(
                tenant_id, f"tenant-{tenant_id}-frontend", "frontend"
            )

            # Apply all configurations
            configs = [
                ("namespace-security", namespace_config),
                ("pod-security-policy", psp_config),
                ("security-context-constraints", scc_config),
                ("admission-controller", admission_config),
                ("resource-quotas", quota_config),
                ("backend-security-patch", backend_patch),
                ("frontend-security-patch", frontend_patch)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    if "patch" in config_name:
                        # Use strategic merge patch for deployments
                        result = run_command(
                            f"kubectl patch deployment {config_name.split('-')[0]}-{tenant_id}-{config_name.split('-')[1]} "
                            f"-n tenant-{tenant_id} --patch-file {temp_file}",
                            check=False
                        )
                    else:
                        result = run_command(f"kubectl apply -f {temp_file}", check=False)

                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            logger.info(f"✅ Pod Security Standards compliance completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply Pod Security Standards for tenant {tenant_id}: {e}")
            return False
