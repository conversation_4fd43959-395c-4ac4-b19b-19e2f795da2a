#!/usr/bin/env python3
"""
Runtime Security & Monitoring Module
Implements runtime protection with Falco rules, admission controllers,
OPA Gatekeeper policies, and security benchmarks.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class RuntimeSecurityManager:
    """Manages runtime security monitoring and protection for tenants."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_falco_rules(self, tenant_id: str) -> str:
        """Create Falco rules for runtime monitoring."""
        falco_rules = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-falco-rules
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/falco-rules: "true"
data:
  tenant_rules.yaml: |
    # Tenant-specific Falco rules for runtime security

    # Rule: Detect privilege escalation attempts
    - rule: Tenant {tenant_id} Privilege Escalation
      desc: Detect privilege escalation in tenant {tenant_id}
      condition: >
        spawned_process and
        k8s.ns.name = "tenant-{tenant_id}" and
        (proc.name in (sudo, su, doas) or
         proc.args contains "chmod +s" or
         proc.args contains "setuid")
      output: >
        Privilege escalation detected in tenant {tenant_id}
        (user=%user.name command=%proc.cmdline container=%container.name
         image=%container.image.repository:%container.image.tag)
      priority: CRITICAL
      tags: [tenant-{tenant_id}, privilege_escalation, security]

    # Rule: Detect suspicious file access
    - rule: Tenant {tenant_id} Suspicious File Access
      desc: Detect access to sensitive files in tenant {tenant_id}
      condition: >
        open_read and
        k8s.ns.name = "tenant-{tenant_id}" and
        (fd.name startswith /etc/passwd or
         fd.name startswith /etc/shadow or
         fd.name startswith /etc/ssh/ or
         fd.name startswith /root/.ssh/ or
         fd.name contains "id_rsa" or
         fd.name contains "authorized_keys")
      output: >
        Suspicious file access in tenant {tenant_id}
        (user=%user.name file=%fd.name command=%proc.cmdline
         container=%container.name image=%container.image.repository:%container.image.tag)
      priority: HIGH
      tags: [tenant-{tenant_id}, file_access, security]

    # Rule: Detect network anomalies
    - rule: Tenant {tenant_id} Unexpected Network Connection
      desc: Detect unexpected network connections from tenant {tenant_id}
      condition: >
        inbound_outbound and
        k8s.ns.name = "tenant-{tenant_id}" and
        not (fd.rip in (10.0.0.0/8, **********/12, ***********/16)) and
        not (fd.rip in ("***************")) and
        not (proc.name in (curl, wget, apt, yum, pip))
      output: >
        Unexpected network connection from tenant {tenant_id}
        (user=%user.name connection=%fd.name command=%proc.cmdline
         container=%container.name image=%container.image.repository:%container.image.tag)
      priority: MEDIUM
      tags: [tenant-{tenant_id}, network, security]

    # Rule: Detect container escape attempts
    - rule: Tenant {tenant_id} Container Escape Attempt
      desc: Detect container escape attempts in tenant {tenant_id}
      condition: >
        spawned_process and
        k8s.ns.name = "tenant-{tenant_id}" and
        (proc.name in (docker, kubectl, crictl, runc, ctr) or
         proc.args contains "/proc/self/root" or
         proc.args contains "nsenter" or
         proc.args contains "chroot")
      output: >
        Container escape attempt detected in tenant {tenant_id}
        (user=%user.name command=%proc.cmdline container=%container.name
         image=%container.image.repository:%container.image.tag)
      priority: CRITICAL
      tags: [tenant-{tenant_id}, container_escape, security]

    # Rule: Detect crypto mining
    - rule: Tenant {tenant_id} Crypto Mining Detection
      desc: Detect potential crypto mining in tenant {tenant_id}
      condition: >
        spawned_process and
        k8s.ns.name = "tenant-{tenant_id}" and
        (proc.name in (xmrig, minerd, cpuminer, cgminer, bfgminer) or
         proc.args contains "stratum+tcp" or
         proc.args contains "mining.pool" or
         proc.args contains "cryptonight")
      output: >
        Crypto mining detected in tenant {tenant_id}
        (user=%user.name command=%proc.cmdline container=%container.name
         image=%container.image.repository:%container.image.tag)
      priority: CRITICAL
      tags: [tenant-{tenant_id}, crypto_mining, security]
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: tenant-{tenant_id}-falco
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/falco: "enabled"
spec:
  selector:
    matchLabels:
      app: tenant-{tenant_id}-falco
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-falco
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-falco
      hostNetwork: true
      hostPID: true
      containers:
      - name: falco
        image: falcosecurity/falco:latest
        securityContext:
          privileged: true
        args:
        - /usr/bin/falco
        - --cri=/run/containerd/containerd.sock
        - --k8s-api=https://kubernetes.default.svc.cluster.local
        - --k8s-api-cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        - --k8s-api-token=/var/run/secrets/kubernetes.io/serviceaccount/token
        - -r
        - /etc/falco/tenant_rules.yaml
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
        - mountPath: /host/run/containerd/containerd.sock
          name: containerd-socket
        - mountPath: /host/dev
          name: dev-fs
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /etc/falco/tenant_rules.yaml
          name: tenant-rules
          subPath: tenant_rules.yaml
        env:
        - name: FALCO_GRPC_ENABLED
          value: "true"
        - name: FALCO_GRPC_BIND_ADDRESS
          value: "0.0.0.0:5060"
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: containerd-socket
        hostPath:
          path: /run/containerd/containerd.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: tenant-rules
        configMap:
          name: tenant-{tenant_id}-falco-rules
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
      - effect: NoSchedule
        key: node-role.kubernetes.io/control-plane
"""
        return falco_rules

    def create_opa_gatekeeper_policies(self, tenant_id: str) -> str:
        """Create OPA Gatekeeper policies for admission control."""
        gatekeeper_policies = f"""apiVersion: templates.gatekeeper.sh/v1beta1
kind: ConstraintTemplate
metadata:
  name: tenant{tenant_id.replace('-', '')}securitypolicy
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/gatekeeper: "true"
spec:
  crd:
    spec:
      names:
        kind: Tenant{tenant_id.replace('-', '').title()}SecurityPolicy
      validation:
        type: object
        properties:
          allowedImages:
            type: array
            items:
              type: string
          requiredLabels:
            type: array
            items:
              type: string
          maxReplicas:
            type: integer
  targets:
    - target: admission.k8s.gatekeeper.sh
      rego: |
        package tenant{tenant_id.replace('-', '')}security

        violation[{{"msg": msg}}] {{
          input.review.object.kind == "Pod"
          input.review.object.metadata.namespace == "tenant-{tenant_id}"

          # Check for allowed images
          container := input.review.object.spec.containers[_]
          not image_allowed(container.image)
          msg := sprintf("Image %v is not allowed for tenant {tenant_id}", [container.image])
        }}

        violation[{{"msg": msg}}] {{
          input.review.object.kind == "Pod"
          input.review.object.metadata.namespace == "tenant-{tenant_id}"

          # Check for required labels
          required_label := input.parameters.requiredLabels[_]
          not input.review.object.metadata.labels[required_label]
          msg := sprintf("Required label %v is missing for tenant {tenant_id}", [required_label])
        }}

        violation[{{"msg": msg}}] {{
          input.review.object.kind == "Deployment"
          input.review.object.metadata.namespace == "tenant-{tenant_id}"

          # Check replica limits
          input.review.object.spec.replicas > input.parameters.maxReplicas
          msg := sprintf("Replica count %v exceeds maximum %v for tenant {tenant_id}",
                        [input.review.object.spec.replicas, input.parameters.maxReplicas])
        }}

        violation[{{"msg": msg}}] {{
          input.review.object.kind == "Pod"
          input.review.object.metadata.namespace == "tenant-{tenant_id}"

          # Check security context
          container := input.review.object.spec.containers[_]
          container.securityContext.privileged == true
          msg := sprintf("Privileged containers are not allowed for tenant {tenant_id}")
        }}

        image_allowed(image) {{
          allowed_image := input.parameters.allowedImages[_]
          startswith(image, allowed_image)
        }}
---
apiVersion: config.gatekeeper.sh/v1alpha1
kind: Tenant{tenant_id.replace('-', '').title()}SecurityPolicy
metadata:
  name: tenant-{tenant_id}-security-policy
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/policy: "gatekeeper"
spec:
  match:
    - apiGroups: [""]
      kinds: ["Pod"]
      namespaces: ["tenant-{tenant_id}"]
    - apiGroups: ["apps"]
      kinds: ["Deployment", "ReplicaSet"]
      namespaces: ["tenant-{tenant_id}"]
  parameters:
    allowedImages:
    - "************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev"
    - "************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev"
    - "************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev"
    requiredLabels:
    - "tenant.architrave.io/tenant-id"
    - "app.kubernetes.io/name"
    maxReplicas: 5
"""
        return gatekeeper_policies

    def create_admission_controllers(self, tenant_id: str) -> str:
        """Create custom admission controllers for tenant security."""
        admission_controller = f"""apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: tenant-{tenant_id}-security-validator
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/admission-controller: "true"
webhooks:
- name: tenant-{tenant_id}-security.architrave.com
  clientConfig:
    service:
      name: tenant-{tenant_id}-security-webhook
      namespace: tenant-{tenant_id}
      path: "/validate"
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods", "services", "configmaps", "secrets"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["apps"]
    apiVersions: ["v1"]
    resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["networking.k8s.io"]
    apiVersions: ["v1"]
    resources: ["networkpolicies", "ingresses"]
  namespaceSelector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-security-webhook
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/webhook: "true"
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-{tenant_id}-security-webhook
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-security-webhook
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-security-webhook
      containers:
      - name: webhook
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/security-webhook:latest
        ports:
        - containerPort: 8443
          name: webhook-api
        env:
        - name: TENANT_ID
          value: {tenant_id}
        - name: TLS_CERT_FILE
          value: /etc/webhook/certs/tls.crt
        - name: TLS_PRIVATE_KEY_FILE
          value: /etc/webhook/certs/tls.key
        volumeMounts:
        - name: webhook-certs
          mountPath: /etc/webhook/certs
          readOnly: true
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
      volumes:
      - name: webhook-certs
        secret:
          secretName: tenant-{tenant_id}-webhook-certs
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-security-webhook
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/webhook-service: "true"
spec:
  selector:
    app: tenant-{tenant_id}-security-webhook
  ports:
  - name: webhook-api
    port: 443
    targetPort: webhook-api
    protocol: TCP
"""
        return admission_controller

    def create_security_benchmarks(self, tenant_id: str) -> str:
        """Create CIS Kubernetes security benchmarks compliance."""
        benchmarks = f"""apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-{tenant_id}-security-benchmark
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/benchmark: "cis"
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-{tenant_id}-benchmark
          containers:
          - name: kube-bench
            image: aquasec/kube-bench:latest
            command: ["kube-bench"]
            args:
            - --benchmark
            - cis-1.6
            - --json
            - --outputfile
            - /tmp/results.json
            - --targets
            - node,policies,managedservices
            env:
            - name: TENANT_ID
              value: {tenant_id}
            volumeMounts:
            - name: results-volume
              mountPath: /tmp
            securityContext:
              allowPrivilegeEscalation: false
              runAsUser: 33
              runAsGroup: 33
              runAsNonRoot: true
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
          - name: results-processor
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/security-processor:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Processing security benchmark results for tenant {tenant_id}"

              # Wait for kube-bench to complete
              while [ ! -f /tmp/results.json ]; do
                sleep 5
              done

              # Process results and send alerts if needed
              python3 /app/process_benchmark_results.py \\
                --tenant-id {tenant_id} \\
                --results-file /tmp/results.json \\
                --alert-webhook https://alerts.architrave.local/webhook
            volumeMounts:
            - name: results-volume
              mountPath: /tmp
            securityContext:
              allowPrivilegeEscalation: false
              runAsUser: 33
              runAsGroup: 33
              runAsNonRoot: true
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
          volumes:
          - name: results-volume
            emptyDir: {{}}
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-benchmark-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/benchmark-config: "true"
data:
  benchmark-config.yaml: |
    # CIS Kubernetes Benchmark Configuration for Tenant {tenant_id}
    controls:
      # 1. Master Node Security Configuration
      - id: "1.1.1"
        description: "Ensure that the API server pod specification file permissions are set to 644 or more restrictive"
        type: "master"

      - id: "1.1.2"
        description: "Ensure that the API server pod specification file ownership is set to root:root"
        type: "master"

      # 2. Worker Node Security Configuration
      - id: "2.1.1"
        description: "Ensure that the kubelet service file permissions are set to 644 or more restrictive"
        type: "node"

      # 3. Control Plane Configuration
      - id: "3.1.1"
        description: "Client certificate authentication should not be used for users"
        type: "controlplane"

      # 4. Worker Node Configuration
      - id: "4.1.1"
        description: "Ensure that the kubelet service file permissions are set to 644 or more restrictive"
        type: "node"

      # 5. Kubernetes Policies
      - id: "5.1.1"
        description: "Ensure that the cluster-admin role is only used where required"
        type: "policies"
        tenant_specific: true

      - id: "5.1.3"
        description: "Minimize wildcard use in Roles and ClusterRoles"
        type: "policies"
        tenant_specific: true

      - id: "5.2.2"
        description: "Minimize the admission of containers with allowPrivilegeEscalation"
        type: "policies"
        tenant_specific: true

      - id: "5.3.2"
        description: "Minimize the admission of containers wishing to share the host process ID namespace"
        type: "policies"
        tenant_specific: true

      - id: "5.7.3"
        description: "Apply Security Context to Your Pods and Containers"
        type: "policies"
        tenant_specific: true

      - id: "5.7.4"
        description: "The default namespace should not be used"
        type: "policies"
        tenant_specific: true

    # Tenant-specific compliance requirements
    tenant_requirements:
      - name: "Pod Security Standards"
        level: "restricted"
        mandatory: true

      - name: "Network Policies"
        type: "deny-all-default"
        mandatory: true

      - name: "Resource Quotas"
        limits:
          cpu: "4"
          memory: "8Gi"
          pods: "10"
        mandatory: true

      - name: "Security Contexts"
        runAsNonRoot: true
        readOnlyRootFilesystem: true
        allowPrivilegeEscalation: false
        mandatory: true
"""
        return benchmarks

    def apply_runtime_security_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive runtime security to tenant."""
        try:
            logger.info(f"🛡️ Applying runtime security monitoring for tenant-{tenant_id}")

            # 1. Create Falco rules for runtime monitoring
            falco_config = self.create_falco_rules(tenant_id)

            # 2. Create OPA Gatekeeper policies
            gatekeeper_config = self.create_opa_gatekeeper_policies(tenant_id)

            # 3. Create admission controllers
            admission_config = self.create_admission_controllers(tenant_id)

            # 4. Create security benchmarks
            benchmark_config = self.create_security_benchmarks(tenant_id)

            # Apply all configurations
            configs = [
                ("falco-runtime-monitoring", falco_config),
                ("gatekeeper-policies", gatekeeper_config),
                ("admission-controllers", admission_config),
                ("security-benchmarks", benchmark_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            # Create necessary service accounts and RBAC
            logger.info(f"🔧 Setting up service accounts and RBAC for tenant-{tenant_id}")
            try:
                rbac_yaml = f"""apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-falco
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "falco"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-security-webhook
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "webhook"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-benchmark
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "benchmark"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-{tenant_id}-falco
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role: "falco"
rules:
- apiGroups: [""]
  resources: ["nodes", "namespaces", "pods", "replicationcontrollers", "services", "events"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["replicasets", "daemonsets", "deployments"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["replicasets", "daemonsets", "deployments"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-{tenant_id}-falco
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role-binding: "falco"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-{tenant_id}-falco
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-falco
  namespace: tenant-{tenant_id}
"""

                with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                    f.write(rbac_yaml)
                    temp_file = f.name

                result = run_command(f"kubectl apply -f {temp_file}", check=False)
                os.unlink(temp_file)

                if "error" not in result.lower():
                    logger.info(f"✅ Applied RBAC configuration for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Failed to apply RBAC: {result}")

            except Exception as e:
                logger.warning(f"Failed to apply RBAC configuration: {e}")

            # Verify runtime security components
            logger.info(f"🔍 Verifying runtime security components for tenant-{tenant_id}")
            try:
                # Check if Falco is running
                falco_check = run_command(
                    f"kubectl get pods -n tenant-{tenant_id} -l app=tenant-{tenant_id}-falco --no-headers",
                    check=False
                )

                if "Running" in falco_check:
                    logger.info(f"✅ Falco is running for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Falco not running for tenant-{tenant_id}: {falco_check}")

                # Check if Gatekeeper constraints are active
                gatekeeper_check = run_command(
                    f"kubectl get constraints -n tenant-{tenant_id} --no-headers",
                    check=False
                )

                if gatekeeper_check and "No resources found" not in gatekeeper_check:
                    logger.info(f"✅ Gatekeeper constraints active for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ No Gatekeeper constraints found for tenant-{tenant_id}")

            except Exception as e:
                logger.warning(f"Runtime security verification failed: {e}")

            logger.info(f"✅ Runtime security setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply runtime security for tenant {tenant_id}: {e}")
            return False
