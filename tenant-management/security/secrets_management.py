#!/usr/bin/env python3
"""
Advanced Secrets Management & Encryption Module
Implements enterprise-grade secrets management with encryption at rest,
secret rotation, sealed secrets, and certificate management.
"""

import os
import json
import base64
import time
import logging
import tempfile
import hashlib
import secrets
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

# Use built-in crypto instead of cryptography for now
try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import rsa
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography import x509
    from cryptography.x509.oid import NameOID
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

logger = logging.getLogger(__name__)

class EncryptionManager:
    """Manages encryption keys and operations for tenant data."""

    def __init__(self):
        self.master_key = self._get_or_create_master_key()

    def _get_or_create_master_key(self) -> bytes:
        """Get or create master encryption key."""
        try:
            # Try to get from environment or AWS KMS
            master_key_b64 = os.environ.get('MASTER_ENCRYPTION_KEY')
            if master_key_b64:
                return base64.b64decode(master_key_b64)

            # Generate new master key
            if CRYPTOGRAPHY_AVAILABLE:
                master_key = Fernet.generate_key()
            else:
                # Fallback to simple key generation
                master_key = base64.urlsafe_b64encode(os.urandom(32))
            logger.warning("Generated new master key - store securely!")
            return master_key
        except Exception as e:
            logger.error(f"Failed to get master key: {e}")
            raise

    def generate_tenant_keys(self, tenant_id: str) -> Dict[str, str]:
        """Generate encryption keys for a tenant."""
        try:
            # Generate different keys for different purposes
            if CRYPTOGRAPHY_AVAILABLE:
                keys = {
                    'data_encryption_key': Fernet.generate_key().decode(),
                    'database_encryption_key': Fernet.generate_key().decode(),
                    's3_encryption_key': Fernet.generate_key().decode(),
                    'backup_encryption_key': Fernet.generate_key().decode(),
                    'session_encryption_key': Fernet.generate_key().decode(),
                    'created_at': datetime.now().isoformat(),
                    'tenant_id': tenant_id
                }
            else:
                # Fallback key generation
                keys = {
                    'data_encryption_key': base64.urlsafe_b64encode(os.urandom(32)).decode(),
                    'database_encryption_key': base64.urlsafe_b64encode(os.urandom(32)).decode(),
                    's3_encryption_key': base64.urlsafe_b64encode(os.urandom(32)).decode(),
                    'backup_encryption_key': base64.urlsafe_b64encode(os.urandom(32)).decode(),
                    'session_encryption_key': base64.urlsafe_b64encode(os.urandom(32)).decode(),
                    'created_at': datetime.now().isoformat(),
                    'tenant_id': tenant_id
                }

            logger.info(f"Generated encryption keys for tenant {tenant_id}")
            return keys
        except Exception as e:
            logger.error(f"Failed to generate keys for tenant {tenant_id}: {e}")
            raise

    def encrypt_data(self, data: str, key: str) -> str:
        """Encrypt data using provided key."""
        try:
            if CRYPTOGRAPHY_AVAILABLE:
                f = Fernet(key.encode() if isinstance(key, str) else key)
                encrypted_data = f.encrypt(data.encode())
                return base64.b64encode(encrypted_data).decode()
            else:
                # Simple base64 encoding as fallback (not secure, for demo only)
                return base64.b64encode(data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to encrypt data: {e}")
            raise

    def decrypt_data(self, encrypted_data: str, key: str) -> str:
        """Decrypt data using provided key."""
        try:
            if CRYPTOGRAPHY_AVAILABLE:
                f = Fernet(key.encode() if isinstance(key, str) else key)
                decoded_data = base64.b64decode(encrypted_data.encode())
                decrypted_data = f.decrypt(decoded_data)
                return decrypted_data.decode()
            else:
                # Simple base64 decoding as fallback (not secure, for demo only)
                return base64.b64decode(encrypted_data.encode()).decode()
        except Exception as e:
            logger.error(f"Failed to decrypt data: {e}")
            raise

class SecretsManager:
    """Manages secrets lifecycle including creation, rotation, and sealed secrets."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func
        self.encryption_manager = EncryptionManager()

    def create_sealed_secret(self, tenant_id: str, secret_data: Dict[str, str],
                           secret_name: str) -> str:
        """Create a sealed secret for GitOps workflow."""
        try:
            # Create regular secret first
            secret_yaml = self._create_secret_yaml(tenant_id, secret_data, secret_name)

            # Convert to sealed secret using kubeseal
            with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                f.write(secret_yaml)
                temp_file = f.name

            try:
                # Create sealed secret
                sealed_secret = self.run_command(
                    f"kubeseal --format=yaml --cert=sealed-secrets-public.pem < {temp_file}",
                    check=False
                )

                if "error" not in sealed_secret.lower():
                    logger.info(f"Created sealed secret {secret_name} for tenant {tenant_id}")
                    return sealed_secret
                else:
                    logger.warning(f"Kubeseal not available, using regular secret for {secret_name}")
                    return secret_yaml

            finally:
                os.unlink(temp_file)

        except Exception as e:
            logger.error(f"Failed to create sealed secret: {e}")
            # Fallback to regular secret
            return self._create_secret_yaml(tenant_id, secret_data, secret_name)

    def _create_secret_yaml(self, tenant_id: str, secret_data: Dict[str, str],
                           secret_name: str) -> str:
        """Create regular Kubernetes secret YAML."""
        # Encode secret data
        encoded_data = {}
        for key, value in secret_data.items():
            encoded_data[key] = base64.b64encode(value.encode()).decode()

        secret_yaml = f"""apiVersion: v1
kind: Secret
metadata:
  name: {secret_name}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    secrets.architrave.io/managed: "true"
    secrets.architrave.io/type: "tenant-credentials"
  annotations:
    secrets.architrave.io/created-at: "{datetime.utcnow().isoformat()}"
    secrets.architrave.io/rotation-schedule: "30d"
type: Opaque
data:
{chr(10).join(f'  {key}: {value}' for key, value in encoded_data.items())}
"""
        return secret_yaml

    def setup_secret_rotation(self, tenant_id: str) -> str:
        """Set up automated secret rotation for tenant."""
        rotation_cronjob = f"""apiVersion: batch/v1
kind: CronJob
metadata:
  name: secret-rotation-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    secrets.architrave.io/rotation: "true"
spec:
  schedule: "0 2 1 * *"  # Monthly at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: secret-rotator
          containers:
          - name: secret-rotator
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/secret-rotator:latest
            env:
            - name: TENANT_ID
              value: "{tenant_id}"
            - name: ROTATION_TYPE
              value: "database-credentials"
            command:
            - /bin/sh
            - -c
            - |
              echo "Rotating secrets for tenant {tenant_id}"
              # Implement secret rotation logic here
              python3 /app/rotate_secrets.py --tenant-id {tenant_id}
          restartPolicy: OnFailure
"""
        return rotation_cronjob

    def create_certificate_secret(self, tenant_id: str, cert_type: str = "tls") -> str:
        """Create TLS certificate secret for tenant."""
        try:
            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )

            # Generate certificate
            subject = issuer = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "CA"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "San Francisco"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Architrave"),
                x509.NameAttribute(NameOID.COMMON_NAME, f"tenant-{tenant_id}.architrave.local"),
            ])

            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                issuer
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                datetime.utcnow()
            ).not_valid_after(
                datetime.utcnow() + timedelta(days=365)
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName(f"tenant-{tenant_id}.architrave.local"),
                    x509.DNSName(f"*.tenant-{tenant_id}.svc.cluster.local"),
                ]),
                critical=False,
            ).sign(private_key, hashes.SHA256())

            # Serialize certificate and key
            cert_pem = cert.public_bytes(serialization.Encoding.PEM)
            key_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )

            # Create certificate secret
            cert_data = {
                'tls.crt': base64.b64encode(cert_pem).decode(),
                'tls.key': base64.b64encode(key_pem).decode()
            }

            cert_secret = f"""apiVersion: v1
kind: Secret
metadata:
  name: tenant-{tenant_id}-{cert_type}-cert
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    certificates.architrave.io/type: "{cert_type}"
type: kubernetes.io/tls
data:
  tls.crt: {cert_data['tls.crt']}
  tls.key: {cert_data['tls.key']}
"""

            logger.info(f"Generated {cert_type} certificate for tenant {tenant_id}")
            return cert_secret

        except Exception as e:
            logger.error(f"Failed to create certificate for tenant {tenant_id}: {e}")
            raise

    def setup_vault_integration(self, tenant_id: str) -> str:
        """Set up HashiCorp Vault integration for advanced secret management."""
        vault_config = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: vault-config-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    vault.architrave.io/integration: "true"
data:
  vault-config.hcl: |
    storage "consul" {{
      address = "consul.vault-system.svc.cluster.local:8500"
      path    = "vault/tenant-{tenant_id}/"
    }}

    listener "tcp" {{
      address     = "0.0.0.0:8200"
      tls_disable = false
      tls_cert_file = "/vault/tls/tls.crt"
      tls_key_file  = "/vault/tls/tls.key"
    }}

    seal "awskms" {{
      region     = "eu-central-1"
      kms_key_id = "alias/vault-tenant-{tenant_id}"
    }}

    ui = true

    # Tenant-specific mount
    path "tenant-{tenant_id}/*" {{
      capabilities = ["create", "read", "update", "delete", "list"]
    }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vault-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    vault.architrave.io/service-account: "true"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: vault-{tenant_id}
  namespace: tenant-{tenant_id}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: vault-{tenant_id}
  namespace: tenant-{tenant_id}
subjects:
- kind: ServiceAccount
  name: vault-{tenant_id}
  namespace: tenant-{tenant_id}
roleRef:
  kind: Role
  name: vault-{tenant_id}
  apiGroup: rbac.authorization.k8s.io
"""
        return vault_config

    def create_encryption_at_rest_config(self, tenant_id: str) -> str:
        """Create encryption at rest configuration for tenant data."""
        encryption_config = f"""apiVersion: v1
kind: Secret
metadata:
  name: encryption-config-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    encryption.architrave.io/type: "at-rest"
type: Opaque
data:
  encryption-key: {base64.b64encode(Fernet.generate_key()).decode()}
  kms-key-id: {base64.b64encode(f"alias/tenant-{tenant_id}-encryption".encode()).decode()}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: encryption-provider-config-{tenant_id}
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    encryption.architrave.io/provider: "true"
data:
  encryption-provider-config.yaml: |
    apiVersion: apiserver.config.k8s.io/v1
    kind: EncryptionConfiguration
    resources:
    - resources:
      - secrets
      - configmaps
      providers:
      - kms:
          name: tenant-{tenant_id}-kms
          endpoint: unix:///tmp/socketfile.sock
          cachesize: 100
          timeout: 3s
      - aescbc:
          keys:
          - name: key1
            secret: {base64.b64encode(os.urandom(32)).decode()}
      - identity: {{}}
"""
        return encryption_config

    def apply_secrets_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply all secret configurations to tenant namespace."""
        try:
            logger.info(f"🔐 Setting up comprehensive secrets management for tenant-{tenant_id}")

            # 1. Generate tenant encryption keys
            tenant_keys = self.encryption_manager.generate_tenant_keys(tenant_id)

            # 2. Create sealed secret for tenant keys
            sealed_secret = self.create_sealed_secret(
                tenant_id,
                tenant_keys,
                f"tenant-{tenant_id}-encryption-keys"
            )

            # 3. Create TLS certificate
            tls_cert = self.create_certificate_secret(tenant_id, "tls")

            # 4. Create mTLS certificate for service mesh
            mtls_cert = self.create_certificate_secret(tenant_id, "mtls")

            # 5. Set up secret rotation
            rotation_config = self.setup_secret_rotation(tenant_id)

            # 6. Set up Vault integration
            vault_config = self.setup_vault_integration(tenant_id)

            # 7. Set up encryption at rest
            encryption_config = self.create_encryption_at_rest_config(tenant_id)

            # Apply all configurations
            configs = [
                ("sealed-secret", sealed_secret),
                ("tls-certificate", tls_cert),
                ("mtls-certificate", mtls_cert),
                ("secret-rotation", rotation_config),
                ("vault-integration", vault_config),
                ("encryption-at-rest", encryption_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            logger.info(f"✅ Secrets management setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to set up secrets management for tenant {tenant_id}: {e}")
            return False
