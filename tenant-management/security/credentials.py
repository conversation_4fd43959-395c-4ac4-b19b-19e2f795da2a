#!/usr/bin/env python3
"""
Security Utilities for Credential Management
Centralized secure credential handling for all tenant management scripts
"""

import boto3
import json
import logging
import os
from typing import Dict, Optional
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)

class CredentialManager:
    """Secure credential management using AWS Secrets Manager"""
    
    def __init__(self, region_name: str = 'eu-central-1'):
        self.region_name = region_name
        self.secrets_client = boto3.client('secretsmanager', region_name=region_name)
        self._credential_cache = {}
    
    def get_rds_credentials(self, secret_name: str = "production/rds/credentials") -> Dict[str, str]:
        """
        Get RDS credentials from AWS Secrets Manager
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            
        Returns:
            Dictionary containing RDS credentials
            
        Raises:
            Exception: If credentials cannot be retrieved
        """
        if secret_name in self._credential_cache:
            logger.debug(f"Using cached credentials for {secret_name}")
            return self._credential_cache[secret_name]
        
        try:
            logger.info(f"Retrieving RDS credentials from Secrets Manager: {secret_name}")
            response = self.secrets_client.get_secret_value(SecretId=secret_name)
            credentials = json.loads(response['SecretString'])
            
            # Validate required fields
            required_fields = ['host', 'port', 'username', 'password']
            for field in required_fields:
                if field not in credentials:
                    raise ValueError(f"Missing required field '{field}' in credentials")
            
            # Cache credentials for this session
            self._credential_cache[secret_name] = credentials
            logger.info("Successfully retrieved and validated RDS credentials")
            
            return credentials
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'DecryptionFailureException':
                logger.error("Failed to decrypt the secret. Check KMS permissions.")
            elif error_code == 'InternalServiceErrorException':
                logger.error("Internal service error occurred.")
            elif error_code == 'InvalidParameterException':
                logger.error("Invalid parameter provided.")
            elif error_code == 'InvalidRequestException':
                logger.error("Invalid request.")
            elif error_code == 'ResourceNotFoundException':
                logger.error(f"Secret '{secret_name}' not found.")
            else:
                logger.error(f"Unexpected error: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse secret JSON: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error retrieving credentials: {e}")
            raise
    
    def get_database_connection_string(self, secret_name: str = "production/rds/credentials") -> str:
        """
        Get database connection string for MySQL
        
        Args:
            secret_name: Name of the secret in AWS Secrets Manager
            
        Returns:
            MySQL connection string
        """
        credentials = self.get_rds_credentials(secret_name)
        return f"mysql://{credentials['username']}:{credentials['password']}@{credentials['host']}:{credentials['port']}"
    
    def create_tenant_database_user(self, tenant_id: str, secret_name: str = "production/rds/credentials") -> Dict[str, str]:
        """
        Create database credentials for a tenant
        
        Args:
            tenant_id: Tenant identifier
            secret_name: Name of the RDS admin secret
            
        Returns:
            Dictionary containing tenant database credentials
        """
        import secrets
        import string
        
        # Generate secure password
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(16))
        
        # Create tenant-specific database user
        db_tenant_id = tenant_id.replace('-', '_')
        db_user = f"tenant_{db_tenant_id}"
        
        tenant_credentials = {
            'username': db_user,
            'password': password,
            'database': 'architrave',  # All tenants use the same database
            'tenant_id': tenant_id
        }
        
        # Store tenant credentials in Secrets Manager
        tenant_secret_name = f"tenant/{tenant_id}/database"
        try:
            self.secrets_client.create_secret(
                Name=tenant_secret_name,
                Description=f"Database credentials for tenant {tenant_id}",
                SecretString=json.dumps(tenant_credentials)
            )
            logger.info(f"Created database credentials secret for tenant {tenant_id}")
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceExistsException':
                # Update existing secret
                self.secrets_client.update_secret(
                    SecretId=tenant_secret_name,
                    SecretString=json.dumps(tenant_credentials)
                )
                logger.info(f"Updated database credentials secret for tenant {tenant_id}")
            else:
                raise
        
        return tenant_credentials
    
    def get_tenant_database_credentials(self, tenant_id: str) -> Dict[str, str]:
        """
        Get database credentials for a specific tenant
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary containing tenant database credentials
        """
        tenant_secret_name = f"tenant/{tenant_id}/database"
        try:
            response = self.secrets_client.get_secret_value(SecretId=tenant_secret_name)
            return json.loads(response['SecretString'])
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logger.error(f"Database credentials not found for tenant {tenant_id}")
                raise ValueError(f"No database credentials found for tenant {tenant_id}")
            else:
                raise
    
    def delete_tenant_credentials(self, tenant_id: str) -> bool:
        """
        Delete tenant credentials from Secrets Manager
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            True if successful, False otherwise
        """
        tenant_secret_name = f"tenant/{tenant_id}/database"
        try:
            self.secrets_client.delete_secret(
                SecretId=tenant_secret_name,
                ForceDeleteWithoutRecovery=True
            )
            logger.info(f"Deleted database credentials for tenant {tenant_id}")
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                logger.warning(f"Database credentials not found for tenant {tenant_id}")
                return True  # Already deleted
            else:
                logger.error(f"Failed to delete credentials for tenant {tenant_id}: {e}")
                return False
    
    def rotate_tenant_password(self, tenant_id: str) -> Dict[str, str]:
        """
        Rotate password for a tenant
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Dictionary containing new credentials
        """
        logger.info(f"Rotating password for tenant {tenant_id}")
        return self.create_tenant_database_user(tenant_id)
    
    def validate_credentials(self, credentials: Dict[str, str]) -> bool:
        """
        Validate that credentials contain required fields
        
        Args:
            credentials: Dictionary of credentials to validate
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ['host', 'port', 'username', 'password']
        for field in required_fields:
            if field not in credentials or not credentials[field]:
                logger.error(f"Missing or empty required field: {field}")
                return False
        return True

# Global credential manager instance
credential_manager = CredentialManager()

def get_rds_credentials(secret_name: str = "production/rds/credentials") -> Dict[str, str]:
    """Convenience function to get RDS credentials"""
    return credential_manager.get_rds_credentials(secret_name)

def get_tenant_credentials(tenant_id: str) -> Dict[str, str]:
    """Convenience function to get tenant credentials"""
    return credential_manager.get_tenant_database_credentials(tenant_id)

def create_tenant_credentials(tenant_id: str) -> Dict[str, str]:
    """Convenience function to create tenant credentials"""
    return credential_manager.create_tenant_database_user(tenant_id)
