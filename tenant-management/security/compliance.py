#!/usr/bin/env python3
"""
Compliance & Auditing Module
Implements audit logging, compliance scanning, GDPR/SOC2 compliance,
and security assessments for tenant environments.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class ComplianceManager:
    """Manages compliance and auditing for tenant environments."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_audit_logging_config(self, tenant_id: str) -> str:
        """Create comprehensive audit logging configuration."""
        audit_config = f"""apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-audit-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/audit-logging: "true"
data:
  audit-policy.yaml: |
    apiVersion: audit.k8s.io/v1
    kind: Policy
    rules:
    # Log all requests to tenant namespace
    - level: RequestResponse
      namespaces: ["tenant-{tenant_id}"]
      resources:
      - group: ""
        resources: ["*"]
      - group: "apps"
        resources: ["*"]
      - group: "networking.k8s.io"
        resources: ["*"]
      - group: "security.istio.io"
        resources: ["*"]

    # Log all secret and configmap access
    - level: Metadata
      resources:
      - group: ""
        resources: ["secrets", "configmaps"]
      namespaces: ["tenant-{tenant_id}"]

    # Log all RBAC changes
    - level: RequestResponse
      resources:
      - group: "rbac.authorization.k8s.io"
        resources: ["*"]
      namespaces: ["tenant-{tenant_id}"]

    # Log all security policy changes
    - level: RequestResponse
      resources:
      - group: "policy"
        resources: ["podsecuritypolicies"]
      - group: "networking.k8s.io"
        resources: ["networkpolicies"]
      namespaces: ["tenant-{tenant_id}"]

  fluentd-audit.conf: |
    <source>
      @type tail
      path /var/log/audit/audit.log
      pos_file /var/log/fluentd-audit.log.pos
      tag kubernetes.audit
      format json
      time_key timestamp
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>

    <filter kubernetes.audit>
      @type grep
      <regexp>
        key $.objectRef.namespace
        pattern ^tenant-{tenant_id}$
      </regexp>
    </filter>

    <filter kubernetes.audit>
      @type record_transformer
      <record>
        tenant_id {tenant_id}
        compliance_event true
        audit_timestamp ${{Time.now.iso8601}}
      </record>
    </filter>

    <match kubernetes.audit>
      @type elasticsearch
      host elasticsearch.monitoring.svc.cluster.local
      port 9200
      index_name tenant-{tenant_id}-audit-logs
      type_name audit
      include_tag_key true
      tag_key @log_name
      flush_interval 10s
    </match>
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: tenant-{tenant_id}-audit-logger
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/audit-logger: "true"
spec:
  selector:
    matchLabels:
      app: tenant-{tenant_id}-audit-logger
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-audit-logger
        tenant.architrave.io/tenant-id: {tenant_id}
    spec:
      serviceAccountName: tenant-{tenant_id}-audit-logger
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1-debian-elasticsearch
        env:
        - name: FLUENT_ELASTICSEARCH_HOST
          value: "elasticsearch.monitoring.svc.cluster.local"
        - name: FLUENT_ELASTICSEARCH_PORT
          value: "9200"
        - name: FLUENT_ELASTICSEARCH_SCHEME
          value: "http"
        - name: FLUENT_UID
          value: "0"
        volumeMounts:
        - name: audit-config
          mountPath: /fluentd/etc/fluent.conf
          subPath: fluentd-audit.conf
        - name: audit-logs
          mountPath: /var/log/audit
          readOnly: true
        - name: varlog
          mountPath: /var/log
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 0  # Required for log access
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
            add: ["DAC_OVERRIDE", "CHOWN"]
      volumes:
      - name: audit-config
        configMap:
          name: tenant-{tenant_id}-audit-config
      - name: audit-logs
        hostPath:
          path: /var/log/audit
      - name: varlog
        hostPath:
          path: /var/log
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      - key: node-role.kubernetes.io/control-plane
        effect: NoSchedule
"""
        return audit_config

    def create_compliance_scanner_config(self, tenant_id: str) -> str:
        """Create compliance scanning configuration."""
        compliance_scanner = f"""apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-{tenant_id}-compliance-scanner
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/compliance-scanner: "true"
spec:
  schedule: "0 6 * * 1"  # Weekly on Monday at 6 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-{tenant_id}-compliance-scanner
          containers:
          - name: compliance-scanner
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/compliance-scanner:latest
            command: ["/bin/sh", "-c"]
            args:
            - |
              echo "Starting compliance scan for tenant {tenant_id}"

              # GDPR Compliance Check
              python3 /app/gdpr_scanner.py \\
                --tenant-id {tenant_id} \\
                --namespace tenant-{tenant_id} \\
                --output-file /tmp/gdpr-compliance.json

              # SOC2 Compliance Check
              python3 /app/soc2_scanner.py \\
                --tenant-id {tenant_id} \\
                --namespace tenant-{tenant_id} \\
                --output-file /tmp/soc2-compliance.json

              # PCI DSS Compliance Check (if applicable)
              python3 /app/pci_scanner.py \\
                --tenant-id {tenant_id} \\
                --namespace tenant-{tenant_id} \\
                --output-file /tmp/pci-compliance.json

              # ISO 27001 Compliance Check
              python3 /app/iso27001_scanner.py \\
                --tenant-id {tenant_id} \\
                --namespace tenant-{tenant_id} \\
                --output-file /tmp/iso27001-compliance.json

              # Generate compliance report
              python3 /app/generate_compliance_report.py \\
                --tenant-id {tenant_id} \\
                --gdpr-file /tmp/gdpr-compliance.json \\
                --soc2-file /tmp/soc2-compliance.json \\
                --pci-file /tmp/pci-compliance.json \\
                --iso27001-file /tmp/iso27001-compliance.json \\
                --output-file /tmp/compliance-report.json

              # Upload to S3
              aws s3 cp /tmp/compliance-report.json \\
                s3://tenant-{tenant_id}-compliance/reports/$(date +%Y/%m/%d)/compliance-report-$(date +%H%M%S).json \\
                --metadata tenant-id={tenant_id},scan-date=$(date -Iseconds),scan-type=compliance

              echo "Compliance scan completed for tenant {tenant_id}"
            env:
            - name: TENANT_ID
              value: {tenant_id}
            - name: AWS_DEFAULT_REGION
              value: "eu-central-1"
            volumeMounts:
            - name: tmp-volume
              mountPath: /tmp
            - name: compliance-config
              mountPath: /config
            securityContext:
              allowPrivilegeEscalation: false
              runAsUser: 33
              runAsGroup: 33
              runAsNonRoot: true
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
          volumes:
          - name: tmp-volume
            emptyDir: {{}}
          - name: compliance-config
            configMap:
              name: tenant-{tenant_id}-compliance-config
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-compliance-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/compliance-config: "true"
data:
  gdpr-requirements.yaml: |
    # GDPR Compliance Requirements for Tenant {tenant_id}
    requirements:
      - name: "Data Protection by Design"
        description: "Implement data protection measures from the start"
        controls:
          - "Encryption at rest and in transit"
          - "Access controls and authentication"
          - "Data minimization principles"
        checks:
          - "verify_encryption_enabled"
          - "verify_access_controls"
          - "verify_data_retention_policies"

      - name: "Right to be Forgotten"
        description: "Ability to delete personal data upon request"
        controls:
          - "Data deletion procedures"
          - "Audit trail for deletions"
        checks:
          - "verify_deletion_capability"
          - "verify_deletion_audit_trail"

      - name: "Data Breach Notification"
        description: "Notify authorities within 72 hours of breach"
        controls:
          - "Incident response procedures"
          - "Automated alerting systems"
        checks:
          - "verify_incident_response_plan"
          - "verify_alerting_systems"

  soc2-requirements.yaml: |
    # SOC2 Compliance Requirements for Tenant {tenant_id}
    trust_criteria:
      security:
        - "Access controls are implemented"
        - "Logical and physical access is restricted"
        - "System operations are monitored"
      availability:
        - "System availability is maintained"
        - "Backup and recovery procedures exist"
      processing_integrity:
        - "System processing is complete and accurate"
        - "Data validation controls exist"
      confidentiality:
        - "Confidential information is protected"
        - "Encryption is implemented"
      privacy:
        - "Personal information is collected and used appropriately"
        - "Privacy notices are provided"
"""
        return compliance_scanner

    def apply_compliance_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive compliance and auditing to tenant."""
        try:
            logger.info(f"📋 Applying compliance and auditing for tenant-{tenant_id}")

            # 1. Create audit logging configuration
            audit_config = self.create_audit_logging_config(tenant_id)

            # 2. Create compliance scanner configuration
            scanner_config = self.create_compliance_scanner_config(tenant_id)

            # Apply all configurations
            configs = [
                ("audit-logging", audit_config),
                ("compliance-scanner", scanner_config)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            # Create necessary service accounts and RBAC
            logger.info(f"🔧 Setting up compliance service accounts for tenant-{tenant_id}")
            try:
                rbac_yaml = f"""apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-audit-logger
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "audit-logger"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-{tenant_id}-compliance-scanner
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-account: "compliance-scanner"
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-{tenant_id}-compliance-reader
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role: "compliance-reader"
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets", "namespaces"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  verbs: ["get", "list"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies", "ingresses"]
  verbs: ["get", "list"]
- apiGroups: ["policy"]
  resources: ["podsecuritypolicies"]
  verbs: ["get", "list"]
- apiGroups: ["rbac.authorization.k8s.io"]
  resources: ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-{tenant_id}-compliance-reader
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/cluster-role-binding: "compliance-reader"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-{tenant_id}-compliance-reader
subjects:
- kind: ServiceAccount
  name: tenant-{tenant_id}-compliance-scanner
  namespace: tenant-{tenant_id}
- kind: ServiceAccount
  name: tenant-{tenant_id}-audit-logger
  namespace: tenant-{tenant_id}
"""

                with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                    f.write(rbac_yaml)
                    temp_file = f.name

                result = run_command(f"kubectl apply -f {temp_file}", check=False)
                os.unlink(temp_file)

                if "error" not in result.lower():
                    logger.info(f"✅ Applied compliance RBAC for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ Failed to apply compliance RBAC: {result}")

            except Exception as e:
                logger.warning(f"Failed to apply compliance RBAC: {e}")

            logger.info(f"✅ Compliance and auditing setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply compliance for tenant {tenant_id}: {e}")
            return False
