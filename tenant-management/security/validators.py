#!/usr/bin/env python3
"""
Input Validation and Security Utilities
Comprehensive input validation to prevent injection attacks and ensure data integrity
"""

import re
import html
import logging
from typing import Optional, List, Dict, Any
from urllib.parse import quote

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors"""
    pass

class SecurityValidator:
    """Comprehensive input validation for security"""
    
    # Regex patterns for validation
    TENANT_ID_PATTERN = re.compile(r'^[a-z0-9-]{1,50}$')
    DATABASE_NAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]{1,64}$')
    USERNAME_PATTERN = re.compile(r'^[a-zA-Z0-9_]{1,32}$')
    EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    DOMAIN_PATTERN = re.compile(r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
    IP_PATTERN = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
    
    # Dangerous patterns to block
    SQL_INJECTION_PATTERNS = [
        re.compile(r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)', re.IGNORECASE),
        re.compile(r'(--|#|/\*|\*/)', re.IGNORECASE),
        re.compile(r'(\bOR\b|\bAND\b).*=.*', re.IGNORECASE),
        re.compile(r'[\'";]', re.IGNORECASE)
    ]
    
    COMMAND_INJECTION_PATTERNS = [
        re.compile(r'[;&|`$(){}[\]<>]'),
        re.compile(r'\b(rm|cat|ls|ps|kill|sudo|su|chmod|chown)\b', re.IGNORECASE),
        re.compile(r'\.\./', re.IGNORECASE)
    ]
    
    XSS_PATTERNS = [
        re.compile(r'<script[^>]*>.*?</script>', re.IGNORECASE | re.DOTALL),
        re.compile(r'javascript:', re.IGNORECASE),
        re.compile(r'on\w+\s*=', re.IGNORECASE),
        re.compile(r'<iframe[^>]*>', re.IGNORECASE)
    ]
    
    @staticmethod
    def validate_tenant_id(tenant_id: str) -> str:
        """
        Validate tenant ID format and security
        
        Args:
            tenant_id: Tenant identifier to validate
            
        Returns:
            Sanitized tenant ID
            
        Raises:
            ValidationError: If tenant ID is invalid
        """
        if not tenant_id:
            raise ValidationError("Tenant ID cannot be empty")
        
        if not isinstance(tenant_id, str):
            raise ValidationError("Tenant ID must be a string")
        
        # Check length
        if len(tenant_id) > 50:
            raise ValidationError("Tenant ID too long (max 50 characters)")
        
        # Check format
        if not SecurityValidator.TENANT_ID_PATTERN.match(tenant_id):
            raise ValidationError("Tenant ID must contain only lowercase letters, numbers, and hyphens")
        
        # Check for dangerous patterns
        SecurityValidator._check_injection_patterns(tenant_id, "Tenant ID")
        
        # Sanitize and return
        sanitized = html.escape(tenant_id)
        logger.debug(f"Validated tenant ID: {sanitized}")
        return sanitized
    
    @staticmethod
    def validate_database_name(db_name: str) -> str:
        """
        Validate database name format and security
        
        Args:
            db_name: Database name to validate
            
        Returns:
            Sanitized database name
            
        Raises:
            ValidationError: If database name is invalid
        """
        if not db_name:
            raise ValidationError("Database name cannot be empty")
        
        if not isinstance(db_name, str):
            raise ValidationError("Database name must be a string")
        
        # Check length
        if len(db_name) > 64:
            raise ValidationError("Database name too long (max 64 characters)")
        
        # Check format
        if not SecurityValidator.DATABASE_NAME_PATTERN.match(db_name):
            raise ValidationError("Database name must contain only letters, numbers, and underscores")
        
        # Check for dangerous patterns
        SecurityValidator._check_injection_patterns(db_name, "Database name")
        
        logger.debug(f"Validated database name: {db_name}")
        return db_name
    
    @staticmethod
    def validate_username(username: str) -> str:
        """
        Validate username format and security
        
        Args:
            username: Username to validate
            
        Returns:
            Sanitized username
            
        Raises:
            ValidationError: If username is invalid
        """
        if not username:
            raise ValidationError("Username cannot be empty")
        
        if not isinstance(username, str):
            raise ValidationError("Username must be a string")
        
        # Check length
        if len(username) > 32:
            raise ValidationError("Username too long (max 32 characters)")
        
        # Check format
        if not SecurityValidator.USERNAME_PATTERN.match(username):
            raise ValidationError("Username must contain only letters, numbers, and underscores")
        
        # Check for dangerous patterns
        SecurityValidator._check_injection_patterns(username, "Username")
        
        logger.debug(f"Validated username: {username}")
        return username
    
    @staticmethod
    def validate_email(email: str) -> str:
        """
        Validate email format
        
        Args:
            email: Email address to validate
            
        Returns:
            Sanitized email
            
        Raises:
            ValidationError: If email is invalid
        """
        if not email:
            raise ValidationError("Email cannot be empty")
        
        if not isinstance(email, str):
            raise ValidationError("Email must be a string")
        
        # Check length
        if len(email) > 254:
            raise ValidationError("Email too long (max 254 characters)")
        
        # Check format
        if not SecurityValidator.EMAIL_PATTERN.match(email):
            raise ValidationError("Invalid email format")
        
        # Check for dangerous patterns
        SecurityValidator._check_injection_patterns(email, "Email")
        
        sanitized = html.escape(email.lower())
        logger.debug(f"Validated email: {sanitized}")
        return sanitized
    
    @staticmethod
    def validate_domain(domain: str) -> str:
        """
        Validate domain name format
        
        Args:
            domain: Domain name to validate
            
        Returns:
            Sanitized domain
            
        Raises:
            ValidationError: If domain is invalid
        """
        if not domain:
            raise ValidationError("Domain cannot be empty")
        
        if not isinstance(domain, str):
            raise ValidationError("Domain must be a string")
        
        # Check length
        if len(domain) > 253:
            raise ValidationError("Domain too long (max 253 characters)")
        
        # Check format
        if not SecurityValidator.DOMAIN_PATTERN.match(domain):
            raise ValidationError("Invalid domain format")
        
        sanitized = domain.lower()
        logger.debug(f"Validated domain: {sanitized}")
        return sanitized
    
    @staticmethod
    def validate_ip_address(ip: str) -> str:
        """
        Validate IP address format
        
        Args:
            ip: IP address to validate
            
        Returns:
            Validated IP address
            
        Raises:
            ValidationError: If IP address is invalid
        """
        if not ip:
            raise ValidationError("IP address cannot be empty")
        
        if not isinstance(ip, str):
            raise ValidationError("IP address must be a string")
        
        # Check format
        if not SecurityValidator.IP_PATTERN.match(ip):
            raise ValidationError("Invalid IP address format")
        
        logger.debug(f"Validated IP address: {ip}")
        return ip
    
    @staticmethod
    def sanitize_sql_input(input_str: str) -> str:
        """
        Sanitize input for SQL queries
        
        Args:
            input_str: String to sanitize
            
        Returns:
            Sanitized string
            
        Raises:
            ValidationError: If input contains dangerous patterns
        """
        if not isinstance(input_str, str):
            raise ValidationError("Input must be a string")
        
        # Check for SQL injection patterns
        for pattern in SecurityValidator.SQL_INJECTION_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"Input contains potentially dangerous SQL pattern: {input_str}")
        
        # Escape special characters
        sanitized = input_str.replace("'", "''").replace('"', '""')
        return sanitized
    
    @staticmethod
    def sanitize_shell_input(input_str: str) -> str:
        """
        Sanitize input for shell commands
        
        Args:
            input_str: String to sanitize
            
        Returns:
            Sanitized string
            
        Raises:
            ValidationError: If input contains dangerous patterns
        """
        if not isinstance(input_str, str):
            raise ValidationError("Input must be a string")
        
        # Check for command injection patterns
        for pattern in SecurityValidator.COMMAND_INJECTION_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"Input contains potentially dangerous shell pattern: {input_str}")
        
        # Quote the input for shell safety
        sanitized = quote(input_str)
        return sanitized
    
    @staticmethod
    def sanitize_html_input(input_str: str) -> str:
        """
        Sanitize input for HTML output
        
        Args:
            input_str: String to sanitize
            
        Returns:
            Sanitized string
            
        Raises:
            ValidationError: If input contains dangerous patterns
        """
        if not isinstance(input_str, str):
            raise ValidationError("Input must be a string")
        
        # Check for XSS patterns
        for pattern in SecurityValidator.XSS_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"Input contains potentially dangerous XSS pattern: {input_str}")
        
        # HTML escape
        sanitized = html.escape(input_str)
        return sanitized
    
    @staticmethod
    def _check_injection_patterns(input_str: str, field_name: str) -> None:
        """
        Check input for common injection patterns
        
        Args:
            input_str: String to check
            field_name: Name of the field for error messages
            
        Raises:
            ValidationError: If dangerous patterns are found
        """
        # Check for SQL injection
        for pattern in SecurityValidator.SQL_INJECTION_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"{field_name} contains potentially dangerous SQL pattern")
        
        # Check for command injection
        for pattern in SecurityValidator.COMMAND_INJECTION_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"{field_name} contains potentially dangerous command pattern")
        
        # Check for XSS
        for pattern in SecurityValidator.XSS_PATTERNS:
            if pattern.search(input_str):
                raise ValidationError(f"{field_name} contains potentially dangerous XSS pattern")
    
    @staticmethod
    def validate_dict(data: Dict[str, Any], schema: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Validate a dictionary against a schema
        
        Args:
            data: Dictionary to validate
            schema: Validation schema
            
        Returns:
            Validated and sanitized dictionary
            
        Raises:
            ValidationError: If validation fails
        """
        if not isinstance(data, dict):
            raise ValidationError("Data must be a dictionary")
        
        validated = {}
        
        for field, rules in schema.items():
            value = data.get(field)
            
            # Check required fields
            if rules.get('required', False) and value is None:
                raise ValidationError(f"Required field '{field}' is missing")
            
            if value is not None:
                # Validate type
                expected_type = rules.get('type', str)
                if not isinstance(value, expected_type):
                    raise ValidationError(f"Field '{field}' must be of type {expected_type.__name__}")
                
                # Validate with custom validator
                validator = rules.get('validator')
                if validator:
                    validated[field] = validator(value)
                else:
                    validated[field] = value
        
        return validated

# Convenience functions
def validate_tenant_id(tenant_id: str) -> str:
    """Convenience function to validate tenant ID"""
    return SecurityValidator.validate_tenant_id(tenant_id)

def validate_database_name(db_name: str) -> str:
    """Convenience function to validate database name"""
    return SecurityValidator.validate_database_name(db_name)

def sanitize_sql_input(input_str: str) -> str:
    """Convenience function to sanitize SQL input"""
    return SecurityValidator.sanitize_sql_input(input_str)
