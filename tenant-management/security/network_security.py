#!/usr/bin/env python3
"""
Network Security Module
Implements comprehensive network security with mTLS enforcement,
Istio security policies, ingress security, and egress control.
"""

import os
import json
import tempfile
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class NetworkSecurityManager:
    """Manages network security policies and mTLS configuration for tenants."""

    def __init__(self, run_command_func=None):
        self.run_command = run_command_func

    def create_strict_network_policy(self, tenant_id: str) -> str:
        """Create strict network policy for tenant isolation."""
        network_policy = f"""apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-strict-isolation
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/policy-type: "strict-isolation"
spec:
  podSelector: {{}}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: tenant-{tenant_id}
  # Allow ingress from Istio system
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Prometheus metrics
  egress:
  # Allow egress within same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          name: tenant-{tenant_id}
  # Allow egress to Istio system
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow egress to system services
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  # Allow DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS to external services
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow database access
  - to: []
    ports:
    - protocol: TCP
      port: 3306
  # Allow RabbitMQ access
  - to: []
    ports:
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-deny-all-default
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/policy-type: "deny-all"
spec:
  podSelector: {{}}
  policyTypes:
  - Ingress
  - Egress
"""
        return network_policy

    def create_istio_mtls_policy(self, tenant_id: str) -> str:
        """Create Istio mTLS policies for strict mutual TLS."""
        mtls_policy = f"""apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-{tenant_id}-mtls-strict
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/mtls: "strict"
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  mtls:
    mode: STRICT
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tenant-{tenant_id}-mtls-destination
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/destination-rule: "mtls"
spec:
  host: "*.tenant-{tenant_id}.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30s
        tcpKeepalive:
          time: 7200s
          interval: 75s
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
        maxRetries: 3
        consecutiveGatewayErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 50
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 100
      minHealthPercent: 50
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-authz-policy
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/authorization: "strict"
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  rules:
  # Allow frontend to backend communication
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-{tenant_id}/sa/tenant-{tenant_id}-frontend"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
        paths: ["/api/*"]
  # Allow backend to database communication
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-{tenant_id}/sa/tenant-{tenant_id}-backend"]
    to:
    - operation:
        ports: ["3306"]
  # Allow monitoring access
  - from:
    - source:
        namespaces: ["monitoring"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/metrics", "/health"]
  # Deny all other traffic
  - from:
    - source:
        notPrincipals: ["*"]
    to:
    - operation:
        methods: ["*"]
"""
        return mtls_policy

    def create_ingress_security_config(self, tenant_id: str) -> str:
        """Create secure ingress configuration with WAF and rate limiting."""
        ingress_config = f"""apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-{tenant_id}-secure-gateway
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/gateway: "secure"
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: tenant-{tenant_id}-tls-cert
    hosts:
    - "tenant-{tenant_id}.architrave.local"
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "tenant-{tenant_id}.architrave.local"
    tls:
      httpsRedirect: true
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-{tenant_id}-secure-vs
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/virtual-service: "secure"
spec:
  hosts:
  - "tenant-{tenant_id}.architrave.local"
  gateways:
  - tenant-{tenant_id}-secure-gateway
  http:
  # Security headers and rate limiting
  - match:
    - uri:
        prefix: /
    headers:
      response:
        add:
          X-Frame-Options: DENY
          X-Content-Type-Options: nosniff
          X-XSS-Protection: "1; mode=block"
          Strict-Transport-Security: "max-age=31536000; includeSubDomains"
          Content-Security-Policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
          Referrer-Policy: "strict-origin-when-cross-origin"
    route:
    - destination:
        host: tenant-{tenant_id}-frontend.tenant-{tenant_id}.svc.cluster.local
        port:
          number: 80
      weight: 100
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tenant-{tenant_id}-rate-limit
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/rate-limit: "true"
spec:
  workloadSelector:
    labels:
      app: tenant-{tenant_id}-frontend
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.local_ratelimit
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit
          value:
            stat_prefix: local_rate_limiter
            token_bucket:
              max_tokens: 100
              tokens_per_fill: 100
              fill_interval: 60s
            filter_enabled:
              runtime_key: local_rate_limit_enabled
              default_value:
                numerator: 100
                denominator: HUNDRED
            filter_enforced:
              runtime_key: local_rate_limit_enforced
              default_value:
                numerator: 100
                denominator: HUNDRED
"""
        return ingress_config

    def create_egress_control_policy(self, tenant_id: str) -> str:
        """Create strict egress control policies."""
        egress_policy = f"""apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: tenant-{tenant_id}-allowed-external
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/service-entry: "external"
spec:
  hosts:
  - "api.github.com"
  - "registry-1.docker.io"
  - "*.amazonaws.com"
  - "*.eu-central-1.rds.amazonaws.com"
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  - number: 80
    name: http
    protocol: HTTP
  location: MESH_EXTERNAL
  resolution: DNS
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-egress-authz
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/egress-control: "strict"
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  rules:
  # Allow egress to database
  - to:
    - operation:
        ports: ["3306"]
        hosts: ["*.eu-central-1.rds.amazonaws.com"]
  # Allow egress to S3
  - to:
    - operation:
        ports: ["443"]
        hosts: ["*.s3.eu-central-1.amazonaws.com"]
  # Allow egress to approved external APIs
  - to:
    - operation:
        ports: ["443"]
        hosts: ["api.github.com", "registry-1.docker.io"]
  # Deny all other egress
  - from:
    - source:
        principals: ["*"]
    to:
    - operation:
        hosts: ["*"]
    when:
    - key: destination.service.name
      notValues: ["allowed-external-services"]
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-egress-control
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/egress: "controlled"
spec:
  podSelector:
    matchLabels:
      tenant.architrave.io/tenant-id: {tenant_id}
  policyTypes:
  - Egress
  egress:
  # Allow DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS to specific external hosts
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow database access
  - to: []
    ports:
    - protocol: TCP
      port: 3306
  # Allow RabbitMQ access
  - to: []
    ports:
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
  # Block all other egress
"""
        return egress_policy

    def create_waf_security_config(self, tenant_id: str) -> str:
        """Create Web Application Firewall configuration."""
        waf_config = f"""apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tenant-{tenant_id}-waf
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/waf: "enabled"
spec:
  workloadSelector:
    labels:
      app: tenant-{tenant_id}-frontend
  configPatches:
  - applyTo: HTTP_FILTER
    match:
      context: SIDECAR_INBOUND
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.http_connection_manager"
    patch:
      operation: INSERT_BEFORE
      value:
        name: envoy.filters.http.wasm
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
          config:
            name: "waf"
            root_id: "waf"
            vm_config:
              vm_id: "waf"
              runtime: "envoy.wasm.runtime.v8"
              code:
                local:
                  inline_string: |
                    class WAFFilter {{
                      constructor(rootContext) {{
                        this.rootContext = rootContext;
                      }}

                      onRequestHeaders() {{
                        const userAgent = this.getRequestHeader("user-agent");
                        const path = this.getRequestHeader(":path");

                        // Block common attack patterns
                        const maliciousPatterns = [
                          /\.\.\//,  // Directory traversal
                          /<script/i,  // XSS
                          /union.*select/i,  // SQL injection
                          /exec\(/i,  // Command injection
                          /eval\(/i   // Code injection
                        ];

                        for (const pattern of maliciousPatterns) {{
                          if (pattern.test(path) || pattern.test(userAgent)) {{
                            this.sendLocalResponse(403, "Blocked by WAF", "", {{}});
                            return FilterHeadersStatus.StopIteration;
                          }}
                        }}

                        return FilterHeadersStatus.Continue;
                      }}
                    }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-waf-rules
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/waf-rules: "true"
data:
  waf-rules.conf: |
    # ModSecurity Core Rule Set (CRS) compatible rules
    SecRuleEngine On
    SecRequestBodyAccess On
    SecResponseBodyAccess Off

    # Block SQL injection attempts
    SecRule ARGS "@detectSQLi" \\
        "id:1001,\\
        phase:2,\\
        block,\\
        msg:'SQL Injection Attack Detected',\\
        logdata:'Matched Data: %{{MATCHED_VAR}} found within %{{MATCHED_VAR_NAME}}',\\
        tag:'application-multi',\\
        tag:'language-multi',\\
        tag:'platform-multi',\\
        tag:'attack-sqli'"

    # Block XSS attempts
    SecRule ARGS "@detectXSS" \\
        "id:1002,\\
        phase:2,\\
        block,\\
        msg:'XSS Attack Detected',\\
        logdata:'Matched Data: %{{MATCHED_VAR}} found within %{{MATCHED_VAR_NAME}}',\\
        tag:'application-multi',\\
        tag:'language-multi',\\
        tag:'platform-multi',\\
        tag:'attack-xss'"

    # Block directory traversal
    SecRule REQUEST_URI "@contains ../" \\
        "id:1003,\\
        phase:1,\\
        block,\\
        msg:'Directory Traversal Attack Detected',\\
        tag:'attack-traversal'"

    # Rate limiting per IP
    SecRule IP:REQUEST_COUNT "@gt 100" \\
        "id:1004,\\
        phase:1,\\
        block,\\
        msg:'Rate limit exceeded',\\
        expirevar:IP:REQUEST_COUNT=60"
"""
        return waf_config

    def create_service_mesh_security(self, tenant_id: str) -> str:
        """Create comprehensive service mesh security configuration."""
        mesh_security = f"""apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  name: tenant-{tenant_id}-security-config
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/mesh-security: "enabled"
spec:
  values:
    pilot:
      env:
        EXTERNAL_ISTIOD: false
        PILOT_ENABLE_WORKLOAD_ENTRY_AUTOREGISTRATION: true
        PILOT_ENABLE_CROSS_CLUSTER_WORKLOAD_ENTRY: true
    global:
      meshID: tenant-{tenant_id}-mesh
      network: tenant-{tenant_id}-network
      proxy:
        privileged: false
        readinessInitialDelaySeconds: 1
        readinessPeriodSeconds: 2
        readinessFailureThreshold: 30
        statusPort: 15020
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
      defaultPodDisruptionBudget:
        enabled: true
      proxy_init:
        resources:
          limits:
            cpu: 100m
            memory: 50Mi
          requests:
            cpu: 10m
            memory: 10Mi
  components:
    pilot:
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        securityContext:
          runAsUser: 1337
          runAsGroup: 1337
          runAsNonRoot: true
          fsGroup: 1337
---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: tenant-{tenant_id}-jwt-auth
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/jwt-auth: "enabled"
spec:
  selector:
    matchLabels:
      app: tenant-{tenant_id}-frontend
  jwtRules:
  - issuer: "https://tenant-{tenant_id}.architrave.local/auth"
    jwksUri: "https://tenant-{tenant_id}.architrave.local/auth/.well-known/jwks.json"
    audiences:
    - "tenant-{tenant_id}-api"
    forwardOriginalToken: true
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-jwt-authz
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/jwt-authz: "enabled"
spec:
  selector:
    matchLabels:
      app: tenant-{tenant_id}-frontend
  rules:
  - from:
    - source:
        requestPrincipals: ["https://tenant-{tenant_id}.architrave.local/auth/*"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
  - to:
    - operation:
        methods: ["GET"]
        paths: ["/health", "/metrics", "/favicon.ico"]
"""
        return mesh_security

    def apply_network_security_to_tenant(self, tenant_id: str, run_command) -> bool:
        """Apply comprehensive network security to tenant."""
        try:
            logger.info(f"🔒 Applying comprehensive network security for tenant-{tenant_id}")

            # 1. Create strict network policies
            network_policy = self.create_strict_network_policy(tenant_id)

            # 2. Create Istio mTLS policies
            mtls_policy = self.create_istio_mtls_policy(tenant_id)

            # 3. Create secure ingress configuration
            ingress_config = self.create_ingress_security_config(tenant_id)

            # 4. Create egress control policies
            egress_policy = self.create_egress_control_policy(tenant_id)

            # 5. Create WAF security configuration
            waf_config = self.create_waf_security_config(tenant_id)

            # 6. Create service mesh security
            mesh_security = self.create_service_mesh_security(tenant_id)

            # Apply all configurations
            configs = [
                ("network-policy", network_policy),
                ("mtls-policy", mtls_policy),
                ("ingress-security", ingress_config),
                ("egress-control", egress_policy),
                ("waf-security", waf_config),
                ("mesh-security", mesh_security)
            ]

            for config_name, config_yaml in configs:
                try:
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
                        f.write(config_yaml)
                        temp_file = f.name

                    result = run_command(f"kubectl apply -f {temp_file}", check=False)
                    os.unlink(temp_file)

                    if "error" not in result.lower():
                        logger.info(f"✅ Applied {config_name} for tenant-{tenant_id}")
                    else:
                        logger.warning(f"⚠️ Failed to apply {config_name}: {result}")

                except Exception as e:
                    logger.warning(f"Failed to apply {config_name}: {e}")

            # Wait for Istio configuration to propagate
            logger.info(f"⏳ Waiting for Istio configuration to propagate for tenant-{tenant_id}")
            try:
                run_command("sleep 30", check=False)

                # Verify mTLS is working
                mtls_check = run_command(
                    f"kubectl exec -n tenant-{tenant_id} deployment/tenant-{tenant_id}-frontend "
                    f"-- curl -s -o /dev/null -w '%{{http_code}}' "
                    f"http://tenant-{tenant_id}-backend.tenant-{tenant_id}.svc.cluster.local/api/health",
                    check=False
                )

                if "200" in mtls_check:
                    logger.info(f"✅ mTLS verification successful for tenant-{tenant_id}")
                else:
                    logger.warning(f"⚠️ mTLS verification failed for tenant-{tenant_id}: {mtls_check}")

            except Exception as e:
                logger.warning(f"mTLS verification failed: {e}")

            logger.info(f"✅ Network security setup completed for tenant-{tenant_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to apply network security for tenant {tenant_id}: {e}")
            return False
