= Hetzner DNS Integration for Tenant Management
:toc:
:toclevels: 3
:sectnums:

== Overview

This module provides automatic DNS management for tenant onboarding and offboarding using Hetzner DNS API. It automatically creates and manages DNS records for tenant subdomains, eliminating the need for manual DNS configuration.

== Features

* **Automatic DNS Record Creation**: Creates A and CNAME records for tenant subdomains
* **Wildcard Support**: Sets up wildcard DNS records for tenant applications
* **Kubernetes Integration**: Runs as a controller in your Kubernetes cluster
* **Cleanup Automation**: Automatically removes DNS records during tenant offboarding
* **Verification**: Built-in DNS record verification and health checks
* **Error Handling**: Robust error handling with retry mechanisms

== Architecture

=== Components

1. **Hetzner DNS Manager** (`hetzner_dns_manager.py`)
   - Python library for Hetzner DNS API interaction
   - Supports CRUD operations for DNS records
   - Handles authentication and error management

2. **Kubernetes Controller** (`hetzner-dns-controller.yaml`)
   - Watches for tenant namespace creation/deletion
   - Automatically manages DNS records based on namespace events
   - Runs as a deployment in `hetzner-dns-system` namespace

3. **Integration Scripts**
   - Enhanced tenant onboarding script with DNS support
   - Enhanced tenant offboarding script with DNS cleanup
   - Standalone DNS management utilities

=== DNS Record Structure

For each tenant with ID `my-tenant` and zone `architrave.com`:

[cols="1,1,1,2"]
|===
|Record Type |Name |Value |Purpose

|A
|my-tenant.architrave.com
|Load Balancer IP
|Main tenant subdomain

|CNAME
|*.my-tenant.architrave.com
|my-tenant.architrave.com
|Wildcard for tenant applications

|TXT
|_tenant.my-tenant.architrave.com
|tenant-id=my-tenant;created=timestamp
|Verification and metadata
|===

== Prerequisites

=== Hetzner DNS Setup

1. **Hetzner DNS Account**: You need a Hetzner DNS account
2. **DNS Zone**: Your domain must be managed by Hetzner DNS
3. **API Token**: Generate an API token from https://dns.hetzner.com/settings/api-token

=== Kubernetes Requirements

* Kubernetes cluster with Istio service mesh
* `istio-ingressgateway` service with external load balancer
* Sufficient RBAC permissions for namespace management

=== Dependencies

* Python 3.7+
* `requests` library
* `kubernetes` Python client
* `pyyaml` library

== Installation

=== Quick Setup

[source,bash]
----
# Navigate to the hetzner-dns directory
cd tenant-management/hetzner-dns

# Run the setup script
chmod +x setup_hetzner_dns.sh
./setup_hetzner_dns.sh
----

The setup script will:

1. Prompt for your Hetzner DNS API token
2. Detect your DNS zone and load balancer IP
3. Install required Python dependencies
4. Test the DNS connection
5. Deploy the Kubernetes controller
6. Create configuration files

=== Manual Setup

==== 1. Install Dependencies

[source,bash]
----
pip3 install requests pyyaml kubernetes boto3
----

==== 2. Create Kubernetes Secret

[source,bash]
----
kubectl create namespace hetzner-dns-system

kubectl create secret generic hetzner-dns-credentials \
    --namespace=hetzner-dns-system \
    --from-literal=api-token="YOUR_HETZNER_DNS_TOKEN" \
    --from-literal=zone-name="architrave.com"
----

==== 3. Deploy Controller

[source,bash]
----
kubectl apply -f hetzner-dns-controller.yaml
----

==== 4. Verify Installation

[source,bash]
----
kubectl get pods -n hetzner-dns-system
kubectl logs -n hetzner-dns-system deployment/hetzner-dns-controller
----

== Usage

=== Tenant Onboarding with DNS

[source,bash]
----
python3 ../scripts/advanced_tenant_onboard.py \
  --tenant-id my-tenant \
  --tenant-name "My Company" \
  --subdomain my-tenant \
  --domain architrave.com \
  --hetzner-dns-token $HETZNER_DNS_TOKEN \
  --dns-zone architrave.com
----

=== Tenant Offboarding with DNS Cleanup

[source,bash]
----
python3 ../scripts/advanced_tenant_offboard.py \
  --tenant-id my-tenant \
  --hetzner-dns-token $HETZNER_DNS_TOKEN \
  --dns-zone architrave.com
----

=== Manual DNS Management

==== Setup DNS for Tenant

[source,bash]
----
python3 hetzner_dns_manager.py \
  --api-token $HETZNER_DNS_TOKEN \
  --zone-name architrave.com \
  setup \
  --tenant-id my-tenant \
  --load-balancer-ip *******
----

==== Verify DNS Records

[source,bash]
----
python3 hetzner_dns_manager.py \
  --api-token $HETZNER_DNS_TOKEN \
  --zone-name architrave.com \
  verify \
  --tenant-id my-tenant
----

==== List Tenant DNS Records

[source,bash]
----
python3 hetzner_dns_manager.py \
  --api-token $HETZNER_DNS_TOKEN \
  --zone-name architrave.com \
  list \
  --tenant-id my-tenant
----

==== Cleanup DNS Records

[source,bash]
----
python3 hetzner_dns_manager.py \
  --api-token $HETZNER_DNS_TOKEN \
  --zone-name architrave.com \
  cleanup \
  --tenant-id my-tenant
----

== Configuration

=== Environment Variables

Create a `.env.hetzner-dns` file:

[source,bash]
----
# Hetzner DNS Configuration
HETZNER_DNS_TOKEN=your_api_token_here
DNS_ZONE=architrave.com
LOAD_BALANCER_IP=*******
----

=== Controller Configuration

The controller can be configured via the ConfigMap in `hetzner-dns-controller.yaml`:

[source,yaml]
----
data:
  config.yaml: |
    hetzner:
      api_endpoint: "https://dns.hetzner.com/api/v1"
      zone_name: "architrave.com"
      default_ttl: 300
    
    dns:
      record_types:
        - A
        - CNAME
        - TXT
    
    tenants:
      subdomain_pattern: "{tenant-id}.architrave.com"
      wildcard_pattern: "*.{tenant-id}.architrave.com"
      load_balancer_service: "istio-system/istio-ingressgateway"
    
    automation:
      cleanup_on_delete: true
      verify_records: true
      retry_attempts: 3
      retry_delay: 5
----

== Monitoring and Troubleshooting

=== Check Controller Status

[source,bash]
----
# Check pod status
kubectl get pods -n hetzner-dns-system

# Check controller logs
kubectl logs -n hetzner-dns-system deployment/hetzner-dns-controller

# Check controller events
kubectl get events -n hetzner-dns-system
----

=== Verify DNS Records

[source,bash]
----
# Check DNS resolution
nslookup my-tenant.architrave.com
dig my-tenant.architrave.com

# Check wildcard resolution
nslookup app.my-tenant.architrave.com
----

=== Common Issues

==== DNS Records Not Created

1. Check Hetzner DNS API token validity
2. Verify zone exists in Hetzner DNS
3. Check controller logs for errors
4. Ensure load balancer IP is available

==== DNS Records Not Deleted

1. Check namespace annotations for record IDs
2. Verify controller has proper RBAC permissions
3. Check Hetzner DNS API rate limits

==== Controller Not Starting

1. Check secret exists and contains valid token
2. Verify Python dependencies are installed
3. Check network connectivity to Hetzner DNS API

== Security Considerations

=== API Token Security

* Store API tokens in Kubernetes secrets
* Use least-privilege API tokens
* Rotate tokens regularly
* Monitor API token usage

=== Network Security

* Restrict controller network access
* Use network policies to limit egress
* Monitor DNS API calls

=== RBAC

The controller requires these permissions:

[source,yaml]
----
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
----

== API Reference

=== HetznerDNSManager Class

==== Methods

* `setup_tenant_dns(tenant_id, load_balancer_ip)` - Create DNS records
* `cleanup_tenant_dns(tenant_id, record_ids)` - Delete DNS records
* `verify_tenant_dns(tenant_id)` - Verify DNS records
* `list_tenant_records(tenant_id)` - List DNS records

==== Return Values

All methods return appropriate success/failure indicators and detailed results.

== Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

== License

This project is licensed under the MIT License.
