#!/bin/bash

echo "🌐 HETZNER DNS INTEGRATION SETUP"
echo "================================"
echo ""

# Function to check if command succeeded
check_result() {
    if [ $? -eq 0 ]; then
        echo "✅ $1"
        return 0
    else
        echo "❌ $1"
        return 1
    fi
}

# Function to prompt for input
prompt_input() {
    local prompt="$1"
    local var_name="$2"
    local default="$3"
    
    if [ -n "$default" ]; then
        read -p "$prompt [$default]: " input
        if [ -z "$input" ]; then
            input="$default"
        fi
    else
        read -p "$prompt: " input
        while [ -z "$input" ]; do
            echo "This field is required."
            read -p "$prompt: " input
        done
    fi
    
    eval "$var_name='$input'"
}

echo "🔧 STEP 1: Gather Configuration"
echo "==============================="

# Get Hetzner DNS API token
echo ""
echo "You need a Hetzner DNS API token to manage DNS records."
echo "Get your token from: https://dns.hetzner.com/settings/api-token"
echo ""
prompt_input "Enter your Hetzner DNS API token" HETZNER_DNS_TOKEN

# Get DNS zone name
prompt_input "Enter your DNS zone name" DNS_ZONE "architrave.com"

# Get load balancer IP (optional)
echo ""
echo "Detecting load balancer IP..."
LB_IP=$(kubectl get service istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null)
if [ -n "$LB_IP" ]; then
    echo "✅ Detected load balancer IP: $LB_IP"
    prompt_input "Use this load balancer IP" LOAD_BALANCER_IP "$LB_IP"
else
    echo "⚠️ Could not detect load balancer IP automatically"
    prompt_input "Enter your load balancer IP (optional)" LOAD_BALANCER_IP ""
fi

echo ""
echo "🔧 STEP 2: Install Python Dependencies"
echo "======================================"

# Install required Python packages
echo "Installing required Python packages..."
pip3 install requests pyyaml kubernetes boto3 2>/dev/null || {
    echo "Installing with user flag..."
    pip3 install --user requests pyyaml kubernetes boto3
}
check_result "Python dependencies installed"

echo ""
echo "🔧 STEP 3: Test Hetzner DNS Connection"
echo "====================================="

# Test the DNS manager
echo "Testing Hetzner DNS connection..."
python3 -c "
import sys
sys.path.append('.')
from hetzner_dns_manager import HetznerDNSManager

try:
    dns_manager = HetznerDNSManager('$HETZNER_DNS_TOKEN', '$DNS_ZONE')
    print('✅ Hetzner DNS connection successful')
    print(f'✅ Zone ID found for $DNS_ZONE')
except Exception as e:
    print(f'❌ Hetzner DNS connection failed: {e}')
    sys.exit(1)
"
check_result "Hetzner DNS connection test"

echo ""
echo "🔧 STEP 4: Create Kubernetes Secret"
echo "==================================="

# Create namespace if it doesn't exist
kubectl create namespace hetzner-dns-system 2>/dev/null || echo "Namespace already exists"

# Create secret with credentials
kubectl create secret generic hetzner-dns-credentials \
    --namespace=hetzner-dns-system \
    --from-literal=api-token="$HETZNER_DNS_TOKEN" \
    --from-literal=zone-name="$DNS_ZONE" \
    --dry-run=client -o yaml | kubectl apply -f -

check_result "Kubernetes secret created"

echo ""
echo "🔧 STEP 5: Deploy Hetzner DNS Controller"
echo "========================================"

# Apply the controller YAML
echo "Deploying Hetzner DNS controller..."
kubectl apply -f hetzner-dns-controller.yaml
check_result "Hetzner DNS controller deployed"

# Wait for controller to be ready
echo "Waiting for controller to be ready..."
kubectl wait --for=condition=ready pod -l app=hetzner-dns-controller -n hetzner-dns-system --timeout=120s
check_result "Hetzner DNS controller ready"

echo ""
echo "🔧 STEP 6: Test DNS Management"
echo "============================="

# Test DNS management with a test tenant
TEST_TENANT="dns-test-$(date +%s)"
echo "Testing DNS management with tenant: $TEST_TENANT"

# Create test DNS records
echo "Creating test DNS records..."
python3 hetzner_dns_manager.py \
    --api-token "$HETZNER_DNS_TOKEN" \
    --zone-name "$DNS_ZONE" \
    setup \
    --tenant-id "$TEST_TENANT" \
    ${LOAD_BALANCER_IP:+--load-balancer-ip "$LOAD_BALANCER_IP"}

check_result "Test DNS records created"

# Verify DNS records
echo "Verifying test DNS records..."
python3 hetzner_dns_manager.py \
    --api-token "$HETZNER_DNS_TOKEN" \
    --zone-name "$DNS_ZONE" \
    verify \
    --tenant-id "$TEST_TENANT"

check_result "Test DNS records verified"

# Clean up test DNS records
echo "Cleaning up test DNS records..."
python3 hetzner_dns_manager.py \
    --api-token "$HETZNER_DNS_TOKEN" \
    --zone-name "$DNS_ZONE" \
    cleanup \
    --tenant-id "$TEST_TENANT"

check_result "Test DNS records cleaned up"

echo ""
echo "🔧 STEP 7: Update Tenant Scripts"
echo "==============================="

# Create environment file for easy reuse
cat > .env.hetzner-dns << EOF
# Hetzner DNS Configuration
HETZNER_DNS_TOKEN=$HETZNER_DNS_TOKEN
DNS_ZONE=$DNS_ZONE
LOAD_BALANCER_IP=$LOAD_BALANCER_IP
EOF

echo "✅ Created .env.hetzner-dns configuration file"

# Create usage examples
cat > hetzner_dns_examples.sh << 'EOF'
#!/bin/bash

# Example usage of Hetzner DNS integration

# Source the environment
source .env.hetzner-dns

echo "🌐 HETZNER DNS EXAMPLES"
echo "======================"

echo ""
echo "1. Onboard tenant with DNS:"
echo "python3 ../scripts/advanced_tenant_onboard.py \\"
echo "  --tenant-id my-tenant \\"
echo "  --tenant-name \"My Company\" \\"
echo "  --subdomain my-tenant \\"
echo "  --domain $DNS_ZONE \\"
echo "  --hetzner-dns-token \$HETZNER_DNS_TOKEN \\"
echo "  --dns-zone $DNS_ZONE"

echo ""
echo "2. Offboard tenant with DNS cleanup:"
echo "python3 ../scripts/advanced_tenant_offboard.py \\"
echo "  --tenant-id my-tenant \\"
echo "  --hetzner-dns-token \$HETZNER_DNS_TOKEN \\"
echo "  --dns-zone $DNS_ZONE"

echo ""
echo "3. Manual DNS management:"
echo "# Setup DNS for tenant"
echo "python3 hetzner_dns_manager.py --api-token \$HETZNER_DNS_TOKEN setup --tenant-id my-tenant"
echo ""
echo "# Verify DNS for tenant"
echo "python3 hetzner_dns_manager.py --api-token \$HETZNER_DNS_TOKEN verify --tenant-id my-tenant"
echo ""
echo "# List all tenant DNS records"
echo "python3 hetzner_dns_manager.py --api-token \$HETZNER_DNS_TOKEN list"
echo ""
echo "# Cleanup DNS for tenant"
echo "python3 hetzner_dns_manager.py --api-token \$HETZNER_DNS_TOKEN cleanup --tenant-id my-tenant"

echo ""
echo "4. Check controller status:"
echo "kubectl get pods -n hetzner-dns-system"
echo "kubectl logs -n hetzner-dns-system deployment/hetzner-dns-controller"

EOF

chmod +x hetzner_dns_examples.sh
echo "✅ Created hetzner_dns_examples.sh usage guide"

echo ""
echo "🎉 HETZNER DNS INTEGRATION SETUP COMPLETED"
echo "=========================================="
echo ""
echo "📋 SUMMARY:"
echo "----------"
echo "✅ Hetzner DNS API connection tested"
echo "✅ Kubernetes secret created"
echo "✅ DNS controller deployed and running"
echo "✅ DNS management tested successfully"
echo "✅ Configuration files created"
echo ""
echo "📁 FILES CREATED:"
echo "----------------"
echo "• .env.hetzner-dns - Environment configuration"
echo "• hetzner_dns_examples.sh - Usage examples"
echo ""
echo "🚀 NEXT STEPS:"
echo "-------------"
echo "1. Use the updated tenant onboarding script with --hetzner-dns-token flag"
echo "2. DNS records will be automatically created/deleted during tenant lifecycle"
echo "3. Check controller logs: kubectl logs -n hetzner-dns-system deployment/hetzner-dns-controller"
echo "4. View examples: ./hetzner_dns_examples.sh"
echo ""
echo "🌐 DNS ZONE: $DNS_ZONE"
echo "🔗 Load Balancer IP: ${LOAD_BALANCER_IP:-'Auto-detected'}"
echo ""
echo "Your tenant subdomains will be automatically created as:"
echo "• {tenant-id}.$DNS_ZONE"
echo "• *.{tenant-id}.$DNS_ZONE"
