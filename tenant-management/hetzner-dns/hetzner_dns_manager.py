#!/usr/bin/env python3
"""
Hetzner DNS Manager for Tenant Onboarding/Offboarding
Manages DNS records in Hetzner DNS for tenant subdomains
"""

import os
import sys
import json
import time
import logging
import requests
import argparse
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

class HetznerDNSManager:
    """Manages DNS records in Hetzner DNS for tenant subdomains."""
    
    def __init__(self, api_token: str, zone_name: str = "architrave.com"):
        """Initialize the Hetzner DNS manager.
        
        Args:
            api_token: Hetzner DNS API token
            zone_name: The DNS zone name (default: architrave.com)
        """
        self.api_token = api_token
        self.zone_name = zone_name
        self.base_url = "https://dns.hetzner.com/api/v1"
        self.headers = {
            "Auth-API-Token": self.api_token,
            "Content-Type": "application/json"
        }
        self.zone_id = None
        
        # Initialize zone
        self._get_zone_id()
    
    def _get_zone_id(self) -> bool:
        """Get the zone ID for the configured domain."""
        try:
            logger.info(f"Getting zone ID for {self.zone_name}")
            response = requests.get(f"{self.base_url}/zones", headers=self.headers)
            response.raise_for_status()
            
            zones = response.json().get("zones", [])
            
            for zone in zones:
                if zone["name"] == self.zone_name:
                    self.zone_id = zone["id"]
                    logger.info(f"✅ Found zone ID: {self.zone_id} for {self.zone_name}")
                    return True
            
            logger.error(f"❌ Zone {self.zone_name} not found in Hetzner DNS")
            return False
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to get zone ID: {e}")
            return False
    
    def get_load_balancer_ip(self) -> Optional[str]:
        """Get the external IP of the Istio ingress gateway."""
        try:
            # Try to get from kubectl
            import subprocess
            result = subprocess.run([
                "kubectl", "get", "service", "istio-ingressgateway", 
                "-n", "istio-system", 
                "-o", "jsonpath={.status.loadBalancer.ingress[0].ip}"
            ], capture_output=True, text=True, check=True)
            
            ip = result.stdout.strip()
            if ip:
                logger.info(f"✅ Found load balancer IP: {ip}")
                return ip
            else:
                logger.warning("⚠️ Load balancer IP not yet assigned")
                return None
                
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to get load balancer IP: {e}")
            return None
        except ImportError:
            logger.error("❌ subprocess module not available")
            return None
    
    def create_dns_record(self, name: str, record_type: str, value: str, ttl: int = 300) -> Optional[str]:
        """Create a DNS record in Hetzner.
        
        Args:
            name: Record name (e.g., "tenant1.architrave.com")
            record_type: Record type (A, CNAME, TXT, etc.)
            value: Record value (IP address, domain, etc.)
            ttl: Time to live in seconds
            
        Returns:
            Record ID if successful, None otherwise
        """
        if not self.zone_id:
            logger.error("❌ No zone ID available")
            return None
        
        data = {
            "zone_id": self.zone_id,
            "type": record_type,
            "name": name,
            "value": value,
            "ttl": ttl
        }
        
        try:
            logger.info(f"Creating {record_type} record: {name} -> {value}")
            response = requests.post(
                f"{self.base_url}/records",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            record = response.json().get("record", {})
            record_id = record.get("id")
            
            if record_id:
                logger.info(f"✅ Created DNS record: {name} -> {value} (ID: {record_id})")
                return record_id
            else:
                logger.error(f"❌ Failed to get record ID from response")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to create DNS record {name}: {e}")
            return None
    
    def delete_dns_record(self, record_id: str) -> bool:
        """Delete a DNS record from Hetzner.
        
        Args:
            record_id: The record ID to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Deleting DNS record ID: {record_id}")
            response = requests.delete(
                f"{self.base_url}/records/{record_id}",
                headers=self.headers
            )
            response.raise_for_status()
            
            logger.info(f"✅ Deleted DNS record ID: {record_id}")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to delete DNS record {record_id}: {e}")
            return False
    
    def get_existing_records(self, name: str = None) -> List[Dict]:
        """Get existing DNS records, optionally filtered by name.
        
        Args:
            name: Optional name filter
            
        Returns:
            List of DNS records
        """
        if not self.zone_id:
            return []
        
        try:
            logger.info(f"Getting existing DNS records for zone {self.zone_name}")
            response = requests.get(
                f"{self.base_url}/records?zone_id={self.zone_id}",
                headers=self.headers
            )
            response.raise_for_status()
            
            records = response.json().get("records", [])
            
            if name:
                records = [r for r in records if r["name"] == name]
                logger.info(f"Found {len(records)} records for {name}")
            else:
                logger.info(f"Found {len(records)} total records")
            
            return records
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Failed to get existing records: {e}")
            return []
    
    def setup_tenant_dns(self, tenant_id: str, load_balancer_ip: str = None) -> Dict[str, str]:
        """Set up DNS records for a tenant.
        
        Args:
            tenant_id: The tenant identifier
            load_balancer_ip: Optional load balancer IP (will auto-detect if not provided)
            
        Returns:
            Dictionary with record IDs
        """
        logger.info(f"🌐 Setting up DNS for tenant: {tenant_id}")
        
        # Get load balancer IP if not provided
        if not load_balancer_ip:
            load_balancer_ip = self.get_load_balancer_ip()
            if not load_balancer_ip:
                logger.error("❌ Cannot create DNS records without load balancer IP")
                return {}
        
        subdomain = f"{tenant_id}.{self.zone_name}"
        wildcard = f"*.{tenant_id}.{self.zone_name}"
        
        record_ids = {}
        
        # Create A record for subdomain
        a_record_id = self.create_dns_record(subdomain, "A", load_balancer_ip)
        if a_record_id:
            record_ids["a_record"] = a_record_id
        
        # Create wildcard CNAME record
        cname_record_id = self.create_dns_record(wildcard, "CNAME", subdomain)
        if cname_record_id:
            record_ids["cname_record"] = cname_record_id
        
        # Create TXT record for verification
        txt_record_id = self.create_dns_record(
            f"_tenant.{subdomain}", 
            "TXT", 
            f"tenant-id={tenant_id};created={int(time.time())}"
        )
        if txt_record_id:
            record_ids["txt_record"] = txt_record_id
        
        if record_ids:
            logger.info(f"✅ DNS setup completed for tenant: {tenant_id}")
            logger.info(f"   - Subdomain: {subdomain}")
            logger.info(f"   - Wildcard: {wildcard}")
            logger.info(f"   - Load Balancer IP: {load_balancer_ip}")
        else:
            logger.error(f"❌ Failed to set up DNS for tenant: {tenant_id}")
        
        return record_ids
    
    def cleanup_tenant_dns(self, tenant_id: str, record_ids: Dict[str, str] = None) -> bool:
        """Clean up DNS records for a tenant.
        
        Args:
            tenant_id: The tenant identifier
            record_ids: Optional dictionary of record IDs to delete
            
        Returns:
            True if successful, False otherwise
        """
        logger.info(f"🧹 Cleaning up DNS for tenant: {tenant_id}")
        
        success = True
        
        # Delete specific record IDs if provided
        if record_ids:
            for record_type, record_id in record_ids.items():
                if not self.delete_dns_record(record_id):
                    success = False
        
        # Also clean up by name (fallback)
        subdomain = f"{tenant_id}.{self.zone_name}"
        wildcard = f"*.{tenant_id}.{self.zone_name}"
        txt_record = f"_tenant.{subdomain}"
        
        for name in [subdomain, wildcard, txt_record]:
            existing_records = self.get_existing_records(name)
            for record in existing_records:
                if not self.delete_dns_record(record["id"]):
                    success = False
        
        if success:
            logger.info(f"✅ DNS cleanup completed for tenant: {tenant_id}")
        else:
            logger.warning(f"⚠️ DNS cleanup completed with some errors for tenant: {tenant_id}")
        
        return success
    
    def verify_tenant_dns(self, tenant_id: str) -> Dict[str, bool]:
        """Verify DNS records for a tenant.
        
        Args:
            tenant_id: The tenant identifier
            
        Returns:
            Dictionary with verification results
        """
        logger.info(f"🔍 Verifying DNS for tenant: {tenant_id}")
        
        subdomain = f"{tenant_id}.{self.zone_name}"
        wildcard = f"*.{tenant_id}.{self.zone_name}"
        txt_record = f"_tenant.{subdomain}"
        
        results = {}
        
        # Check A record
        a_records = self.get_existing_records(subdomain)
        results["a_record"] = any(r["type"] == "A" for r in a_records)
        
        # Check CNAME record
        cname_records = self.get_existing_records(wildcard)
        results["cname_record"] = any(r["type"] == "CNAME" for r in cname_records)
        
        # Check TXT record
        txt_records = self.get_existing_records(txt_record)
        results["txt_record"] = any(r["type"] == "TXT" for r in txt_records)
        
        # Overall status
        results["all_records"] = all(results.values())
        
        logger.info(f"DNS verification results for {tenant_id}: {results}")
        return results
    
    def list_tenant_records(self, tenant_id: str = None) -> List[Dict]:
        """List all tenant DNS records.
        
        Args:
            tenant_id: Optional tenant ID filter
            
        Returns:
            List of tenant DNS records
        """
        all_records = self.get_existing_records()
        tenant_records = []
        
        for record in all_records:
            name = record["name"]
            
            # Check if this is a tenant record
            if tenant_id:
                if name.startswith(f"{tenant_id}.") or name.startswith(f"*.{tenant_id}."):
                    tenant_records.append(record)
            else:
                # Look for any tenant pattern
                if "." in name and not name.startswith("www.") and not name.startswith("mail."):
                    tenant_records.append(record)
        
        return tenant_records


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Hetzner DNS Manager for Tenant Management")
    parser.add_argument("--api-token", required=True, help="Hetzner DNS API token")
    parser.add_argument("--zone-name", default="architrave.com", help="DNS zone name")
    parser.add_argument("--tenant-id", help="Tenant ID")
    parser.add_argument("--load-balancer-ip", help="Load balancer IP address")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Setup command
    setup_parser = subparsers.add_parser("setup", help="Set up DNS for a tenant")
    setup_parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    setup_parser.add_argument("--load-balancer-ip", help="Load balancer IP")
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser("cleanup", help="Clean up DNS for a tenant")
    cleanup_parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    
    # Verify command
    verify_parser = subparsers.add_parser("verify", help="Verify DNS for a tenant")
    verify_parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List tenant DNS records")
    list_parser.add_argument("--tenant-id", help="Optional tenant ID filter")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Initialize DNS manager
    dns_manager = HetznerDNSManager(args.api_token, args.zone_name)
    
    if args.command == "setup":
        record_ids = dns_manager.setup_tenant_dns(args.tenant_id, args.load_balancer_ip)
        if record_ids:
            print(json.dumps(record_ids, indent=2))
            sys.exit(0)
        else:
            sys.exit(1)
    
    elif args.command == "cleanup":
        success = dns_manager.cleanup_tenant_dns(args.tenant_id)
        sys.exit(0 if success else 1)
    
    elif args.command == "verify":
        results = dns_manager.verify_tenant_dns(args.tenant_id)
        print(json.dumps(results, indent=2))
        sys.exit(0 if results["all_records"] else 1)
    
    elif args.command == "list":
        records = dns_manager.list_tenant_records(args.tenant_id)
        print(json.dumps(records, indent=2))
        sys.exit(0)


if __name__ == "__main__":
    main()
