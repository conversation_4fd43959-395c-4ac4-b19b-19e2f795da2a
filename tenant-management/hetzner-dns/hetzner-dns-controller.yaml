---
# Hetzner DNS Controller for Automatic Tenant DNS Management
apiVersion: v1
kind: Namespace
metadata:
  name: hetzner-dns-system
  labels:
    name: hetzner-dns-system
    app.kubernetes.io/name: hetzner-dns-controller
---
apiVersion: v1
kind: Secret
metadata:
  name: hetzner-dns-credentials
  namespace: hetzner-dns-system
type: Opaque
stringData:
  # Replace with your actual Hetzner DNS API token
  api-token: "YOUR_HETZNER_DNS_API_TOKEN_HERE"
  zone-name: "architrave.com"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: hetzner-dns-config
  namespace: hetzner-dns-system
data:
  config.yaml: |
    hetzner:
      api_endpoint: "https://dns.hetzner.com/api/v1"
      zone_name: "architrave.com"
      default_ttl: 300
      
    dns:
      record_types:
        - A
        - CNAME
        - TXT
      
    tenants:
      subdomain_pattern: "{tenant-id}.architrave.com"
      wildcard_pattern: "*.{tenant-id}.architrave.com"
      load_balancer_service: "istio-system/istio-ingressgateway"
      
    automation:
      cleanup_on_delete: true
      verify_records: true
      retry_attempts: 3
      retry_delay: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
  labels:
    app: hetzner-dns-controller
    app.kubernetes.io/name: hetzner-dns-controller
    app.kubernetes.io/component: controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hetzner-dns-controller
  template:
    metadata:
      labels:
        app: hetzner-dns-controller
        app.kubernetes.io/name: hetzner-dns-controller
        app.kubernetes.io/component: controller
    spec:
      serviceAccountName: hetzner-dns-controller
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: controller
        image: python:3.11-alpine
        imagePullPolicy: Always
        command: ["/bin/sh", "-c"]
        args:
        - |
          # Install required packages
          pip install requests kubernetes pyyaml
          
          # Create the controller script
          cat > /app/hetzner-dns-controller.py << 'EOF'
          #!/usr/bin/env python3
          import os
          import sys
          import time
          import json
          import requests
          import logging
          from kubernetes import client, config, watch
          from kubernetes.client.rest import ApiException
          
          # Configure logging
          logging.basicConfig(
              level=logging.INFO,
              format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
          )
          logger = logging.getLogger('hetzner-dns-controller')
          
          class HetznerDNSController:
              def __init__(self):
                  self.api_token = os.environ.get('HETZNER_DNS_API_TOKEN')
                  self.zone_name = os.environ.get('HETZNER_ZONE_NAME', 'architrave.com')
                  self.base_url = "https://dns.hetzner.com/api/v1"
                  self.headers = {
                      "Auth-API-Token": self.api_token,
                      "Content-Type": "application/json"
                  }
                  
                  # Initialize Kubernetes client
                  try:
                      config.load_incluster_config()
                  except:
                      config.load_kube_config()
                  
                  self.k8s_client = client.CoreV1Api()
                  self.zone_id = self.get_zone_id()
                  
              def get_zone_id(self):
                  """Get the zone ID for the configured domain."""
                  try:
                      response = requests.get(f"{self.base_url}/zones", headers=self.headers)
                      response.raise_for_status()
                      zones = response.json()["zones"]
                      
                      for zone in zones:
                          if zone["name"] == self.zone_name:
                              logger.info(f"Found zone ID {zone['id']} for {self.zone_name}")
                              return zone["id"]
                      
                      logger.error(f"Zone {self.zone_name} not found")
                      return None
                  except Exception as e:
                      logger.error(f"Failed to get zone ID: {e}")
                      return None
              
              def get_load_balancer_ip(self):
                  """Get the external IP of the Istio ingress gateway."""
                  try:
                      service = self.k8s_client.read_namespaced_service(
                          name="istio-ingressgateway",
                          namespace="istio-system"
                      )
                      
                      if service.status.load_balancer.ingress:
                          ip = service.status.load_balancer.ingress[0].ip
                          logger.info(f"Found load balancer IP: {ip}")
                          return ip
                      else:
                          logger.warning("Load balancer IP not yet assigned")
                          return None
                  except ApiException as e:
                      logger.error(f"Failed to get load balancer IP: {e}")
                      return None
              
              def create_dns_record(self, name, record_type, value, ttl=300):
                  """Create a DNS record in Hetzner."""
                  if not self.zone_id:
                      logger.error("No zone ID available")
                      return False
                  
                  data = {
                      "zone_id": self.zone_id,
                      "type": record_type,
                      "name": name,
                      "value": value,
                      "ttl": ttl
                  }
                  
                  try:
                      response = requests.post(
                          f"{self.base_url}/records",
                          headers=self.headers,
                          json=data
                      )
                      response.raise_for_status()
                      record = response.json()["record"]
                      logger.info(f"Created DNS record: {name} -> {value} (ID: {record['id']})")
                      return record["id"]
                  except Exception as e:
                      logger.error(f"Failed to create DNS record {name}: {e}")
                      return False
              
              def delete_dns_record(self, record_id):
                  """Delete a DNS record from Hetzner."""
                  try:
                      response = requests.delete(
                          f"{self.base_url}/records/{record_id}",
                          headers=self.headers
                      )
                      response.raise_for_status()
                      logger.info(f"Deleted DNS record ID: {record_id}")
                      return True
                  except Exception as e:
                      logger.error(f"Failed to delete DNS record {record_id}: {e}")
                      return False
              
              def get_existing_records(self, name):
                  """Get existing DNS records for a name."""
                  if not self.zone_id:
                      return []
                  
                  try:
                      response = requests.get(
                          f"{self.base_url}/records?zone_id={self.zone_id}",
                          headers=self.headers
                      )
                      response.raise_for_status()
                      records = response.json()["records"]
                      
                      return [r for r in records if r["name"] == name]
                  except Exception as e:
                      logger.error(f"Failed to get existing records: {e}")
                      return []
              
              def setup_tenant_dns(self, tenant_id):
                  """Set up DNS records for a tenant."""
                  logger.info(f"Setting up DNS for tenant: {tenant_id}")
                  
                  # Get load balancer IP
                  lb_ip = self.get_load_balancer_ip()
                  if not lb_ip:
                      logger.error("Cannot create DNS records without load balancer IP")
                      return False
                  
                  subdomain = f"{tenant_id}.{self.zone_name}"
                  wildcard = f"*.{tenant_id}.{self.zone_name}"
                  
                  # Create A record for subdomain
                  a_record_id = self.create_dns_record(subdomain, "A", lb_ip)
                  
                  # Create wildcard CNAME record
                  cname_record_id = self.create_dns_record(wildcard, "CNAME", subdomain)
                  
                  if a_record_id and cname_record_id:
                      # Store record IDs in namespace annotation
                      self.store_dns_record_ids(tenant_id, {
                          "a_record": a_record_id,
                          "cname_record": cname_record_id
                      })
                      logger.info(f"DNS setup completed for tenant: {tenant_id}")
                      return True
                  
                  return False
              
              def cleanup_tenant_dns(self, tenant_id):
                  """Clean up DNS records for a tenant."""
                  logger.info(f"Cleaning up DNS for tenant: {tenant_id}")
                  
                  # Get stored record IDs
                  record_ids = self.get_dns_record_ids(tenant_id)
                  
                  if record_ids:
                      for record_type, record_id in record_ids.items():
                          self.delete_dns_record(record_id)
                  
                  # Also clean up by name (fallback)
                  subdomain = f"{tenant_id}.{self.zone_name}"
                  wildcard = f"*.{tenant_id}.{self.zone_name}"
                  
                  for name in [subdomain, wildcard]:
                      existing_records = self.get_existing_records(name)
                      for record in existing_records:
                          self.delete_dns_record(record["id"])
                  
                  logger.info(f"DNS cleanup completed for tenant: {tenant_id}")
              
              def store_dns_record_ids(self, tenant_id, record_ids):
                  """Store DNS record IDs in namespace annotation."""
                  try:
                      namespace_name = f"tenant-{tenant_id}"
                      namespace = self.k8s_client.read_namespace(namespace_name)
                      
                      if not namespace.metadata.annotations:
                          namespace.metadata.annotations = {}
                      
                      namespace.metadata.annotations["hetzner-dns/record-ids"] = json.dumps(record_ids)
                      
                      self.k8s_client.patch_namespace(
                          name=namespace_name,
                          body=namespace
                      )
                      logger.info(f"Stored DNS record IDs for tenant: {tenant_id}")
                  except Exception as e:
                      logger.error(f"Failed to store DNS record IDs: {e}")
              
              def get_dns_record_ids(self, tenant_id):
                  """Get DNS record IDs from namespace annotation."""
                  try:
                      namespace_name = f"tenant-{tenant_id}"
                      namespace = self.k8s_client.read_namespace(namespace_name)
                      
                      if namespace.metadata.annotations:
                          record_ids_json = namespace.metadata.annotations.get("hetzner-dns/record-ids")
                          if record_ids_json:
                              return json.loads(record_ids_json)
                      
                      return {}
                  except Exception as e:
                      logger.error(f"Failed to get DNS record IDs: {e}")
                      return {}
              
              def watch_namespaces(self):
                  """Watch for namespace events and manage DNS accordingly."""
                  logger.info("Starting namespace watcher...")
                  
                  w = watch.Watch()
                  for event in w.stream(self.k8s_client.list_namespace):
                      event_type = event['type']
                      namespace = event['object']
                      namespace_name = namespace.metadata.name
                      
                      # Only handle tenant namespaces
                      if not namespace_name.startswith('tenant-'):
                          continue
                      
                      tenant_id = namespace_name.replace('tenant-', '')
                      
                      if event_type == 'ADDED':
                          logger.info(f"Namespace created: {namespace_name}")
                          # Wait a bit for namespace to be fully ready
                          time.sleep(5)
                          self.setup_tenant_dns(tenant_id)
                      
                      elif event_type == 'DELETED':
                          logger.info(f"Namespace deleted: {namespace_name}")
                          self.cleanup_tenant_dns(tenant_id)
              
              def run(self):
                  """Main controller loop."""
                  logger.info("Starting Hetzner DNS Controller")
                  
                  if not self.api_token:
                      logger.error("HETZNER_DNS_API_TOKEN not provided")
                      sys.exit(1)
                  
                  if not self.zone_id:
                      logger.error("Failed to get zone ID")
                      sys.exit(1)
                  
                  # Start watching namespaces
                  try:
                      self.watch_namespaces()
                  except Exception as e:
                      logger.error(f"Controller error: {e}")
                      sys.exit(1)
          
          if __name__ == "__main__":
              controller = HetznerDNSController()
              controller.run()
          EOF
          
          # Run the controller
          python /app/hetzner-dns-controller.py
        env:
        - name: HETZNER_DNS_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: hetzner-dns-credentials
              key: api-token
        - name: HETZNER_ZONE_NAME
          valueFrom:
            secretKeyRef:
              name: hetzner-dns-credentials
              key: zone-name
        - name: PYTHONUNBUFFERED
          value: "1"
        ports:
        - containerPort: 8080
          name: metrics
        - containerPort: 8081
          name: health
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        livenessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - python
            - -c
            - "import sys; sys.exit(0)"
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: hetzner-dns-controller
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: [""]
  resources: ["services"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: hetzner-dns-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: hetzner-dns-controller
subjects:
- kind: ServiceAccount
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
---
apiVersion: v1
kind: Service
metadata:
  name: hetzner-dns-controller-metrics
  namespace: hetzner-dns-system
  labels:
    app: hetzner-dns-controller
spec:
  ports:
  - name: metrics
    port: 8080
    targetPort: 8080
  - name: health
    port: 8081
    targetPort: 8081
  selector:
    app: hetzner-dns-controller
