#!/usr/bin/env python3
"""
Fixed Advanced Tenant Onboarding Script
Resolves import hanging issues and provides reliable tenant onboarding
"""

import argparse
import json
import logging
import os
import random
import string
import subprocess
import sys
import tempfile
import time
from datetime import datetime

# Configure logging first (before any other imports)
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Try to import Rich with fallback
try:
    from rich.console import Console
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, BarColumn, TextColumn, TimeElapsedColumn
    from rich.table import Table
    console = Console()
    RICH_AVAILABLE = True
    logger.info("✅ Rich library loaded successfully")
except ImportError:
    # Fallback to basic console output
    RICH_AVAILABLE = False
    logger.warning("⚠️ Rich library not available, using basic output")

    class BasicConsole:
        def print(self, *args, **kwargs):
            print(*args)

    console = BasicConsole()

# Try to import boto3 with fallback
try:
    import boto3
    s3_client = boto3.client('s3')
    rds_client = boto3.client('rds')
    AWS_AVAILABLE = True
    logger.info("✅ AWS SDK loaded successfully")
except ImportError:
    AWS_AVAILABLE = False
    logger.warning("⚠️ AWS SDK not available, some features disabled")
    s3_client = None
    rds_client = None

# Default values
DEFAULT_RDS_HOST = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
DEFAULT_RDS_PORT = "3306"
DEFAULT_RDS_ADMIN_USER = "admin"
DEFAULT_RDS_ADMIN_PASSWORD = "&BZzY_<AK(=a*UhZ"
DEFAULT_RDS_DATABASE = "architrave"
DEFAULT_DOMAIN = "architrave.local"
DEFAULT_ENVIRONMENT = "production"

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Fixed Advanced Tenant Onboarding Script")

    # Required arguments
    parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    parser.add_argument("--tenant-name", required=True, help="Tenant name")
    parser.add_argument("--subdomain", required=True, help="Subdomain for tenant")

    # Optional arguments
    parser.add_argument("--domain", default=DEFAULT_DOMAIN, help=f"Domain (default: {DEFAULT_DOMAIN})")
    parser.add_argument("--environment", default=DEFAULT_ENVIRONMENT, help=f"Environment (default: {DEFAULT_ENVIRONMENT})")
    parser.add_argument("--backend-image", required=True, help="Backend image")
    parser.add_argument("--frontend-image", required=True, help="Frontend image")
    parser.add_argument("--rabbitmq-image", required=True, help="RabbitMQ image")
    parser.add_argument("--local-sql-file", help="Path to local SQL file")

    # Skip flags
    parser.add_argument("--skip-db-import", action="store_true", help="Skip database import")
    parser.add_argument("--skip-s3-setup", action="store_true", help="Skip S3 setup")
    parser.add_argument("--skip-monitoring", action="store_true", help="Skip monitoring setup")

    # Debug flag
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")

    args = parser.parse_args()

    if args.debug:
        logger.setLevel(logging.DEBUG)

    # Basic validation
    if not args.tenant_id or not args.tenant_name or not args.subdomain:
        logger.error("❌ Tenant ID, name, and subdomain are required")
        sys.exit(1)

    # Sanitize inputs
    args.tenant_id = args.tenant_id.lower().replace('_', '-')
    args.subdomain = args.subdomain.lower().replace('_', '-')

    return args

def print_header(title: str) -> None:
    """Print a formatted header."""
    if RICH_AVAILABLE:
        console.print()
        console.print(Panel(f"[bold blue]{title}[/bold blue]", expand=False))
        console.print()
    else:
        print(f"\n{'='*60}")
        print(f"  {title}")
        print(f"{'='*60}\n")

def print_step(step: str, description: str) -> None:
    """Print a formatted step."""
    if RICH_AVAILABLE:
        console.print(f"[bold green]Step {step}:[/bold green] [white]{description}[/white]")
    else:
        print(f"Step {step}: {description}")

def print_success(message: str) -> None:
    """Print a success message."""
    if RICH_AVAILABLE:
        console.print(f"[bold green]✓ SUCCESS:[/bold green] {message}")
    else:
        print(f"✓ SUCCESS: {message}")

def print_warning(message: str) -> None:
    """Print a warning message."""
    if RICH_AVAILABLE:
        console.print(f"[bold yellow]⚠ WARNING:[/bold yellow] {message}")
    else:
        print(f"⚠ WARNING: {message}")

def print_error(message: str) -> None:
    """Print an error message."""
    if RICH_AVAILABLE:
        console.print(f"[bold red]✗ ERROR:[/bold red] {message}")
    else:
        print(f"✗ ERROR: {message}")

def print_info(message: str) -> None:
    """Print an info message."""
    if RICH_AVAILABLE:
        console.print(f"[bold cyan]ℹ INFO:[/bold cyan] {message}")
    else:
        print(f"ℹ INFO: {message}")

def run_command(command, shell=True, check=True):
    """Run a shell command and return the output."""
    logger.debug(f"Running command: {command}")

    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=check,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print_error(f"Command failed with exit code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        if check:
            raise
        return None

def generate_password():
    """Generate a random password."""
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

def create_namespace(tenant_id, tenant_name, environment):
    """Create and configure the tenant namespace."""
    logger.info(f"Creating namespace tenant-{tenant_id}")

    # Create namespace
    run_command(f"kubectl create namespace tenant-{tenant_id} --dry-run=client -o yaml | kubectl apply -f -")

    # Label namespace
    tenant_name_label = tenant_name.replace(" ", "-")
    run_command(
        f"kubectl label namespace tenant-{tenant_id} --overwrite "
        f"tenant.architrave.io/tenant-id={tenant_id} "
        f"tenant.architrave.io/tenant-name={tenant_name_label} "
        f"environment={environment} "
        f"istio-injection=enabled "
        f"pod-security.kubernetes.io/enforce=privileged "
        f"pod-security.kubernetes.io/audit=restricted "
        f"pod-security.kubernetes.io/warn=restricted"
    )

    logger.info(f"Namespace tenant-{tenant_id} created successfully")

def import_database(tenant_id, local_sql_file):
    """Import database from local file."""
    logger.info(f"Importing database for tenant-{tenant_id}")

    if not local_sql_file or not os.path.exists(local_sql_file):
        print_warning("No local SQL file provided or file doesn't exist, skipping database import")
        return

    # Create database user
    db_tenant_id = tenant_id.replace('-', '_')
    db_user = f"tenant_{db_tenant_id}"
    db_password = generate_password()

    # Create database bastion pod
    bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-bastion-{tenant_id}
  namespace: tenant-{tenant_id}
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "{DEFAULT_RDS_ADMIN_PASSWORD}"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
      requests:
        cpu: 50m
        memory: 128Mi
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(bastion_yaml)
        bastion_file = f.name

    try:
        # Deploy bastion pod
        run_command(f"kubectl apply -f {bastion_file}")

        # Wait for pod to be ready
        run_command(f"kubectl wait --for=condition=ready pod/db-bastion-{tenant_id} -n tenant-{tenant_id} --timeout=120s")

        # Create database user
        create_user_sql = f"""
CREATE USER IF NOT EXISTS '{db_user}'@'%' IDENTIFIED BY '{db_password}';
GRANT ALL PRIVILEGES ON {DEFAULT_RDS_DATABASE}.* TO '{db_user}'@'%';
FLUSH PRIVILEGES;
"""

        # Execute user creation
        run_command(f"kubectl exec db-bastion-{tenant_id} -n tenant-{tenant_id} -- mysql -h {DEFAULT_RDS_HOST} -P {DEFAULT_RDS_PORT} -u {DEFAULT_RDS_ADMIN_USER} -e \"{create_user_sql}\"")

        # Import SQL file
        print_info("Importing SQL schema...")
        run_command(f"kubectl cp {local_sql_file} tenant-{tenant_id}/db-bastion-{tenant_id}:/tmp/schema.sql")
        run_command(f"kubectl exec db-bastion-{tenant_id} -n tenant-{tenant_id} -- mysql -h {DEFAULT_RDS_HOST} -P {DEFAULT_RDS_PORT} -u {DEFAULT_RDS_ADMIN_USER} {DEFAULT_RDS_DATABASE} < /tmp/schema.sql")

        print_success("Database import completed successfully")

    finally:
        # Cleanup bastion pod
        run_command(f"kubectl delete pod db-bastion-{tenant_id} -n tenant-{tenant_id} --ignore-not-found=true")
        os.unlink(bastion_file)

def create_s3_resources(tenant_id):
    """Create S3 resources for tenant."""
    logger.info(f"Creating S3 resources for tenant-{tenant_id}")

    # Create S3 storage class
    storage_class_yaml = f"""
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-{tenant_id}
provisioner: s3.csi.aws.com
parameters:
  mounter: mountpoint-s3
  awsRegion: eu-central-1
  bucketName: tenant-{tenant_id}-assets
  uid: "33"
  gid: "33"
  dirMode: "0755"
  fileMode: "0644"
reclaimPolicy: Delete
allowVolumeExpansion: true
volumeBindingMode: Immediate
"""

    # Create PV and PVC
    pv_yaml = f"""
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-{tenant_id}
spec:
  capacity:
    storage: 1200Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-{tenant_id}
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-driver-volume-{tenant_id}
    volumeAttributes:
      bucketName: tenant-{tenant_id}-assets
      uid: "33"
      gid: "33"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-{tenant_id}
  namespace: tenant-{tenant_id}
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: s3-sc-{tenant_id}
  resources:
    requests:
      storage: 1200Gi
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(storage_class_yaml)
        sc_file = f.name

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(pv_yaml)
        pv_file = f.name

    try:
        run_command(f"kubectl apply -f {sc_file}")
        run_command(f"kubectl apply -f {pv_file}")
        print_success("S3 resources created successfully")
    finally:
        os.unlink(sc_file)
        os.unlink(pv_file)

def deploy_backend(tenant_id, backend_image):
    """Deploy backend application."""
    logger.info(f"Deploying backend for tenant-{tenant_id}")

    backend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
  labels:
    app: backend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: backend
        tenant: {tenant_id}
    spec:
      containers:
      - name: backend
        image: {backend_image}
        ports:
        - containerPort: 80
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: DB_HOST
          value: "{DEFAULT_RDS_HOST}"
        - name: DB_PORT
          value: "{DEFAULT_RDS_PORT}"
        - name: DB_NAME
          value: "{DEFAULT_RDS_DATABASE}"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: storage
          mountPath: /storage
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: s3-pvc-{tenant_id}
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-backend-service
  namespace: tenant-{tenant_id}
spec:
  selector:
    app: backend
    tenant: {tenant_id}
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(backend_yaml)
        backend_file = f.name

    try:
        run_command(f"kubectl apply -f {backend_file}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-backend -n tenant-{tenant_id} --timeout=300s")
        print_success("Backend deployed successfully")
    finally:
        os.unlink(backend_file)

def deploy_frontend(tenant_id, frontend_image):
    """Deploy frontend application."""
    logger.info(f"Deploying frontend for tenant-{tenant_id}")

    frontend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
  labels:
    app: frontend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: frontend
        tenant: {tenant_id}
    spec:
      containers:
      - name: frontend
        image: {frontend_image}
        ports:
        - containerPort: 80
        - containerPort: 443
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: BACKEND_SERVICE
          value: "tenant-{tenant_id}-backend-service"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-frontend-service
  namespace: tenant-{tenant_id}
spec:
  selector:
    app: frontend
    tenant: {tenant_id}
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: https
    port: 443
    targetPort: 443
  type: ClusterIP
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(frontend_yaml)
        frontend_file = f.name

    try:
        run_command(f"kubectl apply -f {frontend_file}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-frontend -n tenant-{tenant_id} --timeout=300s")
        print_success("Frontend deployed successfully")
    finally:
        os.unlink(frontend_file)

def deploy_rabbitmq(tenant_id, rabbitmq_image):
    """Deploy RabbitMQ application."""
    logger.info(f"Deploying RabbitMQ for tenant-{tenant_id}")

    rabbitmq_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-rabbitmq
  namespace: tenant-{tenant_id}
  labels:
    app: rabbitmq
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: rabbitmq
        tenant: {tenant_id}
    spec:
      containers:
      - name: rabbitmq
        image: {rabbitmq_image}
        ports:
        - containerPort: 5672
        - containerPort: 15672
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: RABBITMQ_DEFAULT_USER
          value: "admin"
        - name: RABBITMQ_DEFAULT_PASS
          value: "admin123"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "300m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-rabbitmq-service
  namespace: tenant-{tenant_id}
spec:
  selector:
    app: rabbitmq
    tenant: {tenant_id}
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  type: ClusterIP
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(rabbitmq_yaml)
        rabbitmq_file = f.name

    try:
        run_command(f"kubectl apply -f {rabbitmq_file}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-rabbitmq -n tenant-{tenant_id} --timeout=300s")
        print_success("RabbitMQ deployed successfully")
    finally:
        os.unlink(rabbitmq_file)

def verify_deployment(tenant_id):
    """Verify the deployment is working."""
    logger.info(f"Verifying deployment for tenant-{tenant_id}")

    verification_results = {}

    # Check namespace
    try:
        result = run_command(f"kubectl get namespace tenant-{tenant_id}", check=False)
        verification_results['namespace'] = "not found" not in result.lower()
    except:
        verification_results['namespace'] = False

    # Check pods
    try:
        result = run_command(f"kubectl get pods -n tenant-{tenant_id} --no-headers", check=False)
        pods = result.split('\n') if result else []
        running_pods = [p for p in pods if 'Running' in p]
        verification_results['pods'] = len(running_pods) >= 3  # backend, frontend, rabbitmq
    except:
        verification_results['pods'] = False

    # Check services
    try:
        result = run_command(f"kubectl get services -n tenant-{tenant_id} --no-headers", check=False)
        services = result.split('\n') if result else []
        verification_results['services'] = len(services) >= 3
    except:
        verification_results['services'] = False

    # Check deployments
    try:
        result = run_command(f"kubectl get deployments -n tenant-{tenant_id} --no-headers", check=False)
        deployments = result.split('\n') if result else []
        ready_deployments = [d for d in deployments if '1/1' in d]
        verification_results['deployments'] = len(ready_deployments) >= 3
    except:
        verification_results['deployments'] = False

    # Print verification results
    print_info("Verification Results:")
    for component, status in verification_results.items():
        if status:
            print_success(f"{component.capitalize()}: ✓")
        else:
            print_error(f"{component.capitalize()}: ✗")

    return verification_results

def onboard_tenant(args):
    """Main tenant onboarding function."""
    tenant_id = args.tenant_id
    tenant_name = args.tenant_name
    subdomain = args.subdomain

    print_header(f"Starting Tenant Onboarding for {tenant_name} (ID: {tenant_id})")

    try:
        # Step 1: Create namespace
        print_step("1", f"Creating namespace for tenant-{tenant_id}")
        create_namespace(tenant_id, tenant_name, args.environment)

        # Step 2: Import database
        if not args.skip_db_import and args.local_sql_file:
            print_step("2", f"Importing database for tenant-{tenant_id}")
            import_database(tenant_id, args.local_sql_file)
        else:
            print_warning("Skipping database import")

        # Step 3: Create S3 resources
        if not args.skip_s3_setup:
            print_step("3", f"Creating S3 resources for tenant-{tenant_id}")
            create_s3_resources(tenant_id)
        else:
            print_warning("Skipping S3 setup")

        # Step 4: Deploy backend
        print_step("4", f"Deploying backend for tenant-{tenant_id}")
        deploy_backend(tenant_id, args.backend_image)

        # Step 5: Deploy frontend
        print_step("5", f"Deploying frontend for tenant-{tenant_id}")
        deploy_frontend(tenant_id, args.frontend_image)

        # Step 6: Deploy RabbitMQ
        print_step("6", f"Deploying RabbitMQ for tenant-{tenant_id}")
        deploy_rabbitmq(tenant_id, args.rabbitmq_image)

        # Step 7: Verify deployment
        print_step("7", f"Verifying deployment for tenant-{tenant_id}")
        verification_results = verify_deployment(tenant_id)

        # Summary
        print_header(f"Tenant Onboarding Summary for {tenant_name} (ID: {tenant_id})")

        if RICH_AVAILABLE:
            table = Table(title="Deployment Information")
            table.add_column("Component", style="cyan")
            table.add_column("Value", style="green")

            table.add_row("Tenant ID", tenant_id)
            table.add_row("Tenant Name", tenant_name)
            table.add_row("Subdomain", subdomain)
            table.add_row("URL", f"https://{subdomain}.{args.domain}")
            table.add_row("Backend Image", args.backend_image)
            table.add_row("Frontend Image", args.frontend_image)
            table.add_row("RabbitMQ Image", args.rabbitmq_image)

            console.print(table)
        else:
            print(f"Tenant ID: {tenant_id}")
            print(f"Tenant Name: {tenant_name}")
            print(f"Subdomain: {subdomain}")
            print(f"URL: https://{subdomain}.{args.domain}")
            print(f"Backend Image: {args.backend_image}")
            print(f"Frontend Image: {args.frontend_image}")
            print(f"RabbitMQ Image: {args.rabbitmq_image}")

        if all(verification_results.values()):
            print_success(f"Tenant onboarding completed successfully for tenant-{tenant_id}")
            return True
        else:
            print_warning(f"Tenant onboarding completed with some issues for tenant-{tenant_id}")
            return False

    except Exception as e:
        print_error(f"Tenant onboarding failed: {e}")
        logger.error(f"Tenant onboarding failed: {e}")
        return False

def main():
    """Main function."""
    # Print welcome banner
    print_header("Fixed Advanced Tenant Onboarding Tool")

    if RICH_AVAILABLE:
        console.print(Panel.fit(
            "[bold blue]Fixed Advanced Tenant Onboarding Tool[/bold blue]\n\n"
            "[cyan]This tool provides reliable tenant onboarding with:[/cyan]\n"
            "- Fixed import issues\n"
            "- Database import from local file\n"
            "- S3 bucket creation and configuration\n"
            "- Kubernetes namespace and resource creation\n"
            "- Deployment of frontend, backend, and RabbitMQ components\n"
            "- Comprehensive verification",
            title="Welcome",
            border_style="blue"
        ))
    else:
        print("Fixed Advanced Tenant Onboarding Tool")
        print("This tool provides reliable tenant onboarding")

    # Parse arguments
    args = parse_arguments()

    # Start onboarding process
    start_time = time.time()
    success = onboard_tenant(args)
    end_time = time.time()

    # Print completion message
    elapsed_time = end_time - start_time
    minutes, seconds = divmod(elapsed_time, 60)

    if success:
        print_header("Tenant Onboarding Completed")
        if RICH_AVAILABLE:
            console.print(Panel.fit(
                f"[bold green]Tenant onboarding completed successfully![/bold green]\n\n"
                f"[cyan]Tenant ID:[/cyan] {args.tenant_id}\n"
                f"[cyan]Tenant Name:[/cyan] {args.tenant_name}\n"
                f"[cyan]URL:[/cyan] https://{args.subdomain}.{args.domain}\n\n"
                f"[cyan]Total time:[/cyan] {int(minutes)} minutes and {int(seconds)} seconds",
                title="Success",
                border_style="green"
            ))
        else:
            print(f"SUCCESS: Tenant onboarding completed!")
            print(f"Tenant ID: {args.tenant_id}")
            print(f"Tenant Name: {args.tenant_name}")
            print(f"URL: https://{args.subdomain}.{args.domain}")
            print(f"Total time: {int(minutes)} minutes and {int(seconds)} seconds")
    else:
        print_header("Tenant Onboarding Failed")
        if RICH_AVAILABLE:
            console.print(Panel.fit(
                f"[bold red]Tenant onboarding failed![/bold red]\n\n"
                f"[cyan]Tenant ID:[/cyan] {args.tenant_id}\n"
                f"[cyan]Tenant Name:[/cyan] {args.tenant_name}\n\n"
                f"[cyan]Total time:[/cyan] {int(minutes)} minutes and {int(seconds)} seconds\n\n"
                f"Please check the logs for more details.",
                title="Error",
                border_style="red"
            ))
        else:
            print(f"ERROR: Tenant onboarding failed!")
            print(f"Tenant ID: {args.tenant_id}")
            print(f"Tenant Name: {args.tenant_name}")
            print(f"Total time: {int(minutes)} minutes and {int(seconds)} seconds")

    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
