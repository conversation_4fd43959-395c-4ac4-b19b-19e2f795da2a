#!/usr/bin/env python3
"""
Database Health Check Script
Verifies database connectivity and runs SELECT statements to verify functionality
"""

import subprocess
import sys
import time
import tempfile
from datetime import datetime

def run_command(command, timeout=30):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def create_db_test_pod():
    """Create a database test pod."""
    print("🔧 Creating database test pod...")
    
    pod_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-health-check
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
      requests:
        cpu: 50m
        memory: 128Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(pod_yaml)
        pod_file = f.name
    
    try:
        # Create pod
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if success:
            print("✅ Database test pod created")
            
            # Wait for pod to be ready
            print("⏳ Waiting for pod to be ready...")
            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/db-health-check --timeout=60s")
            if success:
                print("✅ Database test pod is ready")
                return True
            else:
                print(f"❌ Pod not ready: {stderr}")
                return False
        else:
            print(f"❌ Failed to create pod: {stderr}")
            return False
    finally:
        import os
        os.unlink(pod_file)

def test_database_connectivity():
    """Test basic database connectivity."""
    print("\n🔍 Testing database connectivity...")
    
    db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    db_port = "3306"
    db_user = "admin"
    db_name = "architrave"
    
    # Test basic connection
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} -e 'SELECT 1 as connection_test;'", 15)
    
    connection_success = success and "connection_test" in stdout
    print_result("Database Connection", connection_success, 
                f"Connection to {db_host} {'successful' if connection_success else 'failed'}")
    
    return connection_success

def test_database_queries():
    """Test database queries."""
    print("\n🔍 Testing database queries...")
    
    db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    db_port = "3306"
    db_user = "admin"
    db_name = "architrave"
    
    results = []
    
    # Test 1: Show databases
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} -e 'SHOW DATABASES;'", 15)
    
    databases_visible = success and "architrave" in stdout
    print_result("Show Databases", databases_visible, 
                f"Architrave database {'visible' if databases_visible else 'not visible'}")
    results.append(databases_visible)
    
    # Test 2: Show tables in architrave database
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'SHOW TABLES;'", 15)
    
    tables_visible = success and len(stdout.split('\n')) > 1
    print_result("Show Tables", tables_visible, 
                f"Tables {'found' if tables_visible else 'not found'} in architrave database")
    results.append(tables_visible)
    
    # Test 3: Test a simple SELECT query
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'SELECT NOW() as current_time, VERSION() as mysql_version;'", 15)
    
    select_working = success and "current_time" in stdout
    print_result("SELECT Query", select_working, 
                f"SELECT queries {'working' if select_working else 'failing'}")
    results.append(select_working)
    
    # Test 4: Check if tenant_config table exists (if schema is imported)
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'DESCRIBE tenant_config;' 2>/dev/null", 15)
    
    tenant_table_exists = success and "Field" in stdout
    print_result("Tenant Config Table", tenant_table_exists, 
                f"tenant_config table {'exists' if tenant_table_exists else 'does not exist'}")
    results.append(tenant_table_exists)
    
    return results

def test_database_performance():
    """Test database performance."""
    print("\n🔍 Testing database performance...")
    
    db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    db_port = "3306"
    db_user = "admin"
    db_name = "architrave"
    
    # Test query performance
    start_time = time.time()
    success, stdout, stderr = run_command(
        f"kubectl exec db-health-check -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES;'", 15)
    end_time = time.time()
    
    query_time = end_time - start_time
    performance_good = success and query_time < 5.0  # Should complete in under 5 seconds
    
    print_result("Database Performance", performance_good, 
                f"Query completed in {query_time:.2f} seconds")
    
    return performance_good

def cleanup_test_pod():
    """Clean up the test pod."""
    print("\n🧹 Cleaning up test pod...")
    success, stdout, stderr = run_command("kubectl delete pod db-health-check --ignore-not-found=true")
    if success:
        print("✅ Test pod cleaned up")
    else:
        print(f"⚠️ Cleanup warning: {stderr}")

def main():
    """Main database health check function."""
    print("🔍 DATABASE HEALTH CHECK")
    print("=" * 40)
    print(f"Started at: {datetime.now()}")
    
    try:
        # Create test pod
        if not create_db_test_pod():
            print("❌ Failed to create database test pod")
            return 1
        
        # Test connectivity
        connectivity_success = test_database_connectivity()
        
        # Test queries
        query_results = test_database_queries()
        
        # Test performance
        performance_success = test_database_performance()
        
        # Calculate overall success
        all_tests = [connectivity_success] + query_results + [performance_success]
        passed = sum(all_tests)
        total = len(all_tests)
        
        print(f"\n📊 DATABASE HEALTH SUMMARY")
        print("=" * 40)
        print(f"Tests passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        if passed >= total * 0.8:  # 80% pass rate
            print("🎉 DATABASE HEALTH CHECK PASSED!")
            print("✅ Database is functioning correctly")
            return 0
        else:
            print("⚠️ DATABASE HEALTH CHECK ISSUES")
            print("❌ Database may have connectivity or performance issues")
            return 1
            
    except Exception as e:
        print(f"❌ Database health check error: {e}")
        return 1
    finally:
        cleanup_test_pod()

if __name__ == "__main__":
    sys.exit(main())
