#!/bin/bash

echo "🔍 CHECKING FRESH-TEST TENANT SECURITY STATUS"
echo "=============================================="

TENANT="fresh-test"
NAMESPACE="tenant-$TENANT"

echo "Tenant: $TENANT"
echo "Namespace: $NAMESPACE"
echo ""

echo "1. Pod Security Standards:"
echo "-------------------------"
kubectl get namespace $NAMESPACE -o yaml | grep -A 5 "pod-security" || echo "❌ No Pod Security Standards configured"

echo ""
echo "2. Network Policies:"
echo "-------------------"
kubectl get networkpolicy -n $NAMESPACE 2>/dev/null || echo "❌ No network policies found"

echo ""
echo "3. Istio Security:"
echo "-----------------"
echo "PeerAuthentication:"
kubectl get peerauthentication -n $NAMESPACE 2>/dev/null || echo "❌ No PeerAuthentication found"
echo ""
echo "AuthorizationPolicy:"
kubectl get authorizationpolicy -n $NAMESPACE 2>/dev/null || echo "❌ No AuthorizationPolicy found"

echo ""
echo "4. Resource Management:"
echo "----------------------"
echo "ResourceQuota:"
kubectl get resourcequota -n $NAMESPACE 2>/dev/null || echo "❌ No ResourceQuota found"
echo ""
echo "LimitRange:"
kubectl get limitrange -n $NAMESPACE 2>/dev/null || echo "❌ No LimitRange found"

echo ""
echo "5. Pod Status and Security Contexts:"
echo "------------------------------------"
kubectl get pods -n $NAMESPACE -o wide 2>/dev/null || echo "❌ No pods found"

echo ""
echo "6. Pod Security Context Details:"
echo "--------------------------------"
for pod in $(kubectl get pods -n $NAMESPACE -o jsonpath='{.items[*].metadata.name}' 2>/dev/null); do
    echo "Pod: $pod"
    echo "  Security Context:"
    kubectl get pod $pod -n $NAMESPACE -o jsonpath='{.spec.securityContext}' 2>/dev/null | jq . 2>/dev/null || echo "    No security context or jq not available"
    echo ""
done

echo ""
echo "7. S3 Storage Configuration:"
echo "----------------------------"
kubectl get pvc,pv -n $NAMESPACE | grep s3 || echo "❌ No S3 storage found"

echo ""
echo "8. Database Multi-Tenancy Check:"
echo "--------------------------------"
echo "Checking if tenant_config table exists..."
kubectl exec -n $NAMESPACE deployment/tenant-$TENANT-backend -c backend -- timeout 10 mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -p'&BZzY_<AK(=a*UhZ' --ssl architrave -e "DESCRIBE tenant_config;" 2>/dev/null && echo "✅ tenant_config table exists" || echo "❌ tenant_config table missing"

echo ""
echo "🎯 SECURITY ASSESSMENT SUMMARY"
echo "==============================="

# Count security implementations
SECURITY_SCORE=0
TOTAL_CHECKS=8

# Check Pod Security Standards
if kubectl get namespace $NAMESPACE -o yaml | grep -q "pod-security.kubernetes.io/enforce"; then
    echo "✅ Pod Security Standards: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Pod Security Standards: NOT CONFIGURED"
fi

# Check Network Policies
NP_COUNT=$(kubectl get networkpolicy -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ $NP_COUNT -gt 0 ]; then
    echo "✅ Network Policies: $NP_COUNT policies configured"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Network Policies: NOT CONFIGURED"
fi

# Check Istio mTLS
if kubectl get peerauthentication -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
    echo "✅ Istio mTLS: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Istio mTLS: NOT CONFIGURED"
fi

# Check Authorization Policies
AP_COUNT=$(kubectl get authorizationpolicy -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ $AP_COUNT -gt 0 ]; then
    echo "✅ Authorization Policies: $AP_COUNT policies configured"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Authorization Policies: NOT CONFIGURED"
fi

# Check Resource Quotas
if kubectl get resourcequota -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
    echo "✅ Resource Quotas: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Resource Quotas: NOT CONFIGURED"
fi

# Check Limit Ranges
if kubectl get limitrange -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
    echo "✅ Limit Ranges: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Limit Ranges: NOT CONFIGURED"
fi

# Check S3 Storage
if kubectl get pvc -n $NAMESPACE 2>/dev/null | grep -q s3; then
    echo "✅ S3 Storage: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ S3 Storage: NOT CONFIGURED"
fi

# Check Database Multi-Tenancy
if kubectl exec -n $NAMESPACE deployment/tenant-$TENANT-backend -c backend -- timeout 5 mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -p'&BZzY_<AK(=a*UhZ' --ssl architrave -e "DESCRIBE tenant_config;" >/dev/null 2>&1; then
    echo "✅ Database Multi-Tenancy: CONFIGURED"
    SECURITY_SCORE=$((SECURITY_SCORE + 1))
else
    echo "❌ Database Multi-Tenancy: NOT CONFIGURED"
fi

echo ""
echo "📊 SECURITY SCORE: $SECURITY_SCORE/$TOTAL_CHECKS ($(( SECURITY_SCORE * 100 / TOTAL_CHECKS ))%)"

if [ $SECURITY_SCORE -ge 6 ]; then
    echo "🎉 SECURITY STATUS: GOOD"
elif [ $SECURITY_SCORE -ge 4 ]; then
    echo "⚠️  SECURITY STATUS: NEEDS IMPROVEMENT"
else
    echo "🚨 SECURITY STATUS: CRITICAL - IMMEDIATE ACTION REQUIRED"
fi

echo ""
echo "🔧 RECOMMENDATIONS:"
echo "==================="

if [ $SECURITY_SCORE -lt $TOTAL_CHECKS ]; then
    echo "The following security measures need to be implemented:"
    
    if ! kubectl get namespace $NAMESPACE -o yaml | grep -q "pod-security.kubernetes.io/enforce"; then
        echo "- Add Pod Security Standards to namespace"
    fi
    
    if [ $NP_COUNT -eq 0 ]; then
        echo "- Implement Network Policies for tenant isolation"
    fi
    
    if ! kubectl get peerauthentication -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
        echo "- Configure Istio mTLS for service mesh security"
    fi
    
    if [ $AP_COUNT -eq 0 ]; then
        echo "- Add Istio Authorization Policies"
    fi
    
    if ! kubectl get resourcequota -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
        echo "- Configure Resource Quotas"
    fi
    
    if ! kubectl get limitrange -n $NAMESPACE --no-headers 2>/dev/null | wc -l | grep -q -v "^0$"; then
        echo "- Configure Limit Ranges"
    fi
    
    if ! kubectl get pvc -n $NAMESPACE 2>/dev/null | grep -q s3; then
        echo "- Fix S3 storage configuration"
    fi
    
    if ! kubectl exec -n $NAMESPACE deployment/tenant-$TENANT-backend -c backend -- timeout 5 mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -p'&BZzY_<AK(=a*UhZ' --ssl architrave -e "DESCRIBE tenant_config;" >/dev/null 2>&1; then
        echo "- Implement database multi-tenancy schema"
    fi
else
    echo "✅ All security measures are properly implemented!"
fi
