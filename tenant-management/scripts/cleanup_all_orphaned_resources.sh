#!/bin/bash

# Cleanup All Orphaned Resources Script
# Comprehensive cleanup of all tenant-related orphaned resources

echo "🧹 COMPREHENSIVE CLEANUP OF ALL ORPHANED TENANT RESOURCES"
echo "========================================================="

# Function to safely delete resources
safe_delete() {
    local resource_type=$1
    local resource_name=$2
    local namespace=${3:-""}
    
    if [[ -n "$namespace" ]]; then
        echo "Deleting $resource_type: $resource_name in namespace: $namespace"
        kubectl delete $resource_type $resource_name -n $namespace --ignore-not-found=true --timeout=30s || true
    else
        echo "Deleting $resource_type: $resource_name"
        kubectl delete $resource_type $resource_name --ignore-not-found=true --timeout=30s || true
    fi
}

# Step 1: Clean up orphaned S3 Persistent Volumes
echo "🗑️ Step 1: Cleaning up orphaned S3 Persistent Volumes..."
kubectl get pv | grep tenant | awk '{print $1}' | while read pv_name; do
    if [[ -n "$pv_name" ]]; then
        safe_delete "pv" "$pv_name"
    fi
done

# Step 2: Clean up orphaned Storage Classes
echo "🗑️ Step 2: Cleaning up orphaned Storage Classes..."
kubectl get storageclass | grep tenant | awk '{print $1}' | while read sc_name; do
    if [[ -n "$sc_name" ]]; then
        safe_delete "storageclass" "$sc_name"
    fi
done

# Step 3: Clean up orphaned PVCs in all namespaces
echo "🗑️ Step 3: Cleaning up orphaned PVCs..."
kubectl get pvc --all-namespaces | grep tenant | while read namespace pvc_name rest; do
    if [[ -n "$pvc_name" && -n "$namespace" ]]; then
        safe_delete "pvc" "$pvc_name" "$namespace"
    fi
done

# Step 4: Clean up orphaned ClusterRoles
echo "🗑️ Step 4: Cleaning up orphaned ClusterRoles..."
kubectl get clusterrole | grep tenant | awk '{print $1}' | while read cr_name; do
    if [[ -n "$cr_name" ]]; then
        safe_delete "clusterrole" "$cr_name"
    fi
done

# Step 5: Clean up orphaned ClusterRoleBindings
echo "🗑️ Step 5: Cleaning up orphaned ClusterRoleBindings..."
kubectl get clusterrolebinding | grep tenant | awk '{print $1}' | while read crb_name; do
    if [[ -n "$crb_name" ]]; then
        safe_delete "clusterrolebinding" "$crb_name"
    fi
done

# Step 6: Clean up orphaned Istio VirtualServices
echo "🗑️ Step 6: Cleaning up orphaned Istio VirtualServices..."
kubectl get virtualservice --all-namespaces | grep tenant | while read namespace vs_name rest; do
    if [[ -n "$vs_name" && -n "$namespace" ]]; then
        safe_delete "virtualservice" "$vs_name" "$namespace"
    fi
done

# Step 7: Clean up orphaned Istio DestinationRules
echo "🗑️ Step 7: Cleaning up orphaned Istio DestinationRules..."
kubectl get destinationrule --all-namespaces | grep tenant | while read namespace dr_name rest; do
    if [[ -n "$dr_name" && -n "$namespace" ]]; then
        safe_delete "destinationrule" "$dr_name" "$namespace"
    fi
done

# Step 8: Clean up orphaned Istio PeerAuthentications
echo "🗑️ Step 8: Cleaning up orphaned Istio PeerAuthentications..."
kubectl get peerauthentication --all-namespaces | grep tenant | while read namespace pa_name rest; do
    if [[ -n "$pa_name" && -n "$namespace" ]]; then
        safe_delete "peerauthentication" "$pa_name" "$namespace"
    fi
done

# Step 9: Clean up orphaned Istio Gateways
echo "🗑️ Step 9: Cleaning up orphaned Istio Gateways..."
kubectl get gateway --all-namespaces | grep tenant | while read namespace gw_name rest; do
    if [[ -n "$gw_name" && -n "$namespace" ]]; then
        safe_delete "gateway" "$gw_name" "$namespace"
    fi
done

# Step 10: Clean up orphaned ServiceMonitors
echo "🗑️ Step 10: Cleaning up orphaned ServiceMonitors..."
kubectl get servicemonitor --all-namespaces | grep tenant | while read namespace sm_name rest; do
    if [[ -n "$sm_name" && -n "$namespace" ]]; then
        safe_delete "servicemonitor" "$sm_name" "$namespace"
    fi
done

# Step 11: Clean up orphaned PrometheusRules
echo "🗑️ Step 11: Cleaning up orphaned PrometheusRules..."
kubectl get prometheusrule --all-namespaces | grep tenant | while read namespace pr_name rest; do
    if [[ -n "$pr_name" && -n "$namespace" ]]; then
        safe_delete "prometheusrule" "$pr_name" "$namespace"
    fi
done

# Step 12: Clean up orphaned ConfigMaps
echo "🗑️ Step 12: Cleaning up orphaned tenant ConfigMaps..."
kubectl get configmap --all-namespaces | grep tenant | while read namespace cm_name rest; do
    if [[ -n "$cm_name" && -n "$namespace" ]]; then
        safe_delete "configmap" "$cm_name" "$namespace"
    fi
done

# Step 13: Clean up orphaned Secrets
echo "🗑️ Step 13: Cleaning up orphaned tenant Secrets..."
kubectl get secret --all-namespaces | grep tenant | while read namespace secret_name rest; do
    if [[ -n "$secret_name" && -n "$namespace" ]]; then
        safe_delete "secret" "$secret_name" "$namespace"
    fi
done

# Step 14: Clean up orphaned VolumeAttachments
echo "🗑️ Step 14: Cleaning up orphaned VolumeAttachments..."
kubectl get volumeattachment | grep tenant | awk '{print $1}' | while read va_name; do
    if [[ -n "$va_name" ]]; then
        safe_delete "volumeattachment" "$va_name"
    fi
done

# Step 15: Final verification
echo ""
echo "🔍 FINAL VERIFICATION: Checking for remaining tenant resources"
echo "============================================================="

remaining_resources=0

echo "Checking for remaining tenant namespaces..."
tenant_namespaces=$(kubectl get namespaces | grep tenant- || true)
if [[ -n "$tenant_namespaces" ]]; then
    echo "❌ Remaining tenant namespaces found:"
    echo "$tenant_namespaces"
    remaining_resources=$((remaining_resources + 1))
else
    echo "✅ No tenant namespaces found"
fi

echo "Checking for remaining tenant PVs..."
tenant_pvs=$(kubectl get pv | grep tenant || true)
if [[ -n "$tenant_pvs" ]]; then
    echo "❌ Remaining tenant PVs found:"
    echo "$tenant_pvs"
    remaining_resources=$((remaining_resources + 1))
else
    echo "✅ No tenant PVs found"
fi

echo "Checking for remaining tenant storage classes..."
tenant_scs=$(kubectl get storageclass | grep tenant || true)
if [[ -n "$tenant_scs" ]]; then
    echo "❌ Remaining tenant storage classes found:"
    echo "$tenant_scs"
    remaining_resources=$((remaining_resources + 1))
else
    echo "✅ No tenant storage classes found"
fi

echo "Checking for remaining tenant cluster roles..."
tenant_crs=$(kubectl get clusterrole | grep tenant || true)
if [[ -n "$tenant_crs" ]]; then
    echo "❌ Remaining tenant cluster roles found:"
    echo "$tenant_crs"
    remaining_resources=$((remaining_resources + 1))
else
    echo "✅ No tenant cluster roles found"
fi

echo "Checking for remaining tenant cluster role bindings..."
tenant_crbs=$(kubectl get clusterrolebinding | grep tenant || true)
if [[ -n "$tenant_crbs" ]]; then
    echo "❌ Remaining tenant cluster role bindings found:"
    echo "$tenant_crbs"
    remaining_resources=$((remaining_resources + 1))
else
    echo "✅ No tenant cluster role bindings found"
fi

echo ""
if [[ $remaining_resources -eq 0 ]]; then
    echo "🎉 SUCCESS: COMPLETE CLEANUP ACHIEVED!"
    echo "✅ All orphaned tenant resources have been removed"
    echo "✅ Cluster is now in a clean state"
    echo "🚀 Ready for fresh tenant onboarding!"
else
    echo "⚠️ PARTIAL SUCCESS: Some tenant resources still remain"
    echo "Manual intervention may be required for complete cleanup"
fi

echo ""
echo "🧹 Orphaned resource cleanup completed!"
