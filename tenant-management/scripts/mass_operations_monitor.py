#!/usr/bin/env python3
"""
Mass Operations Monitor
Real-time monitoring of multiple tenant onboarding/offboarding operations
"""

import subprocess
import time
import sys
from datetime import datetime

def run_quick_command(command, timeout=5):
    """Run a command quickly."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip()
    except:
        return False, ""

def get_tenant_namespaces():
    """Get current tenant namespaces."""
    success, output = run_quick_command("kubectl get namespaces --no-headers")
    if success:
        namespaces = [line.split()[0] for line in output.split('\n') if 'tenant-' in line]
        return namespaces
    return []

def get_tenant_pods(namespace):
    """Get pod count for a tenant namespace."""
    success, output = run_quick_command(f"kubectl get pods -n {namespace} --no-headers")
    if success:
        pods = [line for line in output.split('\n') if line.strip()]
        running_pods = [line for line in pods if 'Running' in line]
        return len(pods), len(running_pods)
    return 0, 0

def monitor_operations():
    """Monitor mass operations in real-time."""
    print("🔍 MASS OPERATIONS MONITOR")
    print("=" * 50)
    print(f"Started at: {datetime.now()}")
    print()
    
    # Expected tenants after operations complete
    expected_new_tenants = [
        "enterprise-001",
        "enterprise-002", 
        "startup-alpha",
        "startup-beta",
        "corporate-gamma"
    ]
    
    expected_removed_tenants = [
        "production-tenant-002",
        "test-1748948763",
        "test-tenant-001",
        "final-test"
    ]
    
    cycle = 0
    while cycle < 20:  # Monitor for 20 cycles
        cycle += 1
        print(f"\n📊 MONITORING CYCLE {cycle} - {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 50)
        
        # Get current tenant namespaces
        current_namespaces = get_tenant_namespaces()
        print(f"Current tenant namespaces: {len(current_namespaces)}")
        
        if current_namespaces:
            print("Active tenants:")
            for ns in current_namespaces:
                tenant_id = ns.replace('tenant-', '')
                total_pods, running_pods = get_tenant_pods(ns)
                status = "✅ READY" if running_pods >= 3 else f"🔄 DEPLOYING ({running_pods}/{total_pods})"
                print(f"  - {tenant_id}: {status}")
        else:
            print("  No tenant namespaces found")
        
        # Check for expected new tenants
        print("\n🚀 Expected new tenants:")
        for tenant in expected_new_tenants:
            ns = f"tenant-{tenant}"
            if ns in current_namespaces:
                total_pods, running_pods = get_tenant_pods(ns)
                status = "✅ DEPLOYED" if running_pods >= 3 else f"🔄 DEPLOYING ({running_pods}/3)"
                print(f"  - {tenant}: {status}")
            else:
                print(f"  - {tenant}: ⏳ PENDING")
        
        # Check for removed tenants
        print("\n🧹 Expected removed tenants:")
        for tenant in expected_removed_tenants:
            ns = f"tenant-{tenant}"
            if ns not in current_namespaces:
                print(f"  - {tenant}: ✅ REMOVED")
            else:
                print(f"  - {tenant}: 🔄 REMOVING")
        
        # Calculate progress
        deployed_count = sum(1 for tenant in expected_new_tenants if f"tenant-{tenant}" in current_namespaces)
        removed_count = sum(1 for tenant in expected_removed_tenants if f"tenant-{tenant}" not in current_namespaces)
        
        print(f"\n📈 PROGRESS:")
        print(f"  Onboarding: {deployed_count}/{len(expected_new_tenants)} tenants deployed")
        print(f"  Offboarding: {removed_count}/{len(expected_removed_tenants)} tenants removed")
        
        # Check if operations are complete
        if deployed_count == len(expected_new_tenants) and removed_count == len(expected_removed_tenants):
            print("\n🎉 ALL MASS OPERATIONS COMPLETED SUCCESSFULLY!")
            print(f"✅ {len(expected_new_tenants)} tenants onboarded")
            print(f"✅ {len(expected_removed_tenants)} tenants offboarded")
            break
        
        # Wait before next cycle
        time.sleep(10)
    
    print(f"\nMonitoring completed at: {datetime.now()}")
    return deployed_count, removed_count

def main():
    """Main monitoring function."""
    try:
        deployed, removed = monitor_operations()
        
        print("\n" + "=" * 50)
        print("📊 FINAL MASS OPERATIONS SUMMARY")
        print("=" * 50)
        print(f"✅ Tenants successfully onboarded: {deployed}")
        print(f"✅ Tenants successfully offboarded: {removed}")
        
        if deployed == 5 and removed == 4:
            print("🎉 MASS OPERATIONS 100% SUCCESSFUL!")
            return 0
        else:
            print("⚠️ Some operations may still be in progress")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️ Monitoring stopped by user")
        return 1
    except Exception as e:
        print(f"\n❌ Monitoring error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
