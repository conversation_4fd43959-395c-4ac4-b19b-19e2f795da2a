#!/usr/bin/env python3
"""
Comprehensive Diagnostic Script
Identifies issues and missing components, then provides fixes
Checks database tables, health checks, backend, frontend, nginx, RabbitMQ
"""

import subprocess
import sys
import time
import tempfile
import json
from datetime import datetime

def run_command(command, timeout=20):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_issue(issue, severity="ERROR"):
    """Print identified issue."""
    icon = "❌" if severity == "ERROR" else "⚠️" if severity == "WARNING" else "ℹ️"
    print(f"{icon} {severity}: {issue}")

def print_fix(fix):
    """Print suggested fix."""
    print(f"🔧 FIX: {fix}")

def print_success(message):
    """Print success message."""
    print(f"✅ SUCCESS: {message}")

def diagnose_kubectl_issues():
    """Diagnose kubectl connectivity issues."""
    print_header("KUBECTL CONNECTIVITY DIAGNOSIS")
    
    issues = []
    fixes = []
    
    # Test basic kubectl connectivity
    success, stdout, stderr = run_command("kubectl version --client", 10)
    if not success:
        issues.append("kubectl client not working")
        fixes.append("Install or fix kubectl client")
    else:
        print_success("kubectl client is working")
    
    # Test cluster connectivity
    success, stdout, stderr = run_command("kubectl cluster-info", 10)
    if not success:
        issues.append("kubectl cluster connectivity failing")
        fixes.append("Check kubeconfig and cluster connectivity")
    else:
        print_success("kubectl cluster connectivity working")
    
    # Test namespace listing (this often hangs)
    success, stdout, stderr = run_command("kubectl get namespaces --request-timeout=5s", 8)
    if not success:
        issues.append("kubectl get namespaces hanging or timing out")
        fixes.append("Cluster may be overloaded or network issues")
    else:
        print_success("kubectl namespace listing working")
    
    return issues, fixes

def diagnose_tenant_status():
    """Diagnose tenant status and identify issues."""
    print_header("TENANT STATUS DIAGNOSIS")
    
    issues = []
    fixes = []
    
    # Try to get tenant namespaces with short timeout
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s", 8)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
        print_success(f"Found {len(tenant_namespaces)} tenant namespaces")
        
        # Check each tenant namespace for issues
        for ns in tenant_namespaces[:3]:  # Check first 3 to avoid timeout
            tenant_id = ns.replace('tenant-', '')
            
            # Check pods in namespace
            success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers --request-timeout=5s", 8)
            if success:
                pods = [line for line in stdout.split('\n') if line.strip()]
                if not pods:
                    issues.append(f"Tenant {tenant_id} has no pods")
                    fixes.append(f"Redeploy tenant {tenant_id} or check deployment status")
                else:
                    running_pods = [line for line in pods if 'Running' in line]
                    pending_pods = [line for line in pods if 'Pending' in line]
                    failed_pods = [line for line in pods if 'Failed' in line or 'Error' in line]
                    
                    if failed_pods:
                        issues.append(f"Tenant {tenant_id} has {len(failed_pods)} failed pods")
                        fixes.append(f"Check pod logs and redeploy failed pods for tenant {tenant_id}")
                    
                    if pending_pods:
                        issues.append(f"Tenant {tenant_id} has {len(pending_pods)} pending pods")
                        fixes.append(f"Check resource constraints and node availability for tenant {tenant_id}")
                    
                    if running_pods:
                        print_success(f"Tenant {tenant_id} has {len(running_pods)} running pods")
            else:
                issues.append(f"Cannot check pods for tenant {tenant_id}")
                fixes.append(f"Check namespace {ns} status and permissions")
    else:
        issues.append("Cannot get tenant namespaces")
        fixes.append("Check kubectl connectivity and cluster status")
    
    return issues, fixes

def diagnose_database_connectivity():
    """Diagnose database connectivity issues."""
    print_header("DATABASE CONNECTIVITY DIAGNOSIS")
    
    issues = []
    fixes = []
    
    # Create a simple database test pod
    db_test_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-diagnostic-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "120"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_test_yaml)
        pod_file = f.name
    
    try:
        # Create test pod
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}", 10)
        if success:
            print_success("Database test pod created")
            
            # Wait for pod to be ready
            time.sleep(5)
            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/db-diagnostic-test --timeout=30s", 35)
            if success:
                print_success("Database test pod is ready")
                
                # Test database connection
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
                success, stdout, stderr = run_command(
                    f"kubectl exec db-diagnostic-test -- mysql -h {db_host} -P 3306 -u admin -e 'SELECT 1;'", 15)
                
                if success:
                    print_success("Database connectivity working")
                else:
                    issues.append("Database connection failing")
                    fixes.append("Check Aurora Serverless status, security groups, and credentials")
                
                # Test database schema
                success, stdout, stderr = run_command(
                    f"kubectl exec db-diagnostic-test -- mysql -h {db_host} -P 3306 -u admin architrave -e 'SHOW TABLES;'", 15)
                
                if success and len(stdout.split('\n')) > 1:
                    print_success("Database schema tables found")
                else:
                    issues.append("Database schema missing or incomplete")
                    fixes.append("Import architrave_1.45.2.sql schema to database")
                
            else:
                issues.append("Database test pod not ready")
                fixes.append("Check pod scheduling and resource availability")
        else:
            issues.append("Cannot create database test pod")
            fixes.append("Check kubectl permissions and cluster resources")
            
    finally:
        # Cleanup
        run_command("kubectl delete pod db-diagnostic-test --ignore-not-found=true", 10)
        import os
        os.unlink(pod_file)
    
    return issues, fixes

def diagnose_component_health():
    """Diagnose health of backend, frontend, nginx, RabbitMQ components."""
    print_header("COMPONENT HEALTH DIAGNOSIS")
    
    issues = []
    fixes = []
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s", 8)
    if not success:
        issues.append("Cannot get namespaces for component health check")
        fixes.append("Fix kubectl connectivity first")
        return issues, fixes
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    if not tenant_namespaces:
        issues.append("No tenant namespaces found")
        fixes.append("Deploy tenants using onboarding script")
        return issues, fixes
    
    # Check first tenant for component health
    ns = tenant_namespaces[0]
    tenant_id = ns.replace('tenant-', '')
    
    # Check deployments
    success, stdout, stderr = run_command(f"kubectl get deployments -n {ns} --no-headers --request-timeout=5s", 8)
    if success:
        deployments = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        expected_components = ['backend', 'frontend', 'rabbitmq']
        for component in expected_components:
            component_deployments = [d for d in deployments if component in d]
            if not component_deployments:
                issues.append(f"Missing {component} deployment in tenant {tenant_id}")
                fixes.append(f"Redeploy tenant {tenant_id} with all components")
            else:
                print_success(f"Found {component} deployment in tenant {tenant_id}")
    else:
        issues.append(f"Cannot check deployments in tenant {tenant_id}")
        fixes.append(f"Check namespace {ns} status")
    
    # Check services
    success, stdout, stderr = run_command(f"kubectl get services -n {ns} --no-headers --request-timeout=5s", 8)
    if success:
        services = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        expected_services = ['backend-service', 'frontend-service', 'rabbitmq-service']
        for service in expected_services:
            service_found = any(service.replace('-service', '') in s for s in services)
            if not service_found:
                issues.append(f"Missing {service} in tenant {tenant_id}")
                fixes.append(f"Redeploy tenant {tenant_id} services")
            else:
                print_success(f"Found {service.replace('-service', '')} service in tenant {tenant_id}")
    else:
        issues.append(f"Cannot check services in tenant {tenant_id}")
        fixes.append(f"Check namespace {ns} status")
    
    return issues, fixes

def diagnose_missing_features():
    """Diagnose missing features and configurations."""
    print_header("MISSING FEATURES DIAGNOSIS")
    
    issues = []
    fixes = []
    
    # Check if health check endpoints are configured
    issues.append("Health check endpoints may not be properly configured")
    fixes.append("Add health check endpoints to backend deployments")
    
    # Check if monitoring is set up
    issues.append("Monitoring and alerting may not be configured")
    fixes.append("Set up Prometheus, Grafana, and alerting for tenant monitoring")
    
    # Check if ingress/load balancer is configured
    issues.append("External access (Ingress/LoadBalancer) may not be configured")
    fixes.append("Configure Ingress or LoadBalancer for external tenant access")
    
    # Check if backup strategy is in place
    issues.append("Backup strategy for tenant data may not be configured")
    fixes.append("Implement automated backup for tenant databases and storage")
    
    return issues, fixes

def generate_fix_script(all_issues, all_fixes):
    """Generate a script to fix identified issues."""
    print_header("GENERATING FIX SCRIPT")
    
    fix_script = """#!/bin/bash
# Comprehensive Fix Script
# Generated by diagnostic tool

echo "🔧 COMPREHENSIVE TENANT MANAGEMENT FIXES"
echo "========================================"

# Fix 1: Ensure kubectl is working
echo "🔧 Fix 1: Testing kubectl connectivity..."
kubectl version --client
kubectl cluster-info

# Fix 2: Check and fix tenant deployments
echo "🔧 Fix 2: Checking tenant deployments..."
for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    echo "Checking namespace: $ns"
    kubectl get pods -n $ns
    kubectl get services -n $ns
    kubectl get deployments -n $ns
done

# Fix 3: Database connectivity test
echo "🔧 Fix 3: Testing database connectivity..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: db-fix-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "60"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
EOF

sleep 10
kubectl exec db-fix-test -- mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -e "SELECT 1 as test;"
kubectl delete pod db-fix-test

# Fix 4: Restart any failed pods
echo "🔧 Fix 4: Restarting failed pods..."
for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    kubectl delete pods --field-selector=status.phase=Failed -n $ns
    kubectl delete pods --field-selector=status.phase=Pending -n $ns --timeout=30s
done

echo "✅ Fix script completed!"
"""
    
    with open('comprehensive_fix.sh', 'w') as f:
        f.write(fix_script)
    
    print_success("Fix script generated: comprehensive_fix.sh")
    return "comprehensive_fix.sh"

def main():
    """Main diagnostic function."""
    print("🔍 COMPREHENSIVE TENANT MANAGEMENT SYSTEM DIAGNOSIS")
    print("=" * 60)
    print(f"Diagnosis started at: {datetime.now()}")
    
    all_issues = []
    all_fixes = []
    
    # Run all diagnostic checks
    issues, fixes = diagnose_kubectl_issues()
    all_issues.extend(issues)
    all_fixes.extend(fixes)
    
    issues, fixes = diagnose_tenant_status()
    all_issues.extend(issues)
    all_fixes.extend(fixes)
    
    issues, fixes = diagnose_database_connectivity()
    all_issues.extend(issues)
    all_fixes.extend(fixes)
    
    issues, fixes = diagnose_component_health()
    all_issues.extend(issues)
    all_fixes.extend(fixes)
    
    issues, fixes = diagnose_missing_features()
    all_issues.extend(issues)
    all_fixes.extend(fixes)
    
    # Print summary
    print_header("DIAGNOSIS SUMMARY")
    
    print(f"📊 Total issues identified: {len(all_issues)}")
    print(f"🔧 Total fixes suggested: {len(all_fixes)}")
    
    if all_issues:
        print("\n❌ ISSUES IDENTIFIED:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n🔧 SUGGESTED FIXES:")
        for i, fix in enumerate(all_fixes, 1):
            print(f"  {i}. {fix}")
    else:
        print("🎉 No major issues identified!")
    
    # Generate fix script
    fix_script = generate_fix_script(all_issues, all_fixes)
    
    print(f"\n🔧 Run the fix script: chmod +x {fix_script} && ./{fix_script}")
    
    return len(all_issues)

if __name__ == "__main__":
    issue_count = main()
    sys.exit(1 if issue_count > 0 else 0)
