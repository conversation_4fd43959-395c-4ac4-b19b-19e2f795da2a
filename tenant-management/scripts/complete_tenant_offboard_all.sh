#!/bin/bash

# Complete Tenant Offboarding Script - ALL TENANTS
# Systematically offboards ALL existing tenants for a clean slate

set -e

echo "🧹 COMPLETE TENANT OFFBOARDING - ALL TENANTS"
echo "==========================================="
echo "This script will:"
echo "  🗑️  Find all existing tenant namespaces"
echo "  🗑️  Offboard each tenant completely"
echo "  🗑️  Clean up databases, S3 buckets, and all resources"
echo "  🗑️  Ensure 100% clean slate"
echo ""

# Function to get all tenant namespaces
get_all_tenant_namespaces() {
    echo "🔍 Discovering all tenant namespaces..."
    
    # Get all namespaces that start with "tenant-"
    local tenant_namespaces
    tenant_namespaces=$(kubectl get namespaces -o name | grep "namespace/tenant-" | sed 's|namespace/||' || true)
    
    if [[ -z "$tenant_namespaces" ]]; then
        echo "✅ No tenant namespaces found"
        return 0
    fi
    
    echo "📋 Found tenant namespaces:"
    echo "$tenant_namespaces" | while read -r ns; do
        echo "  - $ns"
    done
    
    echo "$tenant_namespaces"
}

# Function to extract tenant ID from namespace
extract_tenant_id() {
    local namespace=$1
    # Remove "tenant-" prefix to get tenant ID
    echo "${namespace#tenant-}"
}

# Function to offboard a single tenant using the existing script
offboard_single_tenant() {
    local tenant_id=$1
    
    echo ""
    echo "🗑️ Offboarding tenant: $tenant_id"
    echo "================================"
    
    # Check if the advanced offboarding script exists
    if [[ -f "./advanced_tenant_offboard.py" ]]; then
        echo "Using advanced offboarding script..."
        
        # Run the advanced offboarding script with timeout
        if timeout 300 python3 advanced_tenant_offboard.py --tenant-id "$tenant_id" --force; then
            echo "✅ Tenant $tenant_id offboarded successfully using advanced script"
            return 0
        else
            echo "⚠️ Advanced script failed or timed out, falling back to manual cleanup"
        fi
    fi
    
    # Fallback to manual cleanup
    echo "Performing manual cleanup for tenant: $tenant_id"
    manual_tenant_cleanup "$tenant_id"
}

# Function to perform manual tenant cleanup
manual_tenant_cleanup() {
    local tenant_id=$1
    local namespace="tenant-$tenant_id"
    
    echo "🧹 Manual cleanup for tenant: $tenant_id"
    
    # 1. Delete namespace (this will delete most resources)
    echo "Deleting namespace: $namespace"
    kubectl delete namespace "$namespace" --ignore-not-found=true --timeout=120s || {
        echo "⚠️ Namespace deletion timed out, forcing deletion..."
        kubectl patch namespace "$namespace" -p '{"metadata":{"finalizers":[]}}' --type=merge || true
        kubectl delete namespace "$namespace" --ignore-not-found=true --force --grace-period=0 || true
    }
    
    # 2. Clean up any cluster-wide resources
    echo "Cleaning up cluster-wide resources for tenant: $tenant_id"
    
    # Delete cluster role bindings
    kubectl delete clusterrolebinding "tenant-$tenant_id-binding" --ignore-not-found=true || true
    
    # Delete cluster roles
    kubectl delete clusterrole "tenant-$tenant_id-role" --ignore-not-found=true || true
    
    # Delete persistent volumes
    kubectl get pv -o name | grep "$tenant_id" | xargs -r kubectl delete --ignore-not-found=true || true
    
    # 3. Clean up monitoring resources
    echo "Cleaning up monitoring resources..."
    kubectl delete servicemonitor "tenant-$tenant_id-monitor" -n monitoring --ignore-not-found=true || true
    kubectl delete prometheusrule "tenant-$tenant_id-alerts" -n monitoring --ignore-not-found=true || true
    kubectl delete configmap "tenant-$tenant_id-dashboard" -n monitoring --ignore-not-found=true || true
    
    # 4. Clean up Istio resources
    echo "Cleaning up Istio resources..."
    kubectl delete virtualservice "tenant-$tenant_id-vs" --ignore-not-found=true --all-namespaces || true
    kubectl delete destinationrule "tenant-$tenant_id-dr" --ignore-not-found=true --all-namespaces || true
    kubectl delete peerauthentication "tenant-$tenant_id-pa" --ignore-not-found=true --all-namespaces || true
    
    # 5. Database cleanup (if credentials are available)
    echo "Attempting database cleanup..."
    cleanup_tenant_database "$tenant_id" || echo "⚠️ Database cleanup failed or skipped"
    
    # 6. S3 cleanup
    echo "Attempting S3 cleanup..."
    cleanup_tenant_s3 "$tenant_id" || echo "⚠️ S3 cleanup failed or skipped"
    
    echo "✅ Manual cleanup completed for tenant: $tenant_id"
}

# Function to cleanup tenant database
cleanup_tenant_database() {
    local tenant_id=$1
    local db_name="tenant_${tenant_id//-/_}"  # Replace hyphens with underscores
    local db_user="tenant_${tenant_id//-/_}"
    
    echo "Cleaning up database for tenant: $tenant_id"
    
    # Create a temporary pod for database operations
    cat <<EOF | kubectl apply -f - || return 1
apiVersion: v1
kind: Pod
metadata:
  name: db-cleanup-$tenant_id
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ['sleep', '300']
    env:
    - name: MYSQL_PWD
      value: '&BZzY_<AK(=a*UhZ'
EOF
    
    # Wait for pod to be ready
    kubectl wait --for=condition=ready pod/db-cleanup-$tenant_id --timeout=60s || return 1
    
    # Drop database if exists
    kubectl exec db-cleanup-$tenant_id -- mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -e "DROP DATABASE IF EXISTS $db_name;" || true
    
    # Drop user if exists
    kubectl exec db-cleanup-$tenant_id -- mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -e "DROP USER IF EXISTS '$db_user'@'%';" || true
    
    # Flush privileges
    kubectl exec db-cleanup-$tenant_id -- mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -e "FLUSH PRIVILEGES;" || true
    
    # Cleanup pod
    kubectl delete pod db-cleanup-$tenant_id --ignore-not-found=true
    
    echo "✅ Database cleanup completed for tenant: $tenant_id"
}

# Function to cleanup tenant S3 bucket
cleanup_tenant_s3() {
    local tenant_id=$1
    local bucket_name="tenant-$tenant_id-assets"
    
    echo "Cleaning up S3 bucket: $bucket_name"
    
    # Check if bucket exists
    if aws s3 ls "s3://$bucket_name" >/dev/null 2>&1; then
        echo "Bucket exists, emptying and deleting..."
        
        # Empty bucket
        aws s3 rm "s3://$bucket_name" --recursive || true
        
        # Delete bucket
        aws s3 rb "s3://$bucket_name" --force || true
        
        echo "✅ S3 bucket $bucket_name deleted"
    else
        echo "ℹ️ S3 bucket $bucket_name does not exist"
    fi
}

# Function to verify complete cleanup
verify_cleanup() {
    echo ""
    echo "🔍 VERIFICATION: Checking for remaining tenant resources"
    echo "====================================================="
    
    local cleanup_complete=true
    
    # Check for tenant namespaces
    local remaining_namespaces
    remaining_namespaces=$(kubectl get namespaces -o name | grep "namespace/tenant-" | sed 's|namespace/||' || true)
    
    if [[ -n "$remaining_namespaces" ]]; then
        echo "❌ Remaining tenant namespaces found:"
        echo "$remaining_namespaces"
        cleanup_complete=false
    else
        echo "✅ No tenant namespaces remaining"
    fi
    
    # Check for tenant pods in any namespace
    local remaining_pods
    remaining_pods=$(kubectl get pods --all-namespaces -o wide | grep tenant- || true)
    
    if [[ -n "$remaining_pods" ]]; then
        echo "❌ Remaining tenant pods found:"
        echo "$remaining_pods"
        cleanup_complete=false
    else
        echo "✅ No tenant pods remaining"
    fi
    
    # Check for tenant services
    local remaining_services
    remaining_services=$(kubectl get services --all-namespaces | grep tenant- || true)
    
    if [[ -n "$remaining_services" ]]; then
        echo "❌ Remaining tenant services found:"
        echo "$remaining_services"
        cleanup_complete=false
    else
        echo "✅ No tenant services remaining"
    fi
    
    # Check for tenant persistent volumes
    local remaining_pvs
    remaining_pvs=$(kubectl get pv | grep tenant- || true)
    
    if [[ -n "$remaining_pvs" ]]; then
        echo "❌ Remaining tenant persistent volumes found:"
        echo "$remaining_pvs"
        cleanup_complete=false
    else
        echo "✅ No tenant persistent volumes remaining"
    fi
    
    if [[ "$cleanup_complete" == true ]]; then
        echo ""
        echo "🎉 COMPLETE CLEANUP VERIFICATION PASSED!"
        echo "✅ All tenant resources have been successfully removed"
        echo "✅ Clean slate achieved - ready for fresh tenant onboarding"
        return 0
    else
        echo ""
        echo "⚠️ CLEANUP VERIFICATION FAILED"
        echo "Some tenant resources still remain - manual intervention may be required"
        return 1
    fi
}

# Main execution function
main() {
    echo "🚀 Starting complete tenant offboarding process..."
    echo ""
    
    # Get all tenant namespaces
    local tenant_namespaces
    tenant_namespaces=$(get_all_tenant_namespaces)
    
    if [[ -z "$tenant_namespaces" ]]; then
        echo "✅ No tenants found - system is already clean"
        verify_cleanup
        return 0
    fi
    
    # Count tenants
    local tenant_count
    tenant_count=$(echo "$tenant_namespaces" | wc -l)
    
    echo "📊 Found $tenant_count tenant(s) to offboard"
    echo ""
    
    # Offboard each tenant
    local offboarded_count=0
    local failed_count=0
    
    echo "$tenant_namespaces" | while read -r namespace; do
        if [[ -n "$namespace" ]]; then
            local tenant_id
            tenant_id=$(extract_tenant_id "$namespace")
            
            if offboard_single_tenant "$tenant_id"; then
                ((offboarded_count++))
                echo "✅ Successfully offboarded tenant: $tenant_id"
            else
                ((failed_count++))
                echo "❌ Failed to offboard tenant: $tenant_id"
            fi
        fi
    done
    
    echo ""
    echo "📊 OFFBOARDING SUMMARY"
    echo "====================="
    echo "Total tenants found: $tenant_count"
    echo "Successfully offboarded: $offboarded_count"
    echo "Failed: $failed_count"
    echo ""
    
    # Final verification
    if verify_cleanup; then
        echo ""
        echo "🎉 COMPLETE TENANT OFFBOARDING SUCCESSFUL!"
        echo "========================================="
        echo "✅ All tenants have been completely offboarded"
        echo "✅ All databases cleaned up"
        echo "✅ All S3 buckets removed"
        echo "✅ All Kubernetes resources deleted"
        echo "✅ System is now in a clean slate state"
        echo ""
        echo "🚀 Ready for fresh tenant onboarding with comprehensive security!"
    else
        echo ""
        echo "⚠️ PARTIAL SUCCESS - Some cleanup required"
        echo "Please review the verification output above"
        exit 1
    fi
}

# Run main function
main
