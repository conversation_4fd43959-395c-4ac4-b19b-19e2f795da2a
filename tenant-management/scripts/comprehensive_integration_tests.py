#!/usr/bin/env python3
"""
Comprehensive Integration Test Suite for Tenant Onboarding/Offboarding
This script provides comprehensive testing for all tenant components.
"""

import subprocess
import json
import time
import sys
import logging
from typing import Dict, List, Tuple, Optional

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TenantIntegrationTester:
    """Comprehensive integration tester for tenant onboarding/offboarding."""

    def __init__(self, tenant_id: str):
        self.tenant_id = tenant_id
        self.namespace = f"tenant-{tenant_id}"
        self.test_results = {}

    def run_command(self, command: str, check: bool = True) -> str:
        """Execute a shell command and return output."""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
            return result.stdout.strip()
        except subprocess.CalledProcessError as e:
            if check:
                logger.error(f"Command failed: {command}")
                logger.error(f"Error: {e.stderr}")
                raise
            return e.stderr.strip()

    def test_namespace_exists(self) -> bool:
        """Test if tenant namespace exists."""
        logger.info(f"Testing namespace existence for {self.namespace}")
        try:
            output = self.run_command(f"kubectl get namespace {self.namespace}")
            return self.namespace in output
        except:
            return False

    def test_pods_running(self) -> Dict[str, bool]:
        """Test if all tenant pods are running."""
        logger.info(f"Testing pod status for {self.namespace}")
        results = {}

        components = ['backend', 'frontend', 'rabbitmq']
        for component in components:
            try:
                output = self.run_command(
                    f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-{component} "
                    f"-o jsonpath='{{.items[*].status.phase}}'"
                )
                results[component] = "Running" in output
            except:
                results[component] = False

        return results

    def test_services_accessible(self) -> Dict[str, bool]:
        """Test if all tenant services are accessible."""
        logger.info(f"Testing service accessibility for {self.namespace}")
        results = {}

        services = {
            'backend': 'webapp:8080',
            'frontend': f'tenant-{self.tenant_id}-frontend:80',
            'rabbitmq': f'tenant-{self.tenant_id}-rabbitmq:5672'
        }

        for service_name, service_endpoint in services.items():
            try:
                # Test service exists
                output = self.run_command(f"kubectl get service -n {self.namespace} {service_endpoint.split(':')[0]}")
                results[service_name] = service_endpoint.split(':')[0] in output
            except:
                results[service_name] = False

        return results

    def test_database_connectivity(self) -> bool:
        """Test database connectivity from backend pod."""
        logger.info(f"Testing database connectivity for {self.namespace}")
        try:
            # Get backend pod name
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not backend_pod:
                return False

            # Test database connection with proper environment variable access
            db_test = self.run_command(
                f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                f"php -r \"try {{ "
                f"\\$host = getenv('DB_HOST') ?: getenv('MYSQL_HOST'); "
                f"\\$port = getenv('DB_PORT') ?: '3306'; "
                f"\\$dbname = getenv('DB_NAME') ?: getenv('MYSQL_DATABASE'); "
                f"\\$user = getenv('DB_USER') ?: getenv('MYSQL_USER'); "
                f"\\$password = getenv('DB_PASSWORD') ?: getenv('MYSQL_PASSWORD'); "
                f"if (!\\$host || !\\$user || !\\$password || !\\$dbname) {{ "
                f"echo 'FAILED: Missing environment variables'; exit(1); }} "
                f"\\$dsn = \\\"mysql:host=\\$host;port=\\$port;dbname=\\$dbname\\\"; "
                f"\\$options = [PDO::MYSQL_ATTR_SSL_CA => '/tmp/rds-ca-2019-root.pem', PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false, PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]; "
                f"\\$pdo = new PDO(\\$dsn, \\$user, \\$password, \\$options); "
                f"echo 'SUCCESS'; }} catch(Exception \\$e) {{ echo 'FAILED: ' . \\$e->getMessage(); }}\""
            )

            return "SUCCESS" in db_test
        except Exception as e:
            logger.error(f"Database connectivity test failed: {e}")
            return False

    def test_frontend_backend_integration(self) -> bool:
        """Test frontend to backend communication."""
        logger.info(f"Testing frontend-backend integration for {self.namespace}")
        try:
            # Get frontend pod name
            frontend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-frontend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not frontend_pod:
                return False

            # Test backend connectivity from frontend
            integration_test = self.run_command(
                f"kubectl exec -n {self.namespace} {frontend_pod} -c frontend -- "
                f"curl -s -o /dev/null -w '%{{http_code}}' http://webapp:8080/health-check.php"
            )

            return integration_test == "200"
        except Exception as e:
            logger.error(f"Frontend-backend integration test failed: {e}")
            return False

    def test_rabbitmq_connectivity(self) -> bool:
        """Test RabbitMQ connectivity."""
        logger.info(f"Testing RabbitMQ connectivity for {self.namespace}")
        try:
            # Get backend pod name
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not backend_pod:
                return False

            # Test RabbitMQ connectivity using network test (AMQP extension not available)
            rabbitmq_test = self.run_command(
                f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                f"timeout 5 bash -c 'echo > /dev/tcp/tenant-{self.tenant_id}-rabbitmq/5672' && echo 'SUCCESS' || echo 'FAILED'"
            )

            return "SUCCESS" in rabbitmq_test
        except Exception as e:
            logger.error(f"RabbitMQ connectivity test failed: {e}")
            return False

    def test_s3_access(self) -> bool:
        """Test S3 bucket access."""
        logger.info(f"Testing S3 access for {self.namespace}")
        try:
            # Get backend pod name
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not backend_pod:
                return False

            # Test S3 write access (write to temp subdirectory)
            s3_test = self.run_command(
                f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                f"sh -c 'echo \"test-$(date)\" > /storage/s3/temp/integration-test.txt && echo SUCCESS || echo FAILED'"
            )

            return "SUCCESS" in s3_test
        except Exception as e:
            logger.error(f"S3 access test failed: {e}")
            return False

    def test_health_endpoints(self) -> Dict[str, bool]:
        """Test all health endpoints."""
        logger.info(f"Testing health endpoints for {self.namespace}")
        results = {}

        try:
            # Test frontend health endpoint
            frontend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-frontend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if frontend_pod:
                frontend_health = self.run_command(
                    f"kubectl exec -n {self.namespace} {frontend_pod} -c frontend -- "
                    f"curl -s -o /dev/null -w '%{{http_code}}' http://localhost:80/health"
                )
                results['frontend'] = frontend_health == "200"
            else:
                results['frontend'] = False

            # Test backend health endpoint
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if backend_pod:
                backend_health = self.run_command(
                    f"kubectl exec -n {self.namespace} {backend_pod} -c nginx -- "
                    f"curl -s -o /dev/null -w '%{{http_code}}' http://localhost:8080/health"
                )
                results['backend'] = backend_health == "200"
            else:
                results['backend'] = False

        except Exception as e:
            logger.error(f"Health endpoint test failed: {e}")
            results['frontend'] = False
            results['backend'] = False

        return results

    def test_cli_functionality(self) -> Dict[str, bool]:
        """Test Architrave CLI functionality."""
        logger.info(f"Testing CLI functionality for {self.namespace}")
        results = {}

        try:
            # Get backend pod name
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not backend_pod:
                return {'cli_available': False, 'cli_help': False, 'cli_config': False}

            # Test CLI availability
            try:
                cli_check = self.run_command(
                    f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                    f"test -f /storage/ArchAssets/bin/architrave && echo 'EXISTS' || echo 'MISSING'"
                )
                results['cli_available'] = "EXISTS" in cli_check
            except:
                results['cli_available'] = False

            # Test CLI help command
            try:
                cli_help = self.run_command(
                    f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                    f"timeout 10 /storage/ArchAssets/bin/architrave --help 2>&1 | head -5"
                )
                results['cli_help'] = "Available commands" in cli_help or "Usage:" in cli_help
            except:
                results['cli_help'] = False

            # Test CLI configuration
            try:
                cli_config = self.run_command(
                    f"kubectl exec -n {self.namespace} {backend_pod} -c backend -- "
                    f"test -f /storage/ArchAssets/config/autoload/local.php && echo 'CONFIG_EXISTS' || echo 'CONFIG_MISSING'"
                )
                results['cli_config'] = "CONFIG_EXISTS" in cli_config
            except:
                results['cli_config'] = False

        except Exception as e:
            logger.error(f"CLI functionality test failed: {e}")
            results = {'cli_available': False, 'cli_help': False, 'cli_config': False}

        return results

    def test_istio_proxy_status(self) -> bool:
        """Test Istio proxy readiness."""
        logger.info(f"Testing Istio proxy status for {self.namespace}")
        try:
            # Get backend pod name
            backend_pod = self.run_command(
                f"kubectl get pods -n {self.namespace} -l app=tenant-{self.tenant_id}-backend "
                f"-o jsonpath='{{.items[0].metadata.name}}'"
            )

            if not backend_pod:
                return False

            # Check istio-proxy container readiness
            proxy_status = self.run_command(
                f"kubectl get pod -n {self.namespace} {backend_pod} "
                f"-o jsonpath='{{.status.containerStatuses[?(@.name==\"istio-proxy\")].ready}}'"
            )

            return "true" in proxy_status
        except Exception as e:
            logger.error(f"Istio proxy status test failed: {e}")
            return False

    def run_comprehensive_tests(self) -> Dict[str, any]:
        """Run all comprehensive tests."""
        logger.info(f"Starting comprehensive integration tests for tenant-{self.tenant_id}")

        results = {
            'tenant_id': self.tenant_id,
            'namespace_exists': self.test_namespace_exists(),
            'pods_running': self.test_pods_running(),
            'services_accessible': self.test_services_accessible(),
            'database_connectivity': self.test_database_connectivity(),
            'frontend_backend_integration': self.test_frontend_backend_integration(),
            'rabbitmq_connectivity': self.test_rabbitmq_connectivity(),
            's3_access': self.test_s3_access(),
            'health_endpoints': self.test_health_endpoints(),
            'cli_functionality': self.test_cli_functionality(),
            'istio_proxy_ready': self.test_istio_proxy_status()
        }

        # Calculate overall success rate
        total_tests = 0
        passed_tests = 0

        for key, value in results.items():
            if key == 'tenant_id':
                continue
            elif isinstance(value, bool):
                total_tests += 1
                if value:
                    passed_tests += 1
            elif isinstance(value, dict):
                for sub_key, sub_value in value.items():
                    total_tests += 1
                    if sub_value:
                        passed_tests += 1

        results['success_rate'] = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        results['total_tests'] = total_tests
        results['passed_tests'] = passed_tests

        return results

def main():
    """Main function to run integration tests."""
    if len(sys.argv) != 2:
        print("Usage: python3 comprehensive_integration_tests.py <tenant_id>")
        sys.exit(1)

    tenant_id = sys.argv[1]
    tester = TenantIntegrationTester(tenant_id)

    print(f"🧪 Running comprehensive integration tests for tenant-{tenant_id}")
    print("=" * 80)

    results = tester.run_comprehensive_tests()

    # Print results
    print(f"\n📊 Test Results for tenant-{tenant_id}:")
    print("=" * 80)
    print(f"Namespace Exists: {'✅' if results['namespace_exists'] else '❌'}")

    print(f"\nPod Status:")
    for component, status in results['pods_running'].items():
        print(f"  {component}: {'✅' if status else '❌'}")

    print(f"\nService Accessibility:")
    for service, status in results['services_accessible'].items():
        print(f"  {service}: {'✅' if status else '❌'}")

    print(f"\nConnectivity Tests:")
    print(f"  Database: {'✅' if results['database_connectivity'] else '❌'}")
    print(f"  Frontend-Backend: {'✅' if results['frontend_backend_integration'] else '❌'}")
    print(f"  RabbitMQ: {'✅' if results['rabbitmq_connectivity'] else '❌'}")
    print(f"  S3 Access: {'✅' if results['s3_access'] else '❌'}")

    print(f"\nHealth Endpoints:")
    for endpoint, status in results['health_endpoints'].items():
        print(f"  {endpoint}: {'✅' if status else '❌'}")

    print(f"\nCLI Functionality:")
    for cli_test, status in results['cli_functionality'].items():
        print(f"  {cli_test}: {'✅' if status else '❌'}")

    print(f"\nIstio Configuration:")
    print(f"  Proxy Ready: {'✅' if results['istio_proxy_ready'] else '❌'}")

    print(f"\n📈 Overall Results:")
    print(f"Success Rate: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']} tests passed)")

    if results['success_rate'] >= 80:
        print("🎉 Integration tests PASSED!")
        return 0
    else:
        print("❌ Integration tests FAILED!")
        return 1

if __name__ == "__main__":
    sys.exit(main())
