#!/usr/bin/env python3
"""
Simple Tenant Check Script
Basic script to check tenant status without complex operations
"""

import subprocess
import sys

def simple_kubectl_check():
    """Simple kubectl connectivity check."""
    try:
        print("🔍 Testing kubectl connectivity...")
        result = subprocess.run(
            ["kubectl", "version", "--client"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ kubectl client is working")
            print(f"Client version: {result.stdout.strip()}")
        else:
            print("❌ kubectl client failed")
            return False
        
        # Test cluster connectivity
        print("🔍 Testing cluster connectivity...")
        result = subprocess.run(
            ["kubectl", "cluster-info"], 
            capture_output=True, 
            text=True, 
            timeout=15
        )
        
        if result.returncode == 0:
            print("✅ Cluster connectivity working")
            return True
        else:
            print("❌ Cluster connectivity failed")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ kubectl commands timed out")
        return False
    except Exception as e:
        print(f"❌ kubectl test failed: {e}")
        return False

def check_tenant_namespaces():
    """Check for tenant namespaces."""
    try:
        print("🔍 Checking for tenant namespaces...")
        result = subprocess.run(
            ["kubectl", "get", "namespaces", "-o", "name"], 
            capture_output=True, 
            text=True, 
            timeout=15
        )
        
        if result.returncode == 0:
            namespaces = result.stdout.strip().split('\n')
            tenant_namespaces = [ns for ns in namespaces if 'tenant-' in ns]
            
            if tenant_namespaces:
                print(f"📋 Found {len(tenant_namespaces)} tenant namespace(s):")
                for ns in tenant_namespaces:
                    print(f"  - {ns}")
                return tenant_namespaces
            else:
                print("✅ No tenant namespaces found")
                return []
        else:
            print("❌ Failed to get namespaces")
            print(f"Error: {result.stderr}")
            return None
            
    except subprocess.TimeoutExpired:
        print("❌ Namespace check timed out")
        return None
    except Exception as e:
        print(f"❌ Namespace check failed: {e}")
        return None

def manual_offboard_commands(tenant_namespaces):
    """Generate manual offboarding commands."""
    if not tenant_namespaces:
        return
    
    print("\n🔧 MANUAL OFFBOARDING COMMANDS")
    print("=" * 40)
    print("If automated offboarding fails, run these commands manually:")
    print()
    
    for ns in tenant_namespaces:
        tenant_id = ns.replace('namespace/tenant-', '')
        print(f"# Offboard tenant: {tenant_id}")
        print(f"python3 advanced_tenant_offboard.py --tenant-id {tenant_id} --force")
        print(f"# OR manual cleanup:")
        print(f"kubectl delete namespace tenant-{tenant_id} --force --grace-period=0")
        print()

def main():
    """Main function."""
    print("🧹 SIMPLE TENANT CHECK & OFFBOARDING GUIDE")
    print("=" * 50)
    
    # Test kubectl
    if not simple_kubectl_check():
        print("\n❌ kubectl is not working properly")
        print("Please check your Kubernetes configuration")
        return False
    
    print()
    
    # Check for tenants
    tenant_namespaces = check_tenant_namespaces()
    
    if tenant_namespaces is None:
        print("\n❌ Could not check tenant namespaces")
        return False
    
    if not tenant_namespaces:
        print("\n🎉 SYSTEM IS ALREADY CLEAN!")
        print("✅ No tenant namespaces found")
        print("✅ Ready for fresh tenant onboarding")
        return True
    
    # Generate manual commands
    manual_offboard_commands(tenant_namespaces)
    
    print("🔧 RECOMMENDED ACTIONS:")
    print("1. Run the advanced offboarding script for each tenant")
    print("2. If that fails, use the manual kubectl delete commands")
    print("3. Verify cleanup with: kubectl get namespaces | grep tenant-")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
