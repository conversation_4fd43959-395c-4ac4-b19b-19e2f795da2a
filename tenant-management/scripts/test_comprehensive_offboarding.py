#!/usr/bin/env python3
"""
Test Comprehensive Offboarding Script
Tests the enhanced advanced offboarding script with all cleanup features
"""

import subprocess
import sys
import time
import argparse

def run_command(cmd, check=True):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {e.stderr}")
            raise
        return e.stdout.strip(), e.stderr.strip(), e.returncode

def find_all_tenant_resources():
    """Find all tenant-related resources in the cluster."""
    print("🔍 Scanning cluster for ALL tenant-related resources...")
    
    resources_found = {}
    
    # Check namespaces
    stdout, stderr, rc = run_command("kubectl get namespaces | grep tenant-", check=False)
    if stdout:
        resources_found['namespaces'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['namespaces'])} tenant namespaces")
    
    # Check PVs
    stdout, stderr, rc = run_command("kubectl get pv | grep tenant", check=False)
    if stdout:
        resources_found['pvs'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['pvs'])} tenant PVs")
    
    # Check Storage Classes
    stdout, stderr, rc = run_command("kubectl get storageclass | grep tenant", check=False)
    if stdout:
        resources_found['storage_classes'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['storage_classes'])} tenant storage classes")
    
    # Check ClusterRoles
    stdout, stderr, rc = run_command("kubectl get clusterrole | grep tenant", check=False)
    if stdout:
        resources_found['cluster_roles'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['cluster_roles'])} tenant cluster roles")
    
    # Check ClusterRoleBindings
    stdout, stderr, rc = run_command("kubectl get clusterrolebinding | grep tenant", check=False)
    if stdout:
        resources_found['cluster_role_bindings'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['cluster_role_bindings'])} tenant cluster role bindings")
    
    # Check VirtualServices
    stdout, stderr, rc = run_command("kubectl get virtualservice --all-namespaces | grep tenant", check=False)
    if stdout:
        resources_found['virtual_services'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['virtual_services'])} tenant virtual services")
    
    # Check DestinationRules
    stdout, stderr, rc = run_command("kubectl get destinationrule --all-namespaces | grep tenant", check=False)
    if stdout:
        resources_found['destination_rules'] = stdout.split('\n')
        print(f"📋 Found {len(resources_found['destination_rules'])} tenant destination rules")
    
    return resources_found

def extract_tenant_ids_from_resources(resources_found):
    """Extract unique tenant IDs from found resources."""
    tenant_ids = set()
    
    for resource_type, resource_list in resources_found.items():
        for resource in resource_list:
            if 'tenant-' in resource:
                # Extract tenant ID from resource name
                parts = resource.split()
                for part in parts:
                    if part.startswith('tenant-'):
                        tenant_id = part.replace('tenant-', '').split('-')[0]
                        if tenant_id:
                            tenant_ids.add(tenant_id)
    
    return list(tenant_ids)

def test_offboard_tenant(tenant_id, force=True):
    """Test offboarding a specific tenant."""
    print(f"\n🗑️ Testing offboarding for tenant: {tenant_id}")
    print("=" * 60)
    
    # Run the enhanced offboarding script
    cmd = f"python3 advanced_tenant_offboard.py --tenant-id {tenant_id} --force --verify"
    
    print(f"Running: {cmd}")
    
    start_time = time.time()
    stdout, stderr, rc = run_command(cmd, check=False)
    elapsed_time = time.time() - start_time
    
    print(f"⏱️ Offboarding took {elapsed_time:.2f} seconds")
    
    if rc == 0:
        print(f"✅ Tenant {tenant_id} offboarded successfully")
        return True
    else:
        print(f"❌ Tenant {tenant_id} offboarding failed")
        if stderr:
            print(f"Error: {stderr}")
        return False

def verify_complete_cleanup():
    """Verify that all tenant resources have been cleaned up."""
    print("\n🔍 FINAL VERIFICATION: Checking for remaining tenant resources")
    print("=" * 70)
    
    remaining_resources = find_all_tenant_resources()
    
    if not remaining_resources:
        print("🎉 SUCCESS: No tenant resources found - complete cleanup achieved!")
        return True
    else:
        print("⚠️ WARNING: Some tenant resources still remain:")
        for resource_type, resource_list in remaining_resources.items():
            print(f"  - {resource_type}: {len(resource_list)} items")
            for resource in resource_list[:3]:  # Show first 3 items
                print(f"    • {resource}")
            if len(resource_list) > 3:
                print(f"    ... and {len(resource_list) - 3} more")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test Comprehensive Tenant Offboarding')
    parser.add_argument('--tenant-id', help='Specific tenant ID to offboard')
    parser.add_argument('--all', action='store_true', help='Offboard all found tenants')
    parser.add_argument('--dry-run', action='store_true', help='Only scan and show what would be offboarded')
    
    args = parser.parse_args()
    
    print("🧹 COMPREHENSIVE TENANT OFFBOARDING TEST")
    print("=" * 50)
    
    # Find all tenant resources
    resources_found = find_all_tenant_resources()
    
    if not resources_found:
        print("✅ No tenant resources found - cluster is already clean!")
        return True
    
    # Extract tenant IDs
    tenant_ids = extract_tenant_ids_from_resources(resources_found)
    
    if not tenant_ids:
        print("⚠️ Found tenant resources but couldn't extract tenant IDs")
        print("Manual cleanup may be required")
        return False
    
    print(f"\n📋 Found {len(tenant_ids)} unique tenant(s): {', '.join(tenant_ids)}")
    
    if args.dry_run:
        print("\n🔍 DRY RUN - Would offboard the following tenants:")
        for tenant_id in tenant_ids:
            print(f"  - {tenant_id}")
        return True
    
    # Determine which tenants to offboard
    tenants_to_offboard = []
    
    if args.tenant_id:
        if args.tenant_id in tenant_ids:
            tenants_to_offboard = [args.tenant_id]
        else:
            print(f"❌ Tenant {args.tenant_id} not found in cluster")
            return False
    elif args.all:
        tenants_to_offboard = tenant_ids
    else:
        print("\nPlease specify --tenant-id <id> or --all to proceed")
        print("Use --dry-run to see what would be offboarded")
        return False
    
    # Offboard tenants
    successful_offboards = 0
    failed_offboards = 0
    
    for tenant_id in tenants_to_offboard:
        if test_offboard_tenant(tenant_id):
            successful_offboards += 1
        else:
            failed_offboards += 1
        
        # Small delay between offboards
        if len(tenants_to_offboard) > 1:
            time.sleep(2)
    
    # Final verification
    print(f"\n📊 OFFBOARDING SUMMARY")
    print("=" * 30)
    print(f"Total tenants processed: {len(tenants_to_offboard)}")
    print(f"Successfully offboarded: {successful_offboards}")
    print(f"Failed: {failed_offboards}")
    
    # Verify complete cleanup
    cleanup_success = verify_complete_cleanup()
    
    if cleanup_success and failed_offboards == 0:
        print("\n🎉 COMPREHENSIVE OFFBOARDING TEST PASSED!")
        print("✅ All tenants offboarded successfully")
        print("✅ All tenant resources cleaned up")
        print("✅ Cluster is in clean state")
        return True
    else:
        print("\n⚠️ COMPREHENSIVE OFFBOARDING TEST COMPLETED WITH ISSUES")
        if failed_offboards > 0:
            print(f"❌ {failed_offboards} tenant(s) failed to offboard")
        if not cleanup_success:
            print("❌ Some tenant resources still remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
