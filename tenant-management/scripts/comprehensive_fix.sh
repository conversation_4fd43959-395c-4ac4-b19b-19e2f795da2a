#!/bin/bash
# Comprehensive Fix Script
# Generated by diagnostic tool

echo "🔧 COMPREHENSIVE TENANT MANAGEMENT FIXES"
echo "========================================"

# Fix 1: Ensure kubectl is working
echo "🔧 Fix 1: Testing kubectl connectivity..."
kubectl version --client
kubectl cluster-info

# Fix 2: Check and fix tenant deployments
echo "🔧 Fix 2: Checking tenant deployments..."
for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    echo "Checking namespace: $ns"
    kubectl get pods -n $ns
    kubectl get services -n $ns
    kubectl get deployments -n $ns
done

# Fix 3: Database connectivity test
echo "🔧 Fix 3: Testing database connectivity..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: db-fix-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "60"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
EOF

sleep 10
kubectl exec db-fix-test -- mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -e "SELECT 1 as test;"
kubectl delete pod db-fix-test

# Fix 4: Restart any failed pods
echo "🔧 Fix 4: Restarting failed pods..."
for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    kubectl delete pods --field-selector=status.phase=Failed -n $ns
    kubectl delete pods --field-selector=status.phase=Pending -n $ns --timeout=30s
done

echo "✅ Fix script completed!"
