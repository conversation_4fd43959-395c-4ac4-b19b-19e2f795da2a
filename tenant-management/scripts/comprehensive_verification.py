#!/usr/bin/env python3
"""
Comprehensive Verification Script for Tenant Management System
Verifies onboarding/offboarding scripts, health checks, backend, frontend, nginx, database
"""

import subprocess
import sys
import time
import json
from datetime import datetime

def run_command(command, timeout=30):
    """Run a command with timeout and return result."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def verify_scripts_functionality():
    """Verify that scripts are working correctly."""
    print_header("SCRIPT FUNCTIONALITY VERIFICATION")
    
    results = []
    
    # Test onboarding script help
    success, stdout, stderr = run_command("python3 advanced_tenant_onboard.py --help", 15)
    help_working = success and "tenant-id" in stdout
    print_result("Onboarding Script Help", help_working, "Help command works without hanging")
    results.append(("Onboarding Help", help_working))
    
    # Test offboarding script help
    success, stdout, stderr = run_command("python3 advanced_tenant_offboard.py --help", 15)
    help_working = success and "tenant-id" in stdout
    print_result("Offboarding Script Help", help_working, "Help command works without hanging")
    results.append(("Offboarding Help", help_working))
    
    # Test script import speed (no hanging)
    success, stdout, stderr = run_command("python3 -c 'exec(open(\"advanced_tenant_onboard.py\").read()[:1000])'", 10)
    import_working = success
    print_result("Onboarding Script Import Speed", import_working, "No hanging imports detected")
    results.append(("Import Speed", import_working))
    
    return results

def verify_tenant_status():
    """Verify current tenant status."""
    print_header("TENANT STATUS VERIFICATION")
    
    results = []
    
    # Get current tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers")
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
        print_result("Tenant Namespaces Discovery", True, f"Found {len(tenant_namespaces)} tenant namespaces")
        results.append(("Namespace Discovery", True))
        
        # Check each tenant namespace
        for ns in tenant_namespaces[:5]:  # Check first 5 tenants
            tenant_id = ns.replace('tenant-', '')
            
            # Check pods in namespace
            success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers")
            if success:
                pods = [line for line in stdout.split('\n') if line.strip()]
                running_pods = [line for line in pods if 'Running' in line]
                pod_status = len(running_pods) >= 1  # At least 1 pod running
                print_result(f"Tenant {tenant_id} Pods", pod_status, f"{len(running_pods)}/{len(pods)} pods running")
                results.append((f"Tenant {tenant_id} Pods", pod_status))
            else:
                print_result(f"Tenant {tenant_id} Pods", False, "Could not get pod status")
                results.append((f"Tenant {tenant_id} Pods", False))
    else:
        print_result("Tenant Namespaces Discovery", False, "Could not get namespaces")
        results.append(("Namespace Discovery", False))
    
    return results

def verify_backend_health():
    """Verify backend health for active tenants."""
    print_header("BACKEND HEALTH VERIFICATION")
    
    results = []
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers")
    if not success:
        print_result("Backend Health Check", False, "Could not get namespaces")
        return [("Backend Health", False)]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    for ns in tenant_namespaces[:3]:  # Check first 3 tenants
        tenant_id = ns.replace('tenant-', '')
        
        # Check if backend service exists
        success, stdout, stderr = run_command(f"kubectl get service -n {ns} --no-headers")
        if success:
            services = [line for line in stdout.split('\n') if 'backend' in line]
            backend_service_exists = len(services) > 0
            print_result(f"Tenant {tenant_id} Backend Service", backend_service_exists, 
                        f"Backend service {'found' if backend_service_exists else 'not found'}")
            results.append((f"Tenant {tenant_id} Backend Service", backend_service_exists))
            
            # Check backend deployment
            success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers")
            if success:
                deployments = [line for line in stdout.split('\n') if 'backend' in line]
                backend_deployment_exists = len(deployments) > 0
                print_result(f"Tenant {tenant_id} Backend Deployment", backend_deployment_exists,
                           f"Backend deployment {'found' if backend_deployment_exists else 'not found'}")
                results.append((f"Tenant {tenant_id} Backend Deployment", backend_deployment_exists))
        else:
            print_result(f"Tenant {tenant_id} Backend Check", False, "Could not check services")
            results.append((f"Tenant {tenant_id} Backend", False))
    
    return results

def verify_frontend_nginx():
    """Verify frontend/nginx for active tenants."""
    print_header("FRONTEND/NGINX VERIFICATION")
    
    results = []
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers")
    if not success:
        print_result("Frontend/Nginx Check", False, "Could not get namespaces")
        return [("Frontend/Nginx", False)]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    for ns in tenant_namespaces[:3]:  # Check first 3 tenants
        tenant_id = ns.replace('tenant-', '')
        
        # Check if frontend service exists
        success, stdout, stderr = run_command(f"kubectl get service -n {ns} --no-headers")
        if success:
            services = [line for line in stdout.split('\n') if 'frontend' in line]
            frontend_service_exists = len(services) > 0
            print_result(f"Tenant {tenant_id} Frontend Service", frontend_service_exists,
                        f"Frontend service {'found' if frontend_service_exists else 'not found'}")
            results.append((f"Tenant {tenant_id} Frontend Service", frontend_service_exists))
            
            # Check frontend deployment
            success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers")
            if success:
                deployments = [line for line in stdout.split('\n') if 'frontend' in line]
                frontend_deployment_exists = len(deployments) > 0
                print_result(f"Tenant {tenant_id} Frontend Deployment", frontend_deployment_exists,
                           f"Frontend deployment {'found' if frontend_deployment_exists else 'not found'}")
                results.append((f"Tenant {tenant_id} Frontend Deployment", frontend_deployment_exists))
        else:
            print_result(f"Tenant {tenant_id} Frontend Check", False, "Could not check services")
            results.append((f"Tenant {tenant_id} Frontend", False))
    
    return results

def verify_database_connectivity():
    """Verify database connectivity."""
    print_header("DATABASE CONNECTIVITY VERIFICATION")
    
    results = []
    
    # Test basic database connectivity using a simple pod
    db_test_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-test-pod
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "60"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
"""
    
    # Create test pod
    success, stdout, stderr = run_command("echo '" + db_test_yaml + "' | kubectl apply -f -")
    if success:
        print_result("Database Test Pod Creation", True, "Test pod created")
        
        # Wait for pod to be ready
        time.sleep(10)
        
        # Test database connection
        db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        success, stdout, stderr = run_command(
            f"kubectl exec db-test-pod -- mysql -h {db_host} -P 3306 -u admin -e 'SELECT 1 as test;'", 15)
        
        db_connectivity = success and "test" in stdout
        print_result("Database Connectivity", db_connectivity, 
                    f"Database connection {'successful' if db_connectivity else 'failed'}")
        results.append(("Database Connectivity", db_connectivity))
        
        # Cleanup test pod
        run_command("kubectl delete pod db-test-pod --ignore-not-found=true")
    else:
        print_result("Database Test Pod Creation", False, "Could not create test pod")
        results.append(("Database Test", False))
    
    return results

def verify_feature_flags():
    """Verify feature flags and configuration."""
    print_header("FEATURE FLAGS VERIFICATION")
    
    results = []
    
    # Check if scripts have proper feature flags
    success, stdout, stderr = run_command("grep -n 'skip-db-import\\|skip-s3-setup\\|skip-monitoring' advanced_tenant_onboard.py")
    feature_flags_exist = success and "skip-db-import" in stdout
    print_result("Onboarding Feature Flags", feature_flags_exist, "Skip flags available for flexible deployment")
    results.append(("Feature Flags", feature_flags_exist))
    
    # Check if offboarding has force and verify flags
    success, stdout, stderr = run_command("grep -n 'force\\|verify' advanced_tenant_offboard.py")
    offboard_flags_exist = success and "force" in stdout
    print_result("Offboarding Feature Flags", offboard_flags_exist, "Force and verify flags available")
    results.append(("Offboarding Flags", offboard_flags_exist))
    
    return results

def main():
    """Main verification function."""
    print("🔍 COMPREHENSIVE TENANT MANAGEMENT SYSTEM VERIFICATION")
    print("=" * 60)
    print(f"Verification started at: {datetime.now()}")
    
    all_results = []
    
    # Run all verification tests
    all_results.extend(verify_scripts_functionality())
    all_results.extend(verify_tenant_status())
    all_results.extend(verify_backend_health())
    all_results.extend(verify_frontend_nginx())
    all_results.extend(verify_database_connectivity())
    all_results.extend(verify_feature_flags())
    
    # Print final summary
    print_header("COMPREHENSIVE VERIFICATION SUMMARY")
    
    passed = 0
    total = len(all_results)
    
    for test_name, success in all_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nVerification Summary: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed >= total * 0.8:  # 80% pass rate
        print("🎉 SYSTEM VERIFICATION SUCCESSFUL!")
        print("✅ Tenant management system is working correctly!")
        return 0
    else:
        print("⚠️ SYSTEM VERIFICATION ISSUES DETECTED")
        print("❌ Some components need attention")
        return 1

if __name__ == "__main__":
    sys.exit(main())
