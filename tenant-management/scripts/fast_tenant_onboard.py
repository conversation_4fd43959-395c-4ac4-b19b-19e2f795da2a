#!/usr/bin/env python3
"""
Fast Tenant Onboarding Script
Optimized version with reduced timeouts and better error handling.
"""

import argparse
import logging
import subprocess
import sys
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

def run_command(command, check=True):
    """Run a shell command and return the output."""
    logger.debug(f"Running command: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=check,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        if check:
            raise
        return None

def create_namespace(tenant_id):
    """Create namespace for the tenant."""
    logger.info(f"Creating namespace for tenant-{tenant_id}")
    
    namespace_yaml = f"""
apiVersion: v1
kind: Namespace
metadata:
  name: tenant-{tenant_id}
  labels:
    tenant: {tenant_id}
    istio-injection: enabled
"""
    
    with open(f"/tmp/namespace-{tenant_id}.yaml", "w") as f:
        f.write(namespace_yaml)
    
    run_command(f"kubectl apply -f /tmp/namespace-{tenant_id}.yaml")
    logger.info(f"✅ Namespace tenant-{tenant_id} created")

def deploy_backend_fast(tenant_id):
    """Deploy backend with optimized configuration for faster startup."""
    logger.info(f"Deploying fast backend for tenant-{tenant_id}")
    
    backend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
  labels:
    app: tenant-{tenant_id}-backend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-{tenant_id}-backend
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-backend
        tenant: {tenant_id}
    spec:
      containers:
      - name: backend
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          value: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        - name: DB_PORT
          value: "3306"
        - name: DB_NAME
          value: "tenant_{tenant_id.replace('-', '_')}"
        - name: DB_USER
          value: "tenant_{tenant_id.replace('-', '_')}_user"
        - name: DB_PASSWORD
          value: "SecurePassword123!"
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 3
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 20
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          requests:
            cpu: "50m"
            memory: "128Mi"
          limits:
            cpu: "200m"
            memory: "256Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
  labels:
    app: tenant-{tenant_id}-backend
spec:
  selector:
    app: tenant-{tenant_id}-backend
  ports:
  - port: 8080
    targetPort: 8080
    name: http
---
apiVersion: v1
kind: Service
metadata:
  name: webapp
  namespace: tenant-{tenant_id}
  labels:
    app: tenant-{tenant_id}-backend
spec:
  selector:
    app: tenant-{tenant_id}-backend
  ports:
  - port: 8080
    targetPort: 8080
    name: http
"""
    
    with open(f"/tmp/backend-{tenant_id}.yaml", "w") as f:
        f.write(backend_yaml)
    
    run_command(f"kubectl apply -f /tmp/backend-{tenant_id}.yaml")
    logger.info(f"✅ Backend for tenant-{tenant_id} deployed")

def deploy_frontend_fast(tenant_id):
    """Deploy frontend with optimized configuration."""
    logger.info(f"Deploying fast frontend for tenant-{tenant_id}")
    
    frontend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
  labels:
    app: tenant-{tenant_id}-frontend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-{tenant_id}-frontend
  template:
    metadata:
      labels:
        app: tenant-{tenant_id}-frontend
        tenant: {tenant_id}
    spec:
      containers:
      - name: frontend
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 2
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 10
          timeoutSeconds: 2
          failureThreshold: 3
        resources:
          requests:
            cpu: "25m"
            memory: "64Mi"
          limits:
            cpu: "100m"
            memory: "128Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
  labels:
    app: tenant-{tenant_id}-frontend
spec:
  selector:
    app: tenant-{tenant_id}-frontend
  ports:
  - port: 8080
    targetPort: 8080
    name: http
"""
    
    with open(f"/tmp/frontend-{tenant_id}.yaml", "w") as f:
        f.write(frontend_yaml)
    
    run_command(f"kubectl apply -f /tmp/frontend-{tenant_id}.yaml")
    logger.info(f"✅ Frontend for tenant-{tenant_id} deployed")

def wait_for_pods_fast(tenant_id):
    """Wait for pods with optimized timeout."""
    logger.info(f"⏳ Waiting for pods to be ready (fast mode)...")
    
    # Wait for pods to be created
    time.sleep(15)
    
    # Check pod status
    pod_status = run_command(f"kubectl get pods -n tenant-{tenant_id}", check=False)
    logger.info(f"📊 Pod status:\n{pod_status}")
    
    # Wait with shorter timeout
    try:
        run_command(f"kubectl wait --for=condition=ready pod --all -n tenant-{tenant_id} --timeout=120s")
        logger.info("✅ All pods are ready")
        return True
    except subprocess.CalledProcessError:
        logger.warning("⚠️ Some pods may not be ready yet, checking individually...")
        
        # Check individual pods
        pods = run_command(f"kubectl get pods -n tenant-{tenant_id} -o jsonpath='{{.items[*].metadata.name}}'", check=False)
        ready_pods = 0
        total_pods = 0
        
        if pods:
            for pod in pods.split():
                total_pods += 1
                pod_ready = run_command(f"kubectl get pod {pod} -n tenant-{tenant_id} -o jsonpath='{{.status.conditions[?(@.type==\"Ready\")].status}}'", check=False)
                if pod_ready == "True":
                    logger.info(f"✅ Pod {pod} is ready")
                    ready_pods += 1
                else:
                    logger.warning(f"⚠️ Pod {pod} is not ready")
        
        logger.info(f"📊 Ready pods: {ready_pods}/{total_pods}")
        return ready_pods > 0  # Continue if at least one pod is ready

def test_integration_fast(tenant_id):
    """Quick integration test."""
    logger.info(f"🧪 Running fast integration test for tenant-{tenant_id}")
    
    try:
        # Test frontend health
        frontend_pod = run_command(f"kubectl get pods -n tenant-{tenant_id} -l app=tenant-{tenant_id}-frontend -o jsonpath='{{.items[0].metadata.name}}'", check=False)
        if frontend_pod:
            health_test = run_command(f"kubectl exec -n tenant-{tenant_id} {frontend_pod} -- curl -s -o /dev/null -w '%{{http_code}}' http://localhost:8080/health", check=False)
            if "200" in health_test:
                logger.info("✅ Frontend health check passed")
                return True
        
        logger.warning("⚠️ Integration test inconclusive")
        return False
    except Exception as e:
        logger.warning(f"⚠️ Integration test failed: {e}")
        return False

def main():
    """Main function for fast tenant onboarding."""
    parser = argparse.ArgumentParser(description="Fast tenant onboarding")
    parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    parser.add_argument("--tenant-name", required=True, help="Tenant name")
    parser.add_argument("--subdomain", required=True, help="Subdomain")
    
    args = parser.parse_args()
    
    start_time = datetime.now()
    logger.info(f"🚀 Starting FAST tenant onboarding for {args.tenant_id}")
    
    try:
        # Step 1: Create namespace
        create_namespace(args.tenant_id)
        
        # Step 2: Deploy backend
        deploy_backend_fast(args.tenant_id)
        
        # Step 3: Deploy frontend
        deploy_frontend_fast(args.tenant_id)
        
        # Step 4: Wait for pods (with shorter timeout)
        pods_ready = wait_for_pods_fast(args.tenant_id)
        
        # Step 5: Quick integration test
        integration_ok = test_integration_fast(args.tenant_id)
        
        # Summary
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n{'='*60}")
        logger.info(f"🎉 FAST ONBOARDING COMPLETED")
        logger.info(f"{'='*60}")
        logger.info(f"Tenant ID: {args.tenant_id}")
        logger.info(f"Tenant Name: {args.tenant_name}")
        logger.info(f"Subdomain: {args.subdomain}")
        logger.info(f"Duration: {duration}")
        logger.info(f"Pods Ready: {'✅' if pods_ready else '⚠️'}")
        logger.info(f"Integration: {'✅' if integration_ok else '⚠️'}")
        
        if pods_ready:
            logger.info(f"✅ Tenant {args.tenant_id} is ready for use!")
            return 0
        else:
            logger.warning(f"⚠️ Tenant {args.tenant_id} deployed but may need more time to be fully ready")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Fast onboarding failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
