#!/usr/bin/env python3
"""
Comprehensive Test Suite for Tenant Management System
Tests both onboarding and offboarding scripts with verification
"""

import subprocess
import sys
import time
import json
from datetime import datetime

def run_command(command, timeout=60):
    """Run a command with timeout and return result."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_test_header(test_name):
    """Print a formatted test header."""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def test_script_imports():
    """Test that scripts can be imported without hanging."""
    print_test_header("Testing Script Import Capabilities")
    
    # Test onboarding script import
    success, stdout, stderr = run_command("python3 -c 'import sys; sys.path.append(\".\"); exec(open(\"advanced_tenant_onboard.py\").read()[:1000])'", 10)
    print_result("Onboarding Script Import", success, stderr if not success else "No hanging imports")
    
    # Test offboarding script import
    success, stdout, stderr = run_command("python3 -c 'import sys; sys.path.append(\".\"); exec(open(\"advanced_tenant_offboard.py\").read()[:1000])'", 10)
    print_result("Offboarding Script Import", success, stderr if not success else "No hanging imports")
    
    return True

def test_onboarding_help():
    """Test onboarding script help functionality."""
    print_test_header("Testing Onboarding Script Help")
    
    success, stdout, stderr = run_command("python3 advanced_tenant_onboard.py --help", 15)
    help_working = success and "tenant-id" in stdout and "tenant-name" in stdout
    print_result("Onboarding Help Command", help_working, "Help text displayed correctly" if help_working else "Help command failed")
    
    return help_working

def test_offboarding_help():
    """Test offboarding script help functionality."""
    print_test_header("Testing Offboarding Script Help")
    
    success, stdout, stderr = run_command("python3 advanced_tenant_offboard.py --help", 15)
    help_working = success and "tenant-id" in stdout
    print_result("Offboarding Help Command", help_working, "Help text displayed correctly" if help_working else "Help command failed")
    
    return help_working

def test_quick_onboarding():
    """Test quick onboarding with minimal setup."""
    print_test_header("Testing Quick Tenant Onboarding")
    
    tenant_id = f"test-{int(time.time())}"
    command = f"""python3 advanced_tenant_onboard.py \
  --tenant-id "{tenant_id}" \
  --tenant-name "Test Tenant" \
  --subdomain "{tenant_id}" \
  --domain "architrave.local" \
  --backend-image "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test" \
  --frontend-image "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl" \
  --rabbitmq-image "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02" \
  --skip-db-import \
  --skip-s3-setup"""
    
    print(f"Onboarding tenant: {tenant_id}")
    success, stdout, stderr = run_command(command, 180)
    
    if success:
        # Verify namespace was created
        ns_success, ns_stdout, ns_stderr = run_command(f"kubectl get namespace tenant-{tenant_id}", 10)
        print_result("Quick Onboarding", ns_success, f"Tenant {tenant_id} namespace created" if ns_success else "Namespace not found")
        return tenant_id if ns_success else None
    else:
        print_result("Quick Onboarding", False, stderr)
        return None

def test_quick_offboarding(tenant_id):
    """Test quick offboarding."""
    if not tenant_id:
        print_result("Quick Offboarding", False, "No tenant to offboard")
        return False
    
    print_test_header("Testing Quick Tenant Offboarding")
    
    command = f"python3 advanced_tenant_offboard.py --tenant-id {tenant_id} --verify"
    
    print(f"Offboarding tenant: {tenant_id}")
    success, stdout, stderr = run_command(command, 120)
    
    if success:
        # Verify namespace was deleted
        time.sleep(5)  # Wait for cleanup
        ns_success, ns_stdout, ns_stderr = run_command(f"kubectl get namespace tenant-{tenant_id}", 10)
        cleanup_success = not ns_success  # Should fail because namespace is deleted
        print_result("Quick Offboarding", cleanup_success, f"Tenant {tenant_id} cleaned up" if cleanup_success else "Cleanup incomplete")
        return cleanup_success
    else:
        print_result("Quick Offboarding", False, stderr)
        return False

def test_script_validation():
    """Test script input validation."""
    print_test_header("Testing Script Input Validation")
    
    # Test onboarding with missing required args
    success, stdout, stderr = run_command("python3 advanced_tenant_onboard.py --tenant-id test", 10)
    validation_working = not success  # Should fail due to missing args
    print_result("Onboarding Validation", validation_working, "Correctly rejects missing arguments" if validation_working else "Validation not working")
    
    # Test offboarding with missing required args
    success, stdout, stderr = run_command("python3 advanced_tenant_offboard.py", 10)
    validation_working = not success  # Should fail due to missing args
    print_result("Offboarding Validation", validation_working, "Correctly rejects missing arguments" if validation_working else "Validation not working")
    
    return True

def main():
    """Run comprehensive test suite."""
    print("🧪 COMPREHENSIVE TENANT MANAGEMENT SYSTEM TEST SUITE")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")
    print()
    
    test_results = []
    
    # Test 1: Script imports
    test_results.append(("Script Imports", test_script_imports()))
    
    # Test 2: Help functionality
    test_results.append(("Onboarding Help", test_onboarding_help()))
    test_results.append(("Offboarding Help", test_offboarding_help()))
    
    # Test 3: Input validation
    test_results.append(("Input Validation", test_script_validation()))
    
    # Test 4: Quick onboarding/offboarding cycle
    tenant_id = test_quick_onboarding()
    test_results.append(("Quick Onboarding", tenant_id is not None))
    
    if tenant_id:
        offboard_success = test_quick_offboarding(tenant_id)
        test_results.append(("Quick Offboarding", offboard_success))
    
    # Print final results
    print_test_header("FINAL TEST RESULTS")
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if success:
            passed += 1
    
    print(f"\nTest Summary: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - SYSTEM IS WORKING PERFECTLY!")
        return 0
    else:
        print("⚠️ SOME TESTS FAILED - CHECK SYSTEM CONFIGURATION")
        return 1

if __name__ == "__main__":
    sys.exit(main())
