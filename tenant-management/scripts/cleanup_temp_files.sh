#!/bin/bash

echo "🧹 COMPREHENSIVE CLEANUP SCAN"
echo "============================="
echo "Scanning for temporary files, unnecessary scripts, and outdated code..."
echo ""

BASE_DIR="/Users/<USER>/Projects/new_project/infra-provisioning"

# Function to safely remove files
safe_remove() {
    local file="$1"
    local reason="$2"
    echo "🗑️  FOUND: $file"
    echo "   Reason: $reason"
    echo "   Size: $(du -h "$file" 2>/dev/null | cut -f1)"
    echo "   Would you like to remove this? (y/N)"
    # For automation, we'll just list them
    echo "   [LISTED FOR REVIEW]"
    echo ""
}

# Function to scan directory
scan_directory() {
    local dir="$1"
    local description="$2"
    echo "📁 Scanning: $description ($dir)"
    echo "----------------------------------------"
    
    if [ ! -d "$dir" ]; then
        echo "   Directory not found, skipping..."
        echo ""
        return
    fi
    
    local count=0
    
    # Find temporary files
    find "$dir" -name "*.tmp" -o -name "*.temp" -o -name "*.bak" -o -name "*.backup" -o -name "*~" 2>/dev/null | while read file; do
        safe_remove "$file" "Temporary/backup file"
        count=$((count + 1))
    done
    
    # Find log files
    find "$dir" -name "*.log" -size +10M 2>/dev/null | while read file; do
        safe_remove "$file" "Large log file (>10MB)"
        count=$((count + 1))
    done
    
    # Find duplicate scripts
    find "$dir" -name "*_old*" -o -name "*_backup*" -o -name "*_copy*" -o -name "*_test*" 2>/dev/null | while read file; do
        safe_remove "$file" "Duplicate/old version file"
        count=$((count + 1))
    done
    
    # Find empty directories
    find "$dir" -type d -empty 2>/dev/null | while read dir; do
        safe_remove "$dir" "Empty directory"
        count=$((count + 1))
    done
    
    echo "   Found items in this directory: $count"
    echo ""
}

echo "🔍 STEP 1: Scanning Main Directories"
echo "===================================="

# Scan main directories
scan_directory "$BASE_DIR" "Main infra-provisioning directory"
scan_directory "$BASE_DIR/tenant-management" "Tenant management directory"
scan_directory "$BASE_DIR/tenant-management/scripts" "Scripts directory"
scan_directory "$BASE_DIR/security" "Security directory"

echo ""
echo "🔍 STEP 2: Scanning for Specific File Types"
echo "==========================================="

echo "📄 Looking for unnecessary README files:"
find "$BASE_DIR" -name "README*" -o -name "readme*" 2>/dev/null | while read file; do
    if [ -f "$file" ]; then
        size=$(wc -l < "$file" 2>/dev/null)
        if [ "$size" -lt 5 ]; then
            safe_remove "$file" "Short/empty README file ($size lines)"
        fi
    fi
done

echo ""
echo "📄 Looking for duplicate configuration files:"
find "$BASE_DIR" -name "*.yaml.bak" -o -name "*.yml.bak" -o -name "*.json.bak" 2>/dev/null | while read file; do
    safe_remove "$file" "Backup configuration file"
done

echo ""
echo "📄 Looking for temporary Kubernetes files:"
find "$BASE_DIR" -name "tmp*.yaml" -o -name "temp*.yaml" -o -name "*-temp.yaml" 2>/dev/null | while read file; do
    safe_remove "$file" "Temporary Kubernetes manifest"
done

echo ""
echo "📄 Looking for old Python cache files:"
find "$BASE_DIR" -name "__pycache__" -type d 2>/dev/null | while read dir; do
    safe_remove "$dir" "Python cache directory"
done

find "$BASE_DIR" -name "*.pyc" -o -name "*.pyo" 2>/dev/null | while read file; do
    safe_remove "$file" "Python compiled file"
done

echo ""
echo "🔍 STEP 3: Scanning for Outdated Scripts"
echo "========================================"

echo "📜 Looking for scripts that might be outdated:"

# Check for scripts with old naming patterns
find "$BASE_DIR" -name "*_v1*" -o -name "*_old*" -o -name "*_deprecated*" 2>/dev/null | while read file; do
    safe_remove "$file" "Outdated version script"
done

# Check for test scripts that might not be needed
find "$BASE_DIR" -name "test_*" -o -name "*_test.py" -o -name "*_test.sh" 2>/dev/null | while read file; do
    if [ -f "$file" ]; then
        echo "🧪 TEST SCRIPT: $file"
        echo "   Size: $(du -h "$file" 2>/dev/null | cut -f1)"
        echo "   Last modified: $(stat -f "%Sm" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null)"
        echo "   [REVIEW NEEDED - May be important for testing]"
        echo ""
    fi
done

echo ""
echo "🔍 STEP 4: Scanning for Large Files"
echo "==================================="

echo "📦 Looking for files larger than 50MB:"
find "$BASE_DIR" -type f -size +50M 2>/dev/null | while read file; do
    echo "📦 LARGE FILE: $file"
    echo "   Size: $(du -h "$file" 2>/dev/null | cut -f1)"
    echo "   Type: $(file "$file" 2>/dev/null | cut -d: -f2)"
    echo "   [REVIEW NEEDED - May contain important data]"
    echo ""
done

echo ""
echo "🔍 STEP 5: Scanning for Duplicate Files"
echo "======================================="

echo "🔄 Looking for potential duplicate files:"

# Find files with similar names
find "$BASE_DIR" -name "*.yaml" 2>/dev/null | sort | while read file; do
    basename_file=$(basename "$file" .yaml)
    dirname_file=$(dirname "$file")
    
    # Look for similar files
    find "$dirname_file" -name "${basename_file}*.yaml" -o -name "${basename_file}_*.yaml" 2>/dev/null | grep -v "^$file$" | while read similar; do
        echo "🔄 POTENTIAL DUPLICATE:"
        echo "   Original: $file"
        echo "   Similar:  $similar"
        echo "   Size diff: $(du -h "$file" "$similar" 2>/dev/null | awk '{print $1}' | tr '\n' ' ')"
        echo ""
    done
done

echo ""
echo "🔍 STEP 6: Security Files Scan"
echo "=============================="

# Check security directory specifically
if [ -d "$BASE_DIR/security" ]; then
    echo "🔒 Scanning security directory for cleanup:"
    
    find "$BASE_DIR/security" -name "*.yaml" 2>/dev/null | while read file; do
        # Check if file is actually used
        filename=$(basename "$file")
        if ! grep -r "$filename" "$BASE_DIR/tenant-management/scripts/" >/dev/null 2>&1; then
            echo "🔒 UNUSED SECURITY FILE: $file"
            echo "   Not referenced in any scripts"
            echo "   Size: $(du -h "$file" 2>/dev/null | cut -f1)"
            echo ""
        fi
    done
fi

echo ""
echo "🔍 STEP 7: Summary and Recommendations"
echo "====================================="

# Count total files found
TOTAL_TEMP=$(find "$BASE_DIR" -name "*.tmp" -o -name "*.temp" -o -name "*.bak" -o -name "*.backup" -o -name "*~" 2>/dev/null | wc -l)
TOTAL_LOGS=$(find "$BASE_DIR" -name "*.log" -size +10M 2>/dev/null | wc -l)
TOTAL_DUPLICATES=$(find "$BASE_DIR" -name "*_old*" -o -name "*_backup*" -o -name "*_copy*" 2>/dev/null | wc -l)
TOTAL_CACHE=$(find "$BASE_DIR" -name "__pycache__" -type d -o -name "*.pyc" -o -name "*.pyo" 2>/dev/null | wc -l)

echo "📊 CLEANUP SUMMARY:"
echo "==================="
echo "Temporary files found: $TOTAL_TEMP"
echo "Large log files found: $TOTAL_LOGS"
echo "Duplicate/old files found: $TOTAL_DUPLICATES"
echo "Cache files found: $TOTAL_CACHE"

echo ""
echo "💡 RECOMMENDATIONS:"
echo "==================="
echo "1. ✅ Safe to remove: Temporary files, cache files, backup files"
echo "2. ⚠️  Review needed: Test scripts, large files, configuration duplicates"
echo "3. 🔍 Manual check: Security files, scripts with similar names"

echo ""
echo "🚀 AUTOMATED CLEANUP COMMANDS:"
echo "=============================="
echo "# Remove temporary files:"
echo "find '$BASE_DIR' -name '*.tmp' -o -name '*.temp' -o -name '*.bak' -o -name '*.backup' -o -name '*~' -delete"
echo ""
echo "# Remove Python cache:"
echo "find '$BASE_DIR' -name '__pycache__' -type d -exec rm -rf {} + 2>/dev/null"
echo "find '$BASE_DIR' -name '*.pyc' -o -name '*.pyo' -delete"
echo ""
echo "# Remove empty directories:"
echo "find '$BASE_DIR' -type d -empty -delete"

echo ""
echo "🏁 CLEANUP SCAN COMPLETED"
echo "========================="
echo "Review the findings above and run the recommended cleanup commands."
