apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test004-simple-backend
  namespace: tenant-test004
  labels:
    app: tenant-test004-backend
    tenant: test004
    version: simple
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-test004-backend
      version: simple
  template:
    metadata:
      labels:
        app: tenant-test004-backend
        tenant: test004
        version: simple
    spec:
      securityContext:
        runAsUser: 33
        runAsGroup: 33
        fsGroup: 33
        runAsNonRoot: true
      containers:
      - name: backend
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        ports:
        - containerPort: 9000
          name: php-fpm
        - containerPort: 8080
          name: http
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: host
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: port
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: password
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: db-credentials
              key: dbname
        - name: TENANT_ID
          value: "test004"
        command: ["/bin/bash"]
        args:
        - -c
        - |
          echo "Starting simple backend..."
          
          # Create required directories
          mkdir -p /tmp/php-sessions /tmp/nginx-cache
          chmod 777 /tmp/php-sessions /tmp/nginx-cache
          
          # Configure PHP
          echo "session.save_path = \"/tmp/php-sessions\"" > /usr/local/etc/php/conf.d/99-custom.ini
          echo "upload_tmp_dir = \"/tmp\"" >> /usr/local/etc/php/conf.d/99-custom.ini
          
          # Create simple nginx config
          cat > /etc/nginx/conf.d/default.conf << 'EOF'
          server {
              listen 8080;
              server_name localhost;
              
              # Use writable cache directories
              client_body_temp_path /tmp/nginx-cache/client_temp;
              proxy_temp_path /tmp/nginx-cache/proxy_temp;
              
              location /health {
                  access_log off;
                  return 200 "OK";
                  add_header Content-Type text/plain;
              }
              
              location /api/health/extended {
                  access_log off;
                  return 200 '{"status":"healthy","check":{"basic":"y","extended":"y","elastic-search":"n"}}';
                  add_header Content-Type application/json;
              }
              
              location / {
                  return 200 "Backend Working";
                  add_header Content-Type text/plain;
              }
          }
          EOF
          
          # Create cache directories
          mkdir -p /tmp/nginx-cache/client_temp /tmp/nginx-cache/proxy_temp
          
          # Start both PHP-FPM and nginx
          php-fpm --daemonize
          exec nginx -g "daemon off;"
        securityContext:
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-test004-simple-backend
  namespace: tenant-test004
  labels:
    app: tenant-test004-backend
    tenant: test004
spec:
  selector:
    app: tenant-test004-backend
    version: simple
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test004-simple-frontend
  namespace: tenant-test004
  labels:
    app: tenant-test004-frontend
    tenant: test004
    version: simple
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-test004-frontend
      version: simple
  template:
    metadata:
      labels:
        app: tenant-test004-frontend
        tenant: test004
        version: simple
    spec:
      securityContext:
        runAsUser: 101
        runAsGroup: 101
        fsGroup: 101
        runAsNonRoot: true
      containers:
      - name: frontend
        image: nginx:1.25-alpine
        ports:
        - containerPort: 80
          name: http
        command: ["/bin/sh"]
        args:
        - -c
        - |
          # Create nginx configuration
          cat > /etc/nginx/conf.d/default.conf << 'EOF'
          server {
              listen 80;
              server_name _;
              
              # Use writable cache directories
              client_body_temp_path /tmp/nginx-cache/client_temp;
              proxy_temp_path /tmp/nginx-cache/proxy_temp;
              
              location = / {
                  add_header Content-Type text/plain;
                  return 200 "Frontend Working";
              }
              
              location /health {
                  add_header Content-Type text/plain;
                  return 200 "OK";
              }
              
              location /api/health/extended {
                  proxy_pass http://tenant-test004-simple-backend:8080/api/health/extended;
                  proxy_set_header Host $host;
              }
              
              location /api/ {
                  proxy_pass http://tenant-test004-simple-backend:8080/;
                  proxy_set_header Host $host;
                  proxy_set_header X-Real-IP $remote_addr;
              }
              
              location / {
                  proxy_pass http://tenant-test004-simple-backend:8080;
                  proxy_set_header Host $host;
                  proxy_set_header X-Real-IP $remote_addr;
              }
          }
          EOF
          
          # Create cache directories
          mkdir -p /tmp/nginx-cache/client_temp /tmp/nginx-cache/proxy_temp
          
          # Start nginx
          exec nginx -g "daemon off;"
        securityContext:
          runAsUser: 101
          runAsGroup: 101
          runAsNonRoot: true
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-test004-simple-frontend
  namespace: tenant-test004
  labels:
    app: tenant-test004-frontend
    tenant: test004
spec:
  selector:
    app: tenant-test004-frontend
    version: simple
  ports:
  - name: http
    port: 80
    targetPort: 80
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-test004-simple-rabbitmq
  namespace: tenant-test004
  labels:
    app: tenant-test004-rabbitmq
    tenant: test004
    version: simple
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-test004-rabbitmq
      version: simple
  template:
    metadata:
      labels:
        app: tenant-test004-rabbitmq
        tenant: test004
        version: simple
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
        runAsNonRoot: true
      containers:
      - name: rabbitmq
        image: rabbitmq:3.12-management-alpine
        ports:
        - containerPort: 5672
          name: amqp
        - containerPort: 15672
          name: management
        env:
        - name: RABBITMQ_DEFAULT_USER
          value: "admin"
        - name: RABBITMQ_DEFAULT_PASS
          value: "admin123"
        securityContext:
          runAsUser: 999
          runAsGroup: 999
          runAsNonRoot: true
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
        livenessProbe:
          tcpSocket:
            port: 5672
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          tcpSocket:
            port: 5672
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-test004-simple-rabbitmq
  namespace: tenant-test004
  labels:
    app: tenant-test004-rabbitmq
    tenant: test004
spec:
  selector:
    app: tenant-test004-rabbitmq
    version: simple
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  type: ClusterIP
