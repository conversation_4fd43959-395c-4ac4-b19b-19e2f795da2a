#!/usr/bin/env python3
"""
Multi-Cycle Tenant Onboarding/Offboarding Test Suite
This script runs multiple cycles of tenant onboarding and offboarding to test system reliability.
"""

import subprocess
import json
import time
import sys
import logging
import argparse
from typing import Dict, List, Tuple
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiCycleTester:
    """Multi-cycle tester for tenant onboarding/offboarding."""
    
    def __init__(self, cycles: int = 3):
        self.cycles = cycles
        self.results = []
        self.test_tenants = [
            {"id": "test-cycle-1", "name": "Test Cycle 1", "subdomain": "test-cycle-1"},
            {"id": "test-cycle-2", "name": "Test Cycle 2", "subdomain": "test-cycle-2"},
            {"id": "test-cycle-3", "name": "Test Cycle 3", "subdomain": "test-cycle-3"},
        ]
        
    def run_command(self, command: str, check: bool = True) -> Tuple[bool, str]:
        """Execute a shell command and return success status and output."""
        try:
            result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
            return True, result.stdout.strip()
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed: {command}")
            logger.error(f"Error: {e.stderr}")
            return False, e.stderr.strip()
    
    def onboard_tenant(self, tenant_id: str, tenant_name: str, subdomain: str) -> bool:
        """Onboard a single tenant."""
        logger.info(f"🚀 Onboarding tenant: {tenant_id}")
        
        command = (
            f"python3 advanced_tenant_onboard.py "
            f"--tenant-id {tenant_id} "
            f"--tenant-name \"{tenant_name}\" "
            f"--subdomain {subdomain} "
            f"--skip-db-import "
            f"--skip-s3-setup "
            f"--dms "
            f"--external-api "
            f"--heap-tracking "
            f"--debug"
        )
        
        start_time = time.time()
        success, output = self.run_command(command, check=False)
        end_time = time.time()
        
        duration = end_time - start_time
        
        result = {
            'operation': 'onboard',
            'tenant_id': tenant_id,
            'success': success,
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'output_length': len(output)
        }
        
        if success:
            logger.info(f"✅ Tenant {tenant_id} onboarded successfully in {duration:.1f}s")
        else:
            logger.error(f"❌ Tenant {tenant_id} onboarding failed after {duration:.1f}s")
        
        return success
    
    def offboard_tenant(self, tenant_id: str) -> bool:
        """Offboard a single tenant."""
        logger.info(f"🗑️ Offboarding tenant: {tenant_id}")
        
        command = f"python3 advanced_tenant_offboard.py --tenant-id {tenant_id} --force"
        
        start_time = time.time()
        success, output = self.run_command(command, check=False)
        end_time = time.time()
        
        duration = end_time - start_time
        
        result = {
            'operation': 'offboard',
            'tenant_id': tenant_id,
            'success': success,
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'output_length': len(output)
        }
        
        if success:
            logger.info(f"✅ Tenant {tenant_id} offboarded successfully in {duration:.1f}s")
        else:
            logger.error(f"❌ Tenant {tenant_id} offboarding failed after {duration:.1f}s")
        
        return success
    
    def run_integration_tests(self, tenant_id: str) -> Dict:
        """Run comprehensive integration tests for a tenant."""
        logger.info(f"🧪 Running integration tests for tenant: {tenant_id}")
        
        command = f"python3 comprehensive_integration_tests.py {tenant_id}"
        
        start_time = time.time()
        success, output = self.run_command(command, check=False)
        end_time = time.time()
        
        duration = end_time - start_time
        
        # Parse success rate from output if available
        success_rate = 0
        if "Success Rate:" in output:
            try:
                rate_line = [line for line in output.split('\n') if "Success Rate:" in line][0]
                success_rate = float(rate_line.split(':')[1].split('%')[0].strip())
            except:
                success_rate = 0
        
        result = {
            'operation': 'integration_test',
            'tenant_id': tenant_id,
            'success': success and success_rate >= 80,
            'success_rate': success_rate,
            'duration': duration,
            'timestamp': datetime.now().isoformat()
        }
        
        if result['success']:
            logger.info(f"✅ Integration tests passed for {tenant_id} ({success_rate:.1f}% success rate)")
        else:
            logger.error(f"❌ Integration tests failed for {tenant_id} ({success_rate:.1f}% success rate)")
        
        return result
    
    def cleanup_all_tenants(self):
        """Clean up all test tenants before starting."""
        logger.info("🧹 Cleaning up any existing test tenants...")
        
        for tenant in self.test_tenants:
            tenant_id = tenant['id']
            # Check if tenant exists
            success, output = self.run_command(f"kubectl get namespace tenant-{tenant_id}", check=False)
            if success:
                logger.info(f"Found existing tenant {tenant_id}, cleaning up...")
                self.offboard_tenant(tenant_id)
                time.sleep(10)  # Wait for cleanup to complete
    
    def run_single_cycle(self, cycle_num: int) -> Dict:
        """Run a single cycle of onboarding, testing, and offboarding."""
        logger.info(f"🔄 Starting cycle {cycle_num}/{self.cycles}")
        
        cycle_results = {
            'cycle': cycle_num,
            'start_time': datetime.now().isoformat(),
            'operations': [],
            'success': True
        }
        
        # Onboard all tenants
        for tenant in self.test_tenants:
            tenant_id = tenant['id']
            tenant_name = tenant['name']
            subdomain = tenant['subdomain']
            
            onboard_success = self.onboard_tenant(tenant_id, tenant_name, subdomain)
            cycle_results['operations'].append({
                'operation': 'onboard',
                'tenant_id': tenant_id,
                'success': onboard_success
            })
            
            if not onboard_success:
                cycle_results['success'] = False
            
            # Wait a bit between onboardings
            time.sleep(30)
        
        # Wait for all deployments to stabilize
        logger.info("⏳ Waiting for deployments to stabilize...")
        time.sleep(60)
        
        # Run integration tests for all tenants
        for tenant in self.test_tenants:
            tenant_id = tenant['id']
            
            test_result = self.run_integration_tests(tenant_id)
            cycle_results['operations'].append(test_result)
            
            if not test_result['success']:
                cycle_results['success'] = False
        
        # Offboard all tenants
        for tenant in self.test_tenants:
            tenant_id = tenant['id']
            
            offboard_success = self.offboard_tenant(tenant_id)
            cycle_results['operations'].append({
                'operation': 'offboard',
                'tenant_id': tenant_id,
                'success': offboard_success
            })
            
            if not offboard_success:
                cycle_results['success'] = False
            
            # Wait a bit between offboardings
            time.sleep(15)
        
        cycle_results['end_time'] = datetime.now().isoformat()
        
        if cycle_results['success']:
            logger.info(f"✅ Cycle {cycle_num} completed successfully")
        else:
            logger.error(f"❌ Cycle {cycle_num} completed with failures")
        
        return cycle_results
    
    def run_multi_cycle_test(self) -> Dict:
        """Run multiple cycles of testing."""
        logger.info(f"🎯 Starting multi-cycle test with {self.cycles} cycles")
        
        # Cleanup before starting
        self.cleanup_all_tenants()
        
        test_results = {
            'total_cycles': self.cycles,
            'start_time': datetime.now().isoformat(),
            'cycles': [],
            'overall_success': True
        }
        
        for cycle_num in range(1, self.cycles + 1):
            cycle_result = self.run_single_cycle(cycle_num)
            test_results['cycles'].append(cycle_result)
            
            if not cycle_result['success']:
                test_results['overall_success'] = False
            
            # Wait between cycles
            if cycle_num < self.cycles:
                logger.info("⏳ Waiting between cycles...")
                time.sleep(30)
        
        test_results['end_time'] = datetime.now().isoformat()
        
        return test_results
    
    def print_summary(self, results: Dict):
        """Print a comprehensive summary of test results."""
        print("\n" + "="*80)
        print("🏁 MULTI-CYCLE TEST SUMMARY")
        print("="*80)
        
        print(f"Total Cycles: {results['total_cycles']}")
        print(f"Overall Success: {'✅ PASSED' if results['overall_success'] else '❌ FAILED'}")
        
        # Count operations
        total_operations = 0
        successful_operations = 0
        
        for cycle in results['cycles']:
            for operation in cycle['operations']:
                total_operations += 1
                if operation['success']:
                    successful_operations += 1
        
        success_rate = (successful_operations / total_operations) * 100 if total_operations > 0 else 0
        print(f"Operation Success Rate: {success_rate:.1f}% ({successful_operations}/{total_operations})")
        
        print(f"\nCycle Results:")
        for cycle in results['cycles']:
            status = "✅ PASSED" if cycle['success'] else "❌ FAILED"
            print(f"  Cycle {cycle['cycle']}: {status}")
        
        # Print operation breakdown
        operation_stats = {}
        for cycle in results['cycles']:
            for operation in cycle['operations']:
                op_type = operation['operation']
                if op_type not in operation_stats:
                    operation_stats[op_type] = {'total': 0, 'success': 0}
                operation_stats[op_type]['total'] += 1
                if operation['success']:
                    operation_stats[op_type]['success'] += 1
        
        print(f"\nOperation Breakdown:")
        for op_type, stats in operation_stats.items():
            rate = (stats['success'] / stats['total']) * 100 if stats['total'] > 0 else 0
            print(f"  {op_type}: {rate:.1f}% ({stats['success']}/{stats['total']})")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Multi-cycle tenant onboarding/offboarding test')
    parser.add_argument('--cycles', type=int, default=3, help='Number of cycles to run (default: 3)')
    
    args = parser.parse_args()
    
    tester = MultiCycleTester(cycles=args.cycles)
    
    print(f"🚀 Starting multi-cycle test with {args.cycles} cycles")
    print("This will test the reliability of tenant onboarding/offboarding processes")
    print("="*80)
    
    results = tester.run_multi_cycle_test()
    tester.print_summary(results)
    
    # Save results to file
    with open(f'multi_cycle_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    return 0 if results['overall_success'] else 1

if __name__ == "__main__":
    sys.exit(main())
