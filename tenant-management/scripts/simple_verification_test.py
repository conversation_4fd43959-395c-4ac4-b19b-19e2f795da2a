#!/usr/bin/env python3
"""
Simple Verification Test for Tenant Management System
Quick test to verify system status and functionality
"""

import subprocess
import sys
import os

def run_quick_command(command, timeout=10):
    """Run a command quickly with short timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Timeout"
    except Exception as e:
        return False, "", str(e)

def test_script_help():
    """Test that scripts can show help without hanging."""
    print("🧪 Testing Script Help Commands...")
    
    # Test onboarding script help
    success, stdout, stderr = run_quick_command("python3 advanced_tenant_onboard.py --help")
    onboard_help = success and "tenant-id" in stdout
    print(f"  Onboarding Help: {'✅ PASS' if onboard_help else '❌ FAIL'}")
    
    # Test offboarding script help
    success, stdout, stderr = run_quick_command("python3 advanced_tenant_offboard.py --help")
    offboard_help = success and "tenant-id" in stdout
    print(f"  Offboarding Help: {'✅ PASS' if offboard_help else '❌ FAIL'}")
    
    return onboard_help and offboard_help

def test_script_imports():
    """Test that scripts can be imported without hanging."""
    print("🧪 Testing Script Import Speed...")
    
    # Test onboarding script import speed
    success, stdout, stderr = run_quick_command("python3 -c 'exec(open(\"advanced_tenant_onboard.py\").read()[:500])'")
    onboard_import = success
    print(f"  Onboarding Import: {'✅ PASS' if onboard_import else '❌ FAIL'}")
    
    # Test offboarding script import speed
    success, stdout, stderr = run_quick_command("python3 -c 'exec(open(\"advanced_tenant_offboard.py\").read()[:500])'")
    offboard_import = success
    print(f"  Offboarding Import: {'✅ PASS' if offboard_import else '❌ FAIL'}")
    
    return onboard_import and offboard_import

def test_file_existence():
    """Test that all required files exist."""
    print("🧪 Testing File Existence...")
    
    required_files = [
        "advanced_tenant_onboard.py",
        "advanced_tenant_offboard.py",
        "fixed_advanced_onboard.py",
        "comprehensive_test.py"
    ]
    
    all_exist = True
    for file in required_files:
        exists = os.path.exists(file)
        print(f"  {file}: {'✅ EXISTS' if exists else '❌ MISSING'}")
        if not exists:
            all_exist = False
    
    return all_exist

def test_tenant_namespaces():
    """Test current tenant namespace status."""
    print("🧪 Testing Tenant Namespace Status...")
    
    success, stdout, stderr = run_quick_command("kubectl get namespaces")
    if success:
        tenant_namespaces = [line for line in stdout.split('\n') if 'tenant-' in line]
        print(f"  Found {len(tenant_namespaces)} tenant namespaces:")
        for ns in tenant_namespaces[:5]:  # Show first 5
            ns_name = ns.split()[0] if ns.split() else ns
            print(f"    - {ns_name}")
        return True
    else:
        print(f"  ❌ FAIL: Could not get namespaces - {stderr}")
        return False

def main():
    """Run simple verification tests."""
    print("🧪 SIMPLE VERIFICATION TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("File Existence", test_file_existence),
        ("Script Imports", test_script_imports),
        ("Script Help", test_script_help),
        ("Tenant Namespaces", test_tenant_namespaces)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}: PASS")
                passed += 1
            else:
                print(f"❌ {test_name}: FAIL")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL VERIFICATION TESTS PASSED!")
        print("✅ Tenant Management System is working correctly!")
    elif passed >= total * 0.75:
        print("⚠️ Most tests passed - System is mostly functional")
    else:
        print("❌ Multiple test failures - System needs attention")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
