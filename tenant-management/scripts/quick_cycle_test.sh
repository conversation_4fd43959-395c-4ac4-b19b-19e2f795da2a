#!/bin/bash

# Quick Cycle Test Script
# Tests 3 cycles of onboarding and offboarding

set -e

echo "🚀 Starting 3 Cycles of Tenant Onboarding/Offboarding"
echo "=================================================="

# Function to create a basic tenant
create_tenant() {
    local tenant_id=$1
    local tenant_name=$2
    
    echo "📋 Creating tenant: $tenant_id"
    
    # Create namespace
    kubectl create namespace "tenant-$tenant_id" --dry-run=client -o yaml | kubectl apply -f -
    kubectl label namespace "tenant-$tenant_id" "tenant.architrave.io/tenant-id=$tenant_id"
    kubectl label namespace "tenant-$tenant_id" "security.architrave.io/enabled=true"
    
    # Create a simple deployment
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-$tenant_id-demo
  namespace: tenant-$tenant_id
  labels:
    tenant.architrave.io/tenant-id: $tenant_id
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo
      tenant: $tenant_id
  template:
    metadata:
      labels:
        app: demo
        tenant: $tenant_id
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 33
        fsGroup: 33
      containers:
      - name: demo
        image: nginx:alpine
        ports:
        - containerPort: 80
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache/nginx
        - name: var-run
          mountPath: /var/run
      volumes:
      - name: tmp
        emptyDir: {}
      - name: var-cache
        emptyDir: {}
      - name: var-run
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-$tenant_id-demo-service
  namespace: tenant-$tenant_id
spec:
  selector:
    app: demo
    tenant: $tenant_id
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF
    
    # Wait for deployment
    echo "⏳ Waiting for deployment to be ready..."
    kubectl wait --for=condition=available deployment/tenant-$tenant_id-demo -n tenant-$tenant_id --timeout=60s
    
    echo "✅ Tenant $tenant_id created successfully"
}

# Function to verify tenant
verify_tenant() {
    local tenant_id=$1
    
    echo "🔍 Verifying tenant: $tenant_id"
    
    # Check namespace
    if kubectl get namespace "tenant-$tenant_id" >/dev/null 2>&1; then
        echo "✅ Namespace exists"
    else
        echo "❌ Namespace missing"
        return 1
    fi
    
    # Check deployment
    if kubectl get deployment "tenant-$tenant_id-demo" -n "tenant-$tenant_id" >/dev/null 2>&1; then
        echo "✅ Deployment exists"
    else
        echo "❌ Deployment missing"
        return 1
    fi
    
    # Check pods
    local pod_count=$(kubectl get pods -n "tenant-$tenant_id" --no-headers | wc -l)
    echo "✅ Found $pod_count pods"
    
    # Check security context
    local security_context=$(kubectl get pods -n "tenant-$tenant_id" -o jsonpath='{.items[0].spec.securityContext}')
    if [[ "$security_context" == *"runAsUser"* ]]; then
        echo "✅ Security context applied"
    else
        echo "⚠️ Security context not found"
    fi
    
    echo "✅ Tenant $tenant_id verification completed"
}

# Function to delete tenant
delete_tenant() {
    local tenant_id=$1
    
    echo "🗑️ Deleting tenant: $tenant_id"
    
    # Delete namespace (this will delete everything in it)
    kubectl delete namespace "tenant-$tenant_id" --ignore-not-found=true
    
    # Wait for namespace to be deleted
    echo "⏳ Waiting for namespace deletion..."
    while kubectl get namespace "tenant-$tenant_id" >/dev/null 2>&1; do
        sleep 2
    done
    
    echo "✅ Tenant $tenant_id deleted successfully"
}

# Main execution
main() {
    echo "🧹 Ensuring clean slate..."
    kubectl get namespaces | grep tenant- | awk '{print $1}' | xargs -I {} kubectl delete namespace {} --ignore-not-found=true || true
    
    # Wait for any existing tenant namespaces to be deleted
    sleep 5
    
    # Cycle 1
    echo ""
    echo "🔄 CYCLE 1: Testing tenant cycle-001"
    echo "=================================="
    create_tenant "cycle-001" "Cycle Test 001"
    verify_tenant "cycle-001"
    sleep 2
    delete_tenant "cycle-001"
    
    # Cycle 2
    echo ""
    echo "🔄 CYCLE 2: Testing tenant cycle-002"
    echo "=================================="
    create_tenant "cycle-002" "Cycle Test 002"
    verify_tenant "cycle-002"
    sleep 2
    delete_tenant "cycle-002"
    
    # Cycle 3
    echo ""
    echo "🔄 CYCLE 3: Testing tenant cycle-003"
    echo "=================================="
    create_tenant "cycle-003" "Cycle Test 003"
    verify_tenant "cycle-003"
    sleep 2
    delete_tenant "cycle-003"
    
    echo ""
    echo "🎉 ALL 3 CYCLES COMPLETED SUCCESSFULLY!"
    echo "======================================"
    echo "✅ Cycle 1: cycle-001 - PASSED"
    echo "✅ Cycle 2: cycle-002 - PASSED"
    echo "✅ Cycle 3: cycle-003 - PASSED"
    echo ""
    echo "🧹 Final verification - no tenant namespaces should exist:"
    kubectl get namespaces | grep tenant- || echo "✅ Clean slate confirmed - no tenant namespaces found"
}

# Run main function
main
