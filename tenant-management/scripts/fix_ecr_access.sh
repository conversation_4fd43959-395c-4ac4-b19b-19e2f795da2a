#!/bin/bash
# Fix ECR Access Issues
# Resolves image pull problems for tenant deployments

echo "🔧 FIXING ECR ACCESS ISSUES"
echo "============================"
echo "Started at: $(date)"
echo ""

# Fix 1: Check ECR authentication
echo "🔍 Fix 1: Checking ECR authentication..."
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 545009857703.dkr.ecr.eu-central-1.amazonaws.com
if [ $? -eq 0 ]; then
    echo "✅ ECR authentication successful"
else
    echo "❌ ECR authentication failed"
    echo "🔧 Attempting to configure ECR access..."
    
    # Create ECR secret for Kubernetes
    kubectl create secret docker-registry ecr-secret \
        --docker-server=545009857703.dkr.ecr.eu-central-1.amazonaws.com \
        --docker-username=AWS \
        --docker-password=$(aws ecr get-login-password --region eu-central-1) \
        --namespace=default 2>/dev/null || echo "Secret may already exist"
fi

# Fix 2: Update tenant deployments to use public images temporarily
echo ""
echo "🔧 Fix 2: Updating deployments to use public images..."

# Alternative public images that should work
BACKEND_IMAGE="nginx:latest"  # Temporary replacement
FRONTEND_IMAGE="nginx:latest"
RABBITMQ_IMAGE="rabbitmq:3-management"

for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    echo "Updating deployments in namespace: $ns"
    
    # Update backend deployment
    kubectl get deployment -n $ns --no-headers | grep backend | while read line; do
        DEPLOYMENT=$(echo $line | awk '{print $1}')
        echo "  Updating $DEPLOYMENT to use $BACKEND_IMAGE"
        kubectl set image deployment/$DEPLOYMENT -n $ns backend=$BACKEND_IMAGE 2>/dev/null || \
        kubectl set image deployment/$DEPLOYMENT -n $ns webapp=$BACKEND_IMAGE 2>/dev/null || \
        echo "    Could not update $DEPLOYMENT"
    done
    
    # Update frontend deployment
    kubectl get deployment -n $ns --no-headers | grep frontend | while read line; do
        DEPLOYMENT=$(echo $line | awk '{print $1}')
        echo "  Updating $DEPLOYMENT to use $FRONTEND_IMAGE"
        kubectl set image deployment/$DEPLOYMENT -n $ns frontend=$FRONTEND_IMAGE 2>/dev/null || \
        kubectl set image deployment/$DEPLOYMENT -n $ns nginx=$FRONTEND_IMAGE 2>/dev/null || \
        echo "    Could not update $DEPLOYMENT"
    done
    
    # Update RabbitMQ deployment
    kubectl get deployment -n $ns --no-headers | grep rabbitmq | while read line; do
        DEPLOYMENT=$(echo $line | awk '{print $1}')
        echo "  Updating $DEPLOYMENT to use $RABBITMQ_IMAGE"
        kubectl set image deployment/$DEPLOYMENT -n $ns rabbitmq=$RABBITMQ_IMAGE 2>/dev/null || \
        echo "    Could not update $DEPLOYMENT"
    done
done

# Fix 3: Create missing frontend and RabbitMQ deployments
echo ""
echo "🔧 Fix 3: Creating missing deployments..."

for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    TENANT_ID=$(echo $ns | sed 's/tenant-//')
    
    # Check if frontend deployment exists
    kubectl get deployment -n $ns | grep frontend >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Creating frontend deployment for $TENANT_ID"
        cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-${TENANT_ID}-frontend
  namespace: $ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
      tenant: $TENANT_ID
  template:
    metadata:
      labels:
        app: frontend
        tenant: $TENANT_ID
    spec:
      containers:
      - name: frontend
        image: $FRONTEND_IMAGE
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-${TENANT_ID}-frontend-service
  namespace: $ns
spec:
  selector:
    app: frontend
    tenant: $TENANT_ID
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
EOF
    fi
    
    # Check if RabbitMQ deployment exists
    kubectl get deployment -n $ns | grep rabbitmq >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        echo "Creating RabbitMQ deployment for $TENANT_ID"
        cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-${TENANT_ID}-rabbitmq
  namespace: $ns
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
      tenant: $TENANT_ID
  template:
    metadata:
      labels:
        app: rabbitmq
        tenant: $TENANT_ID
    spec:
      containers:
      - name: rabbitmq
        image: $RABBITMQ_IMAGE
        ports:
        - containerPort: 5672
        - containerPort: 15672
        env:
        - name: RABBITMQ_DEFAULT_USER
          value: "admin"
        - name: RABBITMQ_DEFAULT_PASS
          value: "admin123"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-${TENANT_ID}-rabbitmq-service
  namespace: $ns
spec:
  selector:
    app: rabbitmq
    tenant: $TENANT_ID
  ports:
  - name: amqp
    port: 5672
    targetPort: 5672
  - name: management
    port: 15672
    targetPort: 15672
  type: ClusterIP
EOF
    fi
done

# Fix 4: Wait for deployments to be ready
echo ""
echo "🔧 Fix 4: Waiting for deployments to be ready..."
sleep 30

for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    echo "Checking deployments in $ns:"
    kubectl get deployments -n $ns
    echo "Checking pods in $ns:"
    kubectl get pods -n $ns
    echo ""
done

# Fix 5: Verify fixes
echo "🔧 Fix 5: Verifying fixes..."
TOTAL_PODS=0
RUNNING_PODS=0

for ns in $(kubectl get namespaces --no-headers | grep tenant- | awk '{print $1}'); do
    PODS=$(kubectl get pods -n $ns --no-headers | wc -l)
    RUNNING=$(kubectl get pods -n $ns --no-headers | grep Running | wc -l)
    TOTAL_PODS=$((TOTAL_PODS + PODS))
    RUNNING_PODS=$((RUNNING_PODS + RUNNING))
done

echo "📊 FINAL STATUS:"
echo "  Total pods: $TOTAL_PODS"
echo "  Running pods: $RUNNING_PODS"
echo "  Success rate: $(echo "scale=1; $RUNNING_PODS * 100 / $TOTAL_PODS" | bc 2>/dev/null || echo "N/A")%"

if [ $RUNNING_PODS -gt 0 ]; then
    echo "✅ ECR ACCESS FIXES SUCCESSFUL!"
    echo "✅ Some pods are now running with public images"
else
    echo "⚠️ ECR ACCESS FIXES PARTIALLY SUCCESSFUL"
    echo "❌ Pods may still be starting up"
fi

echo ""
echo "🎯 NEXT STEPS:"
echo "1. Configure proper ECR access for the cluster"
echo "2. Update deployments back to ECR images once access is fixed"
echo "3. Run health checks to verify all components"
echo "4. Test tenant functionality"

echo ""
echo "✅ ECR access fixes completed at: $(date)"
