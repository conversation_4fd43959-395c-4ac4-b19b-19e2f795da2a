---
# Hetzner DNS Integration for Multi-Tenant Architecture
apiVersion: v1
kind: Namespace
metadata:
  name: hetzner-dns-system
  labels:
    name: hetzner-dns-system
---
apiVersion: v1
kind: Secret
metadata:
  name: hetzner-dns-credentials
  namespace: hetzner-dns-system
type: Opaque
stringData:
  api-token: "YOUR_HETZNER_DNS_API_TOKEN"  # Replace with actual token
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: hetzner-dns-config
  namespace: hetzner-dns-system
data:
  config.yaml: |
    hetzner:
      api_token_secret: hetzner-dns-credentials
      api_token_key: api-token
      zone_name: "architrave.com"
      ttl: 300
    
    tenants:
      default_subdomain_pattern: "{tenant-id}.architrave.com"
      wildcard_support: true
      ssl_certificates: true
    
    monitoring:
      enabled: true
      metrics_port: 8080
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
  labels:
    app: hetzner-dns-controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hetzner-dns-controller
  template:
    metadata:
      labels:
        app: hetzner-dns-controller
    spec:
      serviceAccountName: hetzner-dns-controller
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: controller
        image: hetznercloud/hetzner-dns-controller:latest
        imagePullPolicy: Always
        env:
        - name: HETZNER_DNS_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: hetzner-dns-credentials
              key: api-token
        - name: ZONE_NAME
          value: "architrave.com"
        - name: LOG_LEVEL
          value: "info"
        ports:
        - containerPort: 8080
          name: metrics
        - containerPort: 8081
          name: health
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        volumeMounts:
        - name: config
          mountPath: /etc/hetzner-dns
          readOnly: true
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: config
        configMap:
          name: hetzner-dns-config
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: hetzner-dns-controller
rules:
- apiGroups: [""]
  resources: ["services", "endpoints", "namespaces"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: hetzner-dns-controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: hetzner-dns-controller
subjects:
- kind: ServiceAccount
  name: hetzner-dns-controller
  namespace: hetzner-dns-system
---
apiVersion: v1
kind: Service
metadata:
  name: hetzner-dns-controller-metrics
  namespace: hetzner-dns-system
  labels:
    app: hetzner-dns-controller
spec:
  ports:
  - name: metrics
    port: 8080
    targetPort: 8080
  selector:
    app: hetzner-dns-controller
---
# Custom Resource Definition for Tenant DNS
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: tenantdns.architrave.io
spec:
  group: architrave.io
  versions:
  - name: v1
    served: true
    storage: true
    schema:
      openAPIV3Schema:
        type: object
        properties:
          spec:
            type: object
            properties:
              tenantId:
                type: string
                pattern: '^[a-z0-9-]+$'
              subdomain:
                type: string
                pattern: '^[a-z0-9-]+$'
              domain:
                type: string
                default: "architrave.com"
              loadBalancerIP:
                type: string
              sslEnabled:
                type: boolean
                default: true
              wildcardEnabled:
                type: boolean
                default: true
            required:
            - tenantId
            - subdomain
          status:
            type: object
            properties:
              phase:
                type: string
                enum: ["Pending", "Active", "Failed"]
              dnsRecords:
                type: array
                items:
                  type: object
                  properties:
                    name:
                      type: string
                    type:
                      type: string
                    value:
                      type: string
                    ttl:
                      type: integer
              lastUpdated:
                type: string
                format: date-time
  scope: Namespaced
  names:
    plural: tenantdns
    singular: tenantdns
    kind: TenantDNS
---
# Example TenantDNS for security-verified
apiVersion: architrave.io/v1
kind: TenantDNS
metadata:
  name: security-verified-dns
  namespace: tenant-security-verified
  labels:
    tenant.architrave.io/tenant-id: security-verified
spec:
  tenantId: security-verified
  subdomain: security-verified
  domain: architrave.com
  sslEnabled: true
  wildcardEnabled: true
  loadBalancerIP: "AUTO_DETECT"  # Will be auto-detected from ALB
---
# Tenant DNS Automation Script ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-dns-automation
  namespace: hetzner-dns-system
data:
  dns-automation.py: |
    #!/usr/bin/env python3
    import requests
    import json
    import os
    import time
    from kubernetes import client, config
    
    class HetznerDNSManager:
        def __init__(self, api_token, zone_name):
            self.api_token = api_token
            self.zone_name = zone_name
            self.base_url = "https://dns.hetzner.com/api/v1"
            self.headers = {
                "Auth-API-Token": api_token,
                "Content-Type": "application/json"
            }
            
        def get_zone_id(self):
            response = requests.get(f"{self.base_url}/zones", headers=self.headers)
            zones = response.json()["zones"]
            for zone in zones:
                if zone["name"] == self.zone_name:
                    return zone["id"]
            return None
            
        def create_dns_record(self, name, record_type, value, ttl=300):
            zone_id = self.get_zone_id()
            if not zone_id:
                raise Exception(f"Zone {self.zone_name} not found")
                
            data = {
                "zone_id": zone_id,
                "type": record_type,
                "name": name,
                "value": value,
                "ttl": ttl
            }
            
            response = requests.post(f"{self.base_url}/records", 
                                   headers=self.headers, json=data)
            return response.json()
            
        def delete_dns_record(self, record_id):
            response = requests.delete(f"{self.base_url}/records/{record_id}", 
                                     headers=self.headers)
            return response.status_code == 200
            
        def list_records(self):
            zone_id = self.get_zone_id()
            response = requests.get(f"{self.base_url}/records?zone_id={zone_id}", 
                                  headers=self.headers)
            return response.json()["records"]
    
    def setup_tenant_dns(tenant_id, subdomain, load_balancer_ip):
        api_token = os.environ.get("HETZNER_DNS_API_TOKEN")
        dns_manager = HetznerDNSManager(api_token, "architrave.com")
        
        # Create A record for subdomain
        dns_manager.create_dns_record(subdomain, "A", load_balancer_ip)
        
        # Create wildcard CNAME record
        dns_manager.create_dns_record(f"*.{subdomain}", "CNAME", f"{subdomain}.architrave.com")
        
        print(f"DNS records created for tenant {tenant_id}")
    
    if __name__ == "__main__":
        import sys
        if len(sys.argv) != 4:
            print("Usage: python dns-automation.py <tenant_id> <subdomain> <load_balancer_ip>")
            sys.exit(1)
            
        setup_tenant_dns(sys.argv[1], sys.argv[2], sys.argv[3])
---
# CronJob for DNS cleanup
apiVersion: batch/v1
kind: CronJob
metadata:
  name: dns-cleanup
  namespace: hetzner-dns-system
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: hetzner-dns-controller
          containers:
          - name: dns-cleanup
            image: python:3.9-alpine
            command:
            - /bin/sh
            - -c
            - |
              pip install requests kubernetes
              python /scripts/dns-cleanup.py
            env:
            - name: HETZNER_DNS_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: hetzner-dns-credentials
                  key: api-token
            volumeMounts:
            - name: scripts
              mountPath: /scripts
          volumes:
          - name: scripts
            configMap:
              name: tenant-dns-automation
          restartPolicy: OnFailure
