#!/usr/bin/env python3
"""
Check and Offboard All Tenants Script
Simple Python script to identify and offboard all existing tenants
"""

import subprocess
import sys
import time
import re

def run_command(cmd, check=True, timeout=30):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            capture_output=True, 
            text=True, 
            check=check,
            timeout=timeout
        )
        return result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        print(f"⚠️ Command timed out: {cmd}")
        return "", "Command timed out"
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {e.stderr}")
            raise
        return e.stdout.strip(), e.stderr.strip()

def get_tenant_namespaces():
    """Get all tenant namespaces."""
    print("🔍 Checking for existing tenant namespaces...")
    
    try:
        stdout, stderr = run_command("kubectl get namespaces -o name", timeout=15)
        
        if stderr:
            print(f"⚠️ Warning: {stderr}")
        
        # Filter for tenant namespaces
        tenant_namespaces = []
        for line in stdout.split('\n'):
            if line.startswith('namespace/tenant-'):
                namespace = line.replace('namespace/', '')
                tenant_namespaces.append(namespace)
        
        return tenant_namespaces
    
    except Exception as e:
        print(f"❌ Failed to get namespaces: {e}")
        return []

def extract_tenant_id(namespace):
    """Extract tenant ID from namespace."""
    return namespace.replace('tenant-', '')

def offboard_tenant_python(tenant_id):
    """Offboard a tenant using the Python script."""
    print(f"\n🗑️ Offboarding tenant: {tenant_id}")
    print("=" * 50)
    
    try:
        # Try using the advanced offboarding script
        cmd = f"python3 advanced_tenant_offboard.py --tenant-id {tenant_id} --force"
        print(f"Running: {cmd}")
        
        stdout, stderr = run_command(cmd, check=False, timeout=300)
        
        if "SUCCESS" in stdout:
            print(f"✅ Tenant {tenant_id} offboarded successfully")
            return True
        else:
            print(f"⚠️ Offboarding may have failed for {tenant_id}")
            if stderr:
                print(f"Error: {stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to offboard tenant {tenant_id}: {e}")
        return False

def manual_namespace_cleanup(namespace):
    """Manually clean up a namespace."""
    print(f"🧹 Manual cleanup for namespace: {namespace}")
    
    try:
        # Force delete namespace
        stdout, stderr = run_command(f"kubectl delete namespace {namespace} --force --grace-period=0", check=False, timeout=60)
        
        if "deleted" in stdout or "not found" in stderr:
            print(f"✅ Namespace {namespace} deleted")
            return True
        else:
            print(f"⚠️ Namespace {namespace} deletion uncertain")
            return False
            
    except Exception as e:
        print(f"❌ Failed to delete namespace {namespace}: {e}")
        return False

def verify_cleanup():
    """Verify that all tenant resources are cleaned up."""
    print("\n🔍 VERIFICATION: Checking for remaining tenant resources")
    print("=" * 60)
    
    try:
        # Check for tenant namespaces
        stdout, stderr = run_command("kubectl get namespaces -o name", timeout=15)
        
        remaining_namespaces = []
        for line in stdout.split('\n'):
            if 'tenant-' in line:
                remaining_namespaces.append(line)
        
        if remaining_namespaces:
            print("❌ Remaining tenant namespaces found:")
            for ns in remaining_namespaces:
                print(f"  - {ns}")
            return False
        else:
            print("✅ No tenant namespaces remaining")
            return True
            
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main function."""
    print("🧹 COMPLETE TENANT OFFBOARDING - ALL TENANTS")
    print("=" * 50)
    print("This script will find and offboard all existing tenants")
    print()
    
    # Get all tenant namespaces
    tenant_namespaces = get_tenant_namespaces()
    
    if not tenant_namespaces:
        print("✅ No tenant namespaces found - system is already clean")
        verify_cleanup()
        return True
    
    print(f"📋 Found {len(tenant_namespaces)} tenant namespace(s):")
    for ns in tenant_namespaces:
        tenant_id = extract_tenant_id(ns)
        print(f"  - {ns} (tenant-id: {tenant_id})")
    
    print()
    
    # Offboard each tenant
    successful_offboards = 0
    failed_offboards = 0
    
    for namespace in tenant_namespaces:
        tenant_id = extract_tenant_id(namespace)
        
        # Try Python offboarding first
        if offboard_tenant_python(tenant_id):
            successful_offboards += 1
        else:
            print(f"⚠️ Python offboarding failed for {tenant_id}, trying manual cleanup...")
            
            # Try manual cleanup
            if manual_namespace_cleanup(namespace):
                successful_offboards += 1
            else:
                failed_offboards += 1
        
        # Small delay between offboards
        time.sleep(2)
    
    # Summary
    print(f"\n📊 OFFBOARDING SUMMARY")
    print("=" * 30)
    print(f"Total tenants found: {len(tenant_namespaces)}")
    print(f"Successfully offboarded: {successful_offboards}")
    print(f"Failed: {failed_offboards}")
    print()
    
    # Final verification
    if verify_cleanup():
        print("\n🎉 COMPLETE TENANT OFFBOARDING SUCCESSFUL!")
        print("=" * 50)
        print("✅ All tenants have been completely offboarded")
        print("✅ System is now in a clean slate state")
        print("🚀 Ready for fresh tenant onboarding!")
        return True
    else:
        print("\n⚠️ PARTIAL SUCCESS - Some cleanup may be required")
        print("Please check the verification output above")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Offboarding interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
