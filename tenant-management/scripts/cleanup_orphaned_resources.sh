#!/bin/bash

# Cleanup Orphaned Resources Script
# Cleans up orphaned S3 PVs and storage classes from previous tenant deployments

echo "🧹 CLEANING UP ORPHANED TENANT RESOURCES"
echo "========================================"

# Clean up orphaned S3 persistent volumes
echo "Cleaning up orphaned S3 persistent volumes..."
kubectl get pv | grep tenant | awk '{print $1}' | while read pv; do
    echo "Deleting PV: $pv"
    kubectl delete pv "$pv" --ignore-not-found=true &
done

# Clean up orphaned storage classes
echo "Cleaning up orphaned storage classes..."
kubectl get storageclass | grep tenant | awk '{print $1}' | while read sc; do
    echo "Deleting StorageClass: $sc"
    kubectl delete storageclass "$sc" --ignore-not-found=true &
done

# Wait for background jobs
wait

echo "✅ Cleanup completed"
