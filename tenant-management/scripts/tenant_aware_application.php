<?php
/**
 * Complete Tenant-Aware Application Framework
 * This provides comprehensive tenant isolation at the application level
 */

class TenantManager {
    private static $instance = null;
    private $tenantId = null;
    private $tenantConfig = null;
    private $pdo = null;
    
    private function __construct() {
        $this->initializeTenant();
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function initializeTenant() {
        // Get tenant ID from various sources
        $this->tenantId = $this->detectTenantId();
        
        if ($this->tenantId) {
            $this->loadTenantConfig();
            $this->setDatabaseContext();
        }
    }
    
    private function detectTenantId() {
        // Priority order for tenant detection
        $sources = [
            $_ENV['TENANT_ID'] ?? null,
            $_SERVER['HTTP_X_TENANT_ID'] ?? null,
            $this->extractFromSubdomain(),
            $this->extractFromPath(),
            'default'
        ];
        
        foreach ($sources as $source) {
            if ($source && $this->validateTenantId($source)) {
                return $source;
            }
        }
        
        return 'default';
    }
    
    private function extractFromSubdomain() {
        $host = $_SERVER['HTTP_HOST'] ?? '';
        if (preg_match('/^([^.]+)\./', $host, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    private function extractFromPath() {
        $path = $_SERVER['REQUEST_URI'] ?? '';
        if (preg_match('/^\/tenant\/([^\/]+)/', $path, $matches)) {
            return $matches[1];
        }
        return null;
    }
    
    private function validateTenantId($tenantId) {
        return preg_match('/^[a-z0-9-]+$/', $tenantId);
    }
    
    private function loadTenantConfig() {
        try {
            $pdo = $this->getDatabaseConnection();
            $stmt = $pdo->prepare("
                SELECT * FROM tenant_config 
                WHERE tenant_id = ? AND status = 'active'
            ");
            $stmt->execute([$this->tenantId]);
            $this->tenantConfig = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$this->tenantConfig) {
                throw new Exception("Tenant not found or inactive: " . $this->tenantId);
            }
        } catch (Exception $e) {
            error_log("Failed to load tenant config: " . $e->getMessage());
            throw new Exception("Invalid tenant configuration");
        }
    }
    
    private function setDatabaseContext() {
        try {
            $pdo = $this->getDatabaseConnection();
            $pdo->exec("CALL sp_set_tenant_context('" . $this->tenantId . "')");
        } catch (Exception $e) {
            error_log("Failed to set database context: " . $e->getMessage());
        }
    }
    
    public function getDatabaseConnection() {
        if ($this->pdo === null) {
            $host = $_ENV['DB_HOST'] ?? 'production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com';
            $port = $_ENV['DB_PORT'] ?? '3306';
            $dbname = $_ENV['DB_NAME'] ?? 'architrave';
            $username = $_ENV['DB_USER'] ?? 'admin';
            $password = $_ENV['DB_PASSWORD'] ?? '&BZzY_<AK(=a*UhZ';
            
            $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
            ];
            
            $this->pdo = new PDO($dsn, $username, $password, $options);
        }
        
        return $this->pdo;
    }
    
    public function getTenantId() {
        return $this->tenantId;
    }
    
    public function getTenantConfig() {
        return $this->tenantConfig;
    }
    
    public function query($sql, $params = []) {
        $pdo = $this->getDatabaseConnection();
        
        // Automatically add tenant filter for SELECT queries
        if (stripos(trim($sql), 'SELECT') === 0 && stripos($sql, 'tenant_id') === false) {
            $sql = $this->addTenantFilter($sql);
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    private function addTenantFilter($sql) {
        // Simple tenant filter injection (can be enhanced)
        $tables = ['users', 'documents', 'folders', 'assets', 'workflows', 'notifications'];
        
        foreach ($tables as $table) {
            if (stripos($sql, "FROM {$table}") !== false || stripos($sql, "JOIN {$table}") !== false) {
                if (stripos($sql, 'WHERE') !== false) {
                    $sql = str_ireplace(
                        "FROM {$table}",
                        "FROM {$table}",
                        $sql
                    );
                    $sql .= " AND {$table}.tenant_id = '" . $this->tenantId . "'";
                } else {
                    $sql .= " WHERE {$table}.tenant_id = '" . $this->tenantId . "'";
                }
                break;
            }
        }
        
        return $sql;
    }
    
    public function createUser($userData) {
        $userData['tenant_id'] = $this->tenantId;
        $userData['created_at'] = date('Y-m-d H:i:s');
        $userData['updated_at'] = date('Y-m-d H:i:s');
        
        $fields = implode(', ', array_keys($userData));
        $placeholders = ':' . implode(', :', array_keys($userData));
        
        $sql = "INSERT INTO users ({$fields}) VALUES ({$placeholders})";
        $stmt = $this->query($sql, $userData);
        
        return $this->getDatabaseConnection()->lastInsertId();
    }
    
    public function getUsers($limit = 100, $offset = 0) {
        $sql = "SELECT * FROM users WHERE tenant_id = ? LIMIT ? OFFSET ?";
        $stmt = $this->query($sql, [$this->tenantId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    public function getDocuments($limit = 100, $offset = 0) {
        $sql = "SELECT * FROM documents WHERE tenant_id = ? LIMIT ? OFFSET ?";
        $stmt = $this->query($sql, [$this->tenantId, $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    public function getTenantStats() {
        $sql = "CALL sp_get_tenant_stats(?)";
        $stmt = $this->query($sql, [$this->tenantId]);
        
        $stats = [];
        while ($row = $stmt->fetch()) {
            $stats[$row['entity']] = $row['count'];
        }
        
        return $stats;
    }
    
    public function validateAccess($table, $recordId) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE id = ? AND tenant_id = ?";
        $stmt = $this->query($sql, [$recordId, $this->tenantId]);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    public function auditLog($operation, $table, $recordId = null, $oldValues = null, $newValues = null) {
        $sql = "
            INSERT INTO tenant_audit_log 
            (tenant_id, operation, table_name, record_id, old_values, new_values, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ";
        
        $this->query($sql, [
            $this->tenantId,
            $operation,
            $table,
            $recordId,
            $oldValues ? json_encode($oldValues) : null,
            $newValues ? json_encode($newValues) : null,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
}

// Tenant-aware middleware
class TenantMiddleware {
    public static function handle() {
        try {
            $tenant = TenantManager::getInstance();
            
            // Set tenant context in headers
            header('X-Tenant-ID: ' . $tenant->getTenantId());
            
            // Validate tenant access
            $config = $tenant->getTenantConfig();
            if (!$config) {
                http_response_code(403);
                echo json_encode(['error' => 'Invalid or inactive tenant']);
                exit;
            }
            
            return $tenant;
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Tenant initialization failed: ' . $e->getMessage()]);
            exit;
        }
    }
}

// Health check endpoint
function tenantHealthCheck() {
    try {
        $tenant = TenantManager::getInstance();
        $config = $tenant->getTenantConfig();
        $stats = $tenant->getTenantStats();
        
        return [
            'status' => 'healthy',
            'tenant_id' => $tenant->getTenantId(),
            'tenant_name' => $config['tenant_name'] ?? 'Unknown',
            'tenant_config' => $config,
            'tenant_stats' => $stats,
            'database_connection' => 'ok',
            'multi_tenancy' => 'enabled',
            'timestamp' => date('Y-m-d H:i:s')
        ];
    } catch (Exception $e) {
        return [
            'status' => 'unhealthy',
            'error' => $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// CLI support
function tenantCLI($args) {
    if (empty($args[1])) {
        echo "Usage: php tenant-cli.php <tenant-id> <command>\n";
        exit(1);
    }
    
    $_ENV['TENANT_ID'] = $args[1];
    $command = $args[2] ?? 'status';
    
    $tenant = TenantManager::getInstance();
    
    switch ($command) {
        case 'status':
            $health = tenantHealthCheck();
            echo json_encode($health, JSON_PRETTY_PRINT) . "\n";
            break;
            
        case 'stats':
            $stats = $tenant->getTenantStats();
            echo "Tenant Statistics:\n";
            foreach ($stats as $entity => $count) {
                echo "  {$entity}: {$count}\n";
            }
            break;
            
        case 'users':
            $users = $tenant->getUsers(10);
            echo "Recent Users:\n";
            foreach ($users as $user) {
                echo "  ID: {$user['id']}, Username: {$user['username']}, Email: {$user['email']}\n";
            }
            break;
            
        default:
            echo "Unknown command: {$command}\n";
            exit(1);
    }
}

// Auto-initialize if not in CLI mode
if (php_sapi_name() !== 'cli') {
    TenantMiddleware::handle();
}
?>
