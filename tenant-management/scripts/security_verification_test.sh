#!/bin/bash

# Security Verification Test Script
# Tests all security implementations for tenant onboarding

TENANT_ID="security-test"
NAMESPACE="tenant-$TENANT_ID"

echo "🔍 COMPREHENSIVE SECURITY VERIFICATION TEST"
echo "=============================================="
echo "Testing tenant: $TENANT_ID"
echo "Namespace: $NAMESPACE"
echo ""

# Function to check if command succeeded
check_result() {
    if [ $? -eq 0 ]; then
        echo "✅ $1"
    else
        echo "❌ $1"
    fi
}

# Function to check if resource exists
check_resource() {
    local resource_type=$1
    local resource_name=$2
    local namespace=$3
    
    if [ -n "$namespace" ]; then
        kubectl get $resource_type $resource_name -n $namespace >/dev/null 2>&1
    else
        kubectl get $resource_type $resource_name >/dev/null 2>&1
    fi
    
    if [ $? -eq 0 ]; then
        echo "✅ $resource_type/$resource_name exists"
        return 0
    else
        echo "❌ $resource_type/$resource_name missing"
        return 1
    fi
}

echo "🧪 STEP 1: Create Test Tenant Namespace"
echo "----------------------------------------"

# Create namespace with Pod Security Standards
kubectl create namespace $NAMESPACE 2>/dev/null
kubectl label namespace $NAMESPACE tenant.architrave.io/tenant-id=$TENANT_ID 2>/dev/null
kubectl label namespace $NAMESPACE pod-security.kubernetes.io/enforce=restricted 2>/dev/null
kubectl label namespace $NAMESPACE pod-security.kubernetes.io/audit=restricted 2>/dev/null
kubectl label namespace $NAMESPACE pod-security.kubernetes.io/warn=restricted 2>/dev/null

check_result "Namespace created with Pod Security Standards"

echo ""
echo "🛡️ STEP 2: Apply Enhanced Security Policies"
echo "--------------------------------------------"

# Apply enhanced security policies from our security module
cat > /tmp/enhanced_security_$TENANT_ID.yaml << EOF
# Enhanced Resource Limits
apiVersion: v1
kind: LimitRange
metadata:
  name: $TENANT_ID-enhanced-limits
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
      ephemeral-storage: "100Mi"
    max:
      cpu: "2"
      memory: "4Gi"
      ephemeral-storage: "10Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
      ephemeral-storage: "50Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Pod
---
# Strict Network Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: $TENANT_ID-strict-isolation
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: $TENANT_ID
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: $TENANT_ID
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 5672
---
# Cross-Tenant Denial Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: $TENANT_ID-deny-cross-tenant
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["$TENANT_ID"]
  egress:
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["$TENANT_ID"]
EOF

kubectl apply -f /tmp/enhanced_security_$TENANT_ID.yaml
check_result "Enhanced security policies applied"

echo ""
echo "🔐 STEP 3: Apply Istio Security Policies"
echo "----------------------------------------"

# Apply Istio security policies
cat > /tmp/istio_security_$TENANT_ID.yaml << EOF
# Strict mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: $TENANT_ID-strict-mtls
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  mtls:
    mode: STRICT
---
# Deny All Authorization Policy
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: $TENANT_ID-deny-all
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  action: DENY
  rules:
  - from:
    - source:
        notNamespaces: ["$NAMESPACE", "istio-system"]
EOF

kubectl apply -f /tmp/istio_security_$TENANT_ID.yaml
check_result "Istio security policies applied"

echo ""
echo "📊 STEP 4: Verify Security Implementations"
echo "============================================"

echo ""
echo "4.1 Pod Security Standards:"
kubectl get namespace $NAMESPACE -o yaml | grep -A 3 "pod-security" || echo "❌ Pod Security Standards not found"

echo ""
echo "4.2 Network Policies:"
check_resource "networkpolicy" "$TENANT_ID-strict-isolation" "$NAMESPACE"
check_resource "networkpolicy" "$TENANT_ID-deny-cross-tenant" "$NAMESPACE"

echo ""
echo "4.3 Istio Security:"
check_resource "peerauthentication" "$TENANT_ID-strict-mtls" "$NAMESPACE"
check_resource "authorizationpolicy" "$TENANT_ID-deny-all" "$NAMESPACE"

echo ""
echo "4.4 Resource Limits:"
check_resource "limitrange" "$TENANT_ID-enhanced-limits" "$NAMESPACE"

echo ""
echo "🧪 STEP 5: Test Cross-Tenant Isolation"
echo "======================================="

# Create a test pod to verify security
cat > /tmp/test_pod_$TENANT_ID.yaml << EOF
apiVersion: v1
kind: Pod
metadata:
  name: security-test-pod
  namespace: $NAMESPACE
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
spec:
  securityContext:
    runAsUser: 1000
    runAsGroup: 1000
    runAsNonRoot: true
    fsGroup: 1000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: test-container
    image: busybox:latest
    command: ["sleep", "3600"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsUser: 1000
      runAsGroup: 1000
      runAsNonRoot: true
      readOnlyRootFilesystem: true
      capabilities:
        drop: ["ALL"]
    resources:
      requests:
        memory: "64Mi"
        cpu: "50m"
      limits:
        memory: "128Mi"
        cpu: "100m"
EOF

kubectl apply -f /tmp/test_pod_$TENANT_ID.yaml
sleep 10

if kubectl get pod security-test-pod -n $NAMESPACE >/dev/null 2>&1; then
    echo "✅ Test pod created successfully with security contexts"
    
    # Check pod security context
    echo ""
    echo "Pod Security Context:"
    kubectl get pod security-test-pod -n $NAMESPACE -o jsonpath='{.spec.securityContext}' | jq .
    
    echo ""
    echo "Container Security Context:"
    kubectl get pod security-test-pod -n $NAMESPACE -o jsonpath='{.spec.containers[0].securityContext}' | jq .
else
    echo "❌ Test pod creation failed - Pod Security Standards may be blocking"
fi

echo ""
echo "🔍 STEP 6: Security Verification Summary"
echo "========================================"

echo ""
echo "Checking all security components:"

# Check namespace labels
echo -n "Pod Security Standards: "
if kubectl get namespace $NAMESPACE -o yaml | grep -q "pod-security.kubernetes.io/enforce: restricted"; then
    echo "✅ ENABLED (restricted)"
else
    echo "❌ NOT CONFIGURED"
fi

# Check network policies
echo -n "Network Policies: "
NP_COUNT=$(kubectl get networkpolicy -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ $NP_COUNT -ge 2 ]; then
    echo "✅ CONFIGURED ($NP_COUNT policies)"
else
    echo "❌ INSUFFICIENT ($NP_COUNT policies)"
fi

# Check Istio security
echo -n "Istio mTLS: "
if kubectl get peerauthentication -n $NAMESPACE --no-headers 2>/dev/null | grep -q "STRICT\|strict"; then
    echo "✅ STRICT MODE"
else
    echo "❌ NOT STRICT"
fi

echo -n "Authorization Policies: "
AP_COUNT=$(kubectl get authorizationpolicy -n $NAMESPACE --no-headers 2>/dev/null | wc -l)
if [ $AP_COUNT -ge 1 ]; then
    echo "✅ CONFIGURED ($AP_COUNT policies)"
else
    echo "❌ NOT CONFIGURED"
fi

# Check resource limits
echo -n "Resource Limits: "
if kubectl get limitrange -n $NAMESPACE --no-headers 2>/dev/null | grep -q "$TENANT_ID-enhanced-limits"; then
    echo "✅ CONFIGURED"
else
    echo "❌ NOT CONFIGURED"
fi

echo ""
echo "🎯 STEP 7: Cleanup Test Resources"
echo "================================="

# Cleanup
kubectl delete namespace $NAMESPACE 2>/dev/null
rm -f /tmp/enhanced_security_$TENANT_ID.yaml /tmp/istio_security_$TENANT_ID.yaml /tmp/test_pod_$TENANT_ID.yaml

echo "✅ Test resources cleaned up"

echo ""
echo "🏁 SECURITY VERIFICATION TEST COMPLETED"
echo "========================================"
