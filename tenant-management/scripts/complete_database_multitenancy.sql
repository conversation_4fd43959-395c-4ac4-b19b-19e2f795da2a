-- Complete Database Multi-Tenancy Implementation
-- This script adds tenant_id to ALL tables and implements complete isolation

-- Step 1: Add tenant_id to all major tables
ALTER TABLE users ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE folders ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE assets ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE user_groups ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE workflows ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE workflow_steps ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE settings ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE permissions ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE sessions ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE file_uploads ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE email_templates ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
ALTER TABLE system_logs ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';

-- Step 2: Create performance indexes
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX IF NOT EXISTS idx_folders_tenant_id ON folders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_assets_tenant_id ON assets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_tenant_id ON user_groups(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflows_tenant_id ON workflows(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_tenant_id ON workflow_steps(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_settings_tenant_id ON settings(tenant_id);
CREATE INDEX IF NOT EXISTS idx_permissions_tenant_id ON permissions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_sessions_tenant_id ON sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_file_uploads_tenant_id ON file_uploads(tenant_id);
CREATE INDEX IF NOT EXISTS idx_email_templates_tenant_id ON email_templates(tenant_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_tenant_id ON system_logs(tenant_id);

-- Step 3: Insert tenant configurations
INSERT INTO tenant_config (tenant_id, tenant_name, subdomain, s3_bucket, status) 
VALUES 
    ('security-verified', 'Security Verified Company', 'security-verified', 'tenant-security-verified-assets', 'active'),
    ('fresh-test', 'Fresh Test Company', 'fresh-test', 'tenant-fresh-test-assets', 'active')
ON DUPLICATE KEY UPDATE 
    tenant_name = VALUES(tenant_name),
    subdomain = VALUES(subdomain),
    s3_bucket = VALUES(s3_bucket),
    status = VALUES(status),
    updated_at = CURRENT_TIMESTAMP;

-- Step 4: Create tenant-aware views
CREATE OR REPLACE VIEW v_tenant_users AS 
SELECT u.*, tc.tenant_name FROM users u 
JOIN tenant_config tc ON u.tenant_id = tc.tenant_id 
WHERE tc.status = 'active';

CREATE OR REPLACE VIEW v_tenant_documents AS 
SELECT d.*, tc.tenant_name FROM documents d 
JOIN tenant_config tc ON d.tenant_id = tc.tenant_id 
WHERE tc.status = 'active';

CREATE OR REPLACE VIEW v_tenant_folders AS 
SELECT f.*, tc.tenant_name FROM folders f 
JOIN tenant_config tc ON f.tenant_id = tc.tenant_id 
WHERE tc.status = 'active';

-- Step 5: Create stored procedures for tenant operations
DELIMITER //

CREATE OR REPLACE PROCEDURE sp_set_tenant_context(IN p_tenant_id VARCHAR(50))
BEGIN
    DECLARE tenant_exists INT DEFAULT 0;
    
    SELECT COUNT(*) INTO tenant_exists 
    FROM tenant_config 
    WHERE tenant_id = p_tenant_id AND status = 'active';
    
    IF tenant_exists = 0 THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'Invalid or inactive tenant';
    END IF;
    
    SET @current_tenant_id = p_tenant_id;
END //

CREATE OR REPLACE PROCEDURE sp_get_tenant_stats(IN p_tenant_id VARCHAR(50))
BEGIN
    CALL sp_set_tenant_context(p_tenant_id);
    
    SELECT 
        'users' as entity,
        COUNT(*) as count
    FROM users WHERE tenant_id = p_tenant_id
    UNION ALL
    SELECT 
        'documents' as entity,
        COUNT(*) as count
    FROM documents WHERE tenant_id = p_tenant_id
    UNION ALL
    SELECT 
        'folders' as entity,
        COUNT(*) as count
    FROM folders WHERE tenant_id = p_tenant_id
    UNION ALL
    SELECT 
        'assets' as entity,
        COUNT(*) as count
    FROM assets WHERE tenant_id = p_tenant_id;
END //

CREATE OR REPLACE PROCEDURE sp_create_tenant_user(
    IN p_tenant_id VARCHAR(50),
    IN p_username VARCHAR(255),
    IN p_email VARCHAR(255),
    IN p_password VARCHAR(255),
    IN p_first_name VARCHAR(255),
    IN p_last_name VARCHAR(255)
)
BEGIN
    CALL sp_set_tenant_context(p_tenant_id);
    
    INSERT INTO users (
        tenant_id, username, email, password, 
        first_name, last_name, created_at, updated_at
    ) VALUES (
        p_tenant_id, p_username, p_email, p_password,
        p_first_name, p_last_name, NOW(), NOW()
    );
    
    SELECT LAST_INSERT_ID() as user_id;
END //

DELIMITER ;

-- Step 6: Create triggers for automatic tenant_id assignment
DELIMITER //

CREATE OR REPLACE TRIGGER tr_users_tenant_id
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@current_tenant_id, 'default');
    END IF;
END //

CREATE OR REPLACE TRIGGER tr_documents_tenant_id
BEFORE INSERT ON documents
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@current_tenant_id, 'default');
    END IF;
END //

CREATE OR REPLACE TRIGGER tr_folders_tenant_id
BEFORE INSERT ON folders
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@current_tenant_id, 'default');
    END IF;
END //

DELIMITER ;

-- Step 7: Create tenant isolation verification
CREATE OR REPLACE VIEW v_tenant_isolation_check AS
SELECT 
    tc.tenant_id,
    tc.tenant_name,
    (SELECT COUNT(*) FROM users WHERE tenant_id = tc.tenant_id) as users_count,
    (SELECT COUNT(*) FROM documents WHERE tenant_id = tc.tenant_id) as documents_count,
    (SELECT COUNT(*) FROM folders WHERE tenant_id = tc.tenant_id) as folders_count,
    (SELECT COUNT(*) FROM assets WHERE tenant_id = tc.tenant_id) as assets_count
FROM tenant_config tc
WHERE tc.status = 'active';

-- Step 8: Create audit table for tenant operations
CREATE TABLE IF NOT EXISTS tenant_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    operation VARCHAR(100) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    user_id INT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tenant_audit_tenant_id (tenant_id),
    INDEX idx_tenant_audit_created_at (created_at),
    INDEX idx_tenant_audit_operation (operation)
);

-- Step 9: Verification queries
SELECT 'Tenant Configuration Check' as test;
SELECT tenant_id, tenant_name, status FROM tenant_config;

SELECT 'Tables with tenant_id column' as test;
SELECT COUNT(*) as tables_with_tenant_id 
FROM information_schema.columns 
WHERE table_schema = 'architrave' AND column_name = 'tenant_id';

SELECT 'Tenant Isolation Verification' as test;
SELECT * FROM v_tenant_isolation_check;
