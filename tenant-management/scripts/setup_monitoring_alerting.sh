#!/bin/bash

# Monitoring & Alerting Setup Script
# Implements comprehensive monitoring, alerting, and security dashboards

set -e

echo "📊 SETTING UP COMPREHENSIVE MONITORING & ALERTING"
echo "================================================="

# Function to create security metrics
create_security_metrics() {
    echo "📈 Creating Security Metrics..."
    
    # Create ServiceMonitor for tenant security metrics
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-security-metrics
  namespace: monitoring
  labels:
    security.architrave.io/monitoring: "enabled"
spec:
  selector:
    matchLabels:
      security.architrave.io/metrics: "enabled"
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
  namespaceSelector:
    matchNames:
    - monitoring
    - falco-system
    - gatekeeper-system
EOF
    
    echo "✅ Security metrics ServiceMonitor created"
}

# Function to create security alert rules
create_security_alerts() {
    echo "🚨 Creating Security Alert Rules..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tenant-security-alerts
  namespace: monitoring
  labels:
    security.architrave.io/alerts: "enabled"
spec:
  groups:
  - name: tenant.security.rules
    rules:
    - alert: TenantPodSecurityViolation
      expr: increase(falco_events_total{rule_name=~".*privilege.*|.*root.*"}[5m]) > 0
      for: 0m
      labels:
        severity: critical
        component: security
      annotations:
        summary: "Tenant pod security violation detected"
        description: "Pod security violation detected in tenant {{ \$labels.tenant_id }}"
    
    - alert: TenantNetworkPolicyViolation
      expr: increase(gatekeeper_violations_total{violation_kind="NetworkPolicy"}[5m]) > 0
      for: 0m
      labels:
        severity: warning
        component: security
      annotations:
        summary: "Tenant network policy violation"
        description: "Network policy violation in tenant {{ \$labels.tenant_id }}"
    
    - alert: TenantResourceQuotaExceeded
      expr: kube_resourcequota_used / kube_resourcequota_hard > 0.9
      for: 5m
      labels:
        severity: warning
        component: resources
      annotations:
        summary: "Tenant resource quota nearly exceeded"
        description: "Tenant {{ \$labels.tenant_id }} is using {{ \$value }}% of resource quota"
    
    - alert: TenantPodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
      for: 5m
      labels:
        severity: critical
        component: availability
      annotations:
        summary: "Tenant pod crash looping"
        description: "Pod {{ \$labels.pod }} in tenant {{ \$labels.tenant_id }} is crash looping"
    
    - alert: TenantHighMemoryUsage
      expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
      for: 10m
      labels:
        severity: warning
        component: performance
      annotations:
        summary: "Tenant high memory usage"
        description: "Container {{ \$labels.container }} in tenant {{ \$labels.tenant_id }} is using {{ \$value }}% memory"
    
    - alert: TenantHighCPUUsage
      expr: rate(container_cpu_usage_seconds_total[5m]) / container_spec_cpu_quota * 100 > 90
      for: 10m
      labels:
        severity: warning
        component: performance
      annotations:
        summary: "Tenant high CPU usage"
        description: "Container {{ \$labels.container }} in tenant {{ \$labels.tenant_id }} is using {{ \$value }}% CPU"
    
    - alert: TenantVulnerabilityDetected
      expr: trivy_vulnerabilities_total{severity="CRITICAL"} > 0
      for: 0m
      labels:
        severity: critical
        component: security
      annotations:
        summary: "Critical vulnerability detected in tenant"
        description: "Critical vulnerability found in tenant {{ \$labels.tenant_id }} image {{ \$labels.image }}"
EOF
    
    echo "✅ Security alert rules created"
}

# Function to create Grafana dashboards
create_grafana_dashboards() {
    echo "📊 Creating Grafana Security Dashboards..."
    
    # Create ConfigMap with dashboard JSON
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-security-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  tenant-security.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Tenant Security Dashboard",
        "tags": ["tenant", "security"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Security Violations",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(falco_events_total[5m]))",
                "legendFormat": "Security Events/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Policy Violations",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(gatekeeper_violations_total)",
                "legendFormat": "Policy Violations"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Tenant Resource Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (tenant_id) (container_memory_usage_bytes{namespace=~\"tenant-.*\"})",
                "legendFormat": "Memory - {{tenant_id}}"
              },
              {
                "expr": "sum by (tenant_id) (rate(container_cpu_usage_seconds_total{namespace=~\"tenant-.*\"}[5m]))",
                "legendFormat": "CPU - {{tenant_id}}"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
          },
          {
            "id": 4,
            "title": "Vulnerability Summary",
            "type": "table",
            "targets": [
              {
                "expr": "trivy_vulnerabilities_total",
                "format": "table"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s"
      }
    }
EOF
    
    echo "✅ Grafana security dashboard created"
}

# Function to setup incident response automation
setup_incident_response() {
    echo "🚨 Setting up Incident Response Automation..."
    
    # Create incident response webhook
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: incident-response-config
  namespace: monitoring
data:
  webhook.yaml: |
    route:
      group_by: ['alertname', 'tenant_id']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'tenant-security-webhook'
    receivers:
    - name: 'tenant-security-webhook'
      webhook_configs:
      - url: 'http://incident-response-service:8080/webhook'
        send_resolved: true
EOF
    
    # Create incident response service
    cat <<EOF | kubectl apply -f -
apiVersion: apps/v1
kind: Deployment
metadata:
  name: incident-response-service
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: incident-response
  template:
    metadata:
      labels:
        app: incident-response
    spec:
      containers:
      - name: incident-response
        image: nginx:alpine
        ports:
        - containerPort: 8080
        env:
        - name: WEBHOOK_PORT
          value: "8080"
---
apiVersion: v1
kind: Service
metadata:
  name: incident-response-service
  namespace: monitoring
spec:
  selector:
    app: incident-response
  ports:
  - port: 8080
    targetPort: 8080
EOF
    
    echo "✅ Incident response automation setup completed"
}

# Function to create performance monitoring
setup_performance_monitoring() {
    echo "⚡ Setting up Performance Monitoring..."
    
    # Create performance metrics collection
    cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-performance-metrics
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: "tenant-performance"
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
  namespaceSelector:
    matchNames:
    - monitoring
EOF
    
    # Create performance dashboard
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-performance-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
data:
  tenant-performance.json: |
    {
      "dashboard": {
        "title": "Tenant Performance Dashboard",
        "panels": [
          {
            "title": "Response Time",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{namespace=~\"tenant-.*\"}[5m]))",
                "legendFormat": "95th percentile - {{tenant_id}}"
              }
            ]
          },
          {
            "title": "Throughput",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (tenant_id) (rate(http_requests_total{namespace=~\"tenant-.*\"}[5m]))",
                "legendFormat": "Requests/sec - {{tenant_id}}"
              }
            ]
          },
          {
            "title": "Error Rate",
            "type": "graph",
            "targets": [
              {
                "expr": "sum by (tenant_id) (rate(http_requests_total{namespace=~\"tenant-.*\",status=~\"5..\"}[5m])) / sum by (tenant_id) (rate(http_requests_total{namespace=~\"tenant-.*\"}[5m]))",
                "legendFormat": "Error Rate - {{tenant_id}}"
              }
            ]
          }
        ]
      }
    }
EOF
    
    echo "✅ Performance monitoring setup completed"
}

# Main function
main() {
    echo "🚀 Starting comprehensive monitoring & alerting setup..."
    echo ""
    
    # Check if monitoring namespace exists
    if ! kubectl get namespace monitoring >/dev/null 2>&1; then
        echo "❌ Monitoring namespace not found. Please install Prometheus stack first."
        echo "Run: ./install_security_tools.sh"
        exit 1
    fi
    
    echo "✅ Monitoring namespace found"
    echo ""
    
    # Setup all monitoring components
    create_security_metrics
    echo ""
    
    create_security_alerts
    echo ""
    
    create_grafana_dashboards
    echo ""
    
    setup_incident_response
    echo ""
    
    setup_performance_monitoring
    echo ""
    
    echo "🎉 COMPREHENSIVE MONITORING & ALERTING SETUP COMPLETED!"
    echo "======================================================"
    echo "✅ Security Metrics - Configured"
    echo "✅ Security Alerts - Active"
    echo "✅ Grafana Dashboards - Available"
    echo "✅ Incident Response - Automated"
    echo "✅ Performance Monitoring - Active"
    echo ""
    echo "📊 Access Dashboards:"
    echo "1. Grafana: kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring"
    echo "2. Prometheus: kubectl port-forward svc/prometheus-kube-prometheus-prometheus 9090:9090 -n monitoring"
    echo "3. AlertManager: kubectl port-forward svc/prometheus-kube-prometheus-alertmanager 9093:9093 -n monitoring"
    echo ""
    echo "🔧 Next Steps:"
    echo "1. Configure notification channels (Slack, email, etc.)"
    echo "2. Customize alert thresholds for your environment"
    echo "3. Set up log aggregation with ELK stack"
    echo "4. Configure backup monitoring"
}

# Run main function
main
