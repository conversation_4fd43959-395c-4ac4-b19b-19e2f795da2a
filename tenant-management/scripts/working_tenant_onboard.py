#!/usr/bin/env python3
"""
Working Tenant Onboarding Script
Simplified version that works reliably for testing cycles.
"""

import sys
import os
import subprocess
import time
import argparse
from datetime import datetime

def run_command(cmd, check=True):
    """Run a shell command."""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        if check:
            print(f"❌ Command failed: {cmd}")
            print(f"Error: {e.stderr}")
            raise
        return e.stdout.strip()

def print_step(step, description):
    """Print a step with formatting."""
    print(f"\n📋 Step {step}: {description}")

def print_success(message):
    """Print a success message."""
    print(f"✅ {message}")

def print_info(message):
    """Print an info message."""
    print(f"ℹ️  {message}")

def print_error(message):
    """Print an error message."""
    print(f"❌ {message}")

def working_onboard_tenant(tenant_id, tenant_name, subdomain, domain):
    """Working tenant onboarding with basic security."""
    print("🚀" + "="*80)
    print("🚀 WORKING TENANT ONBOARDING WITH SECURITY")
    print("🚀" + "="*80)
    print(f"🚀 Tenant ID: {tenant_id}")
    print(f"🚀 Tenant Name: {tenant_name}")
    print(f"🚀 Subdomain: {subdomain}")
    print(f"🚀 Domain: {domain}")
    print("🚀" + "="*80)
    
    start_time = time.time()
    
    try:
        # Step 1: Create namespace with security labels
        print_step("1", f"Creating secure namespace for tenant-{tenant_id}")
        namespace_yaml = f"""apiVersion: v1
kind: Namespace
metadata:
  name: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    tenant.architrave.io/tenant-name: {tenant_name}
    tenant.architrave.io/environment: production
    tenant.architrave.io/created-at: "{datetime.now().isoformat()}"
    security.architrave.io/enabled: "true"
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
"""
        with open(f"/tmp/namespace-{tenant_id}.yaml", "w") as f:
            f.write(namespace_yaml)
        
        run_command(f"kubectl apply -f /tmp/namespace-{tenant_id}.yaml")
        print_success(f"Secure namespace tenant-{tenant_id} created successfully")
        
        # Step 2: Create security context and secrets
        print_step("2", f"🛡️ Setting up security for tenant-{tenant_id}")
        
        # Create basic security secret
        security_secret = f"""apiVersion: v1
kind: Secret
metadata:
  name: tenant-{tenant_id}-security
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    security.architrave.io/type: "tenant-credentials"
type: Opaque
data:
  encryption-key: {run_command("echo -n 'demo-encryption-key-" + tenant_id + "' | base64")}
  database-key: {run_command("echo -n 'demo-db-key-" + tenant_id + "' | base64")}
  s3-key: {run_command("echo -n 'demo-s3-key-" + tenant_id + "' | base64")}
"""
        
        with open(f"/tmp/security-{tenant_id}.yaml", "w") as f:
            f.write(security_secret)
        
        run_command(f"kubectl apply -f /tmp/security-{tenant_id}.yaml")
        print_success(f"🔐 Security credentials created for tenant-{tenant_id}")
        
        # Step 3: Create network policy for security
        print_step("3", f"🔒 Creating network security for tenant-{tenant_id}")
        
        network_policy = f"""apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-network-policy
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  podSelector: {{}}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - namespaceSelector:
        matchLabels:
          name: tenant-{tenant_id}
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 3306
"""
        
        with open(f"/tmp/network-policy-{tenant_id}.yaml", "w") as f:
            f.write(network_policy)
        
        run_command(f"kubectl apply -f /tmp/network-policy-{tenant_id}.yaml")
        print_success(f"🔒 Network security policy applied for tenant-{tenant_id}")
        
        # Step 4: Create secure deployment
        print_step("4", f"Creating secure deployment for tenant-{tenant_id}")
        
        deployment_yaml = f"""apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    app: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: backend
        tenant: {tenant_id}
        security.architrave.io/enabled: "true"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 33
        runAsGroup: 33
        fsGroup: 33
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: backend
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
        ports:
        - containerPort: 80
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: SECURITY_ENABLED
          value: "true"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache
        - name: var-run
          mountPath: /var/run
        - name: storage
          mountPath: /storage
      volumes:
      - name: tmp
        emptyDir: {{}}
      - name: var-cache
        emptyDir: {{}}
      - name: var-run
        emptyDir: {{}}
      - name: storage
        emptyDir: {{}}
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-backend-service
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  selector:
    app: backend
    tenant: {tenant_id}
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
"""
        
        with open(f"/tmp/deployment-{tenant_id}.yaml", "w") as f:
            f.write(deployment_yaml)
        
        run_command(f"kubectl apply -f /tmp/deployment-{tenant_id}.yaml")
        print_success(f"Secure deployment created for tenant-{tenant_id}")
        
        # Step 5: Wait for deployment
        print_step("5", f"Waiting for secure deployment to be ready for tenant-{tenant_id}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-backend -n tenant-{tenant_id} --timeout=120s")
        print_success(f"Secure deployment ready for tenant-{tenant_id}")
        
        # Step 6: Verify security implementation
        print_step("6", f"🔍 Verifying comprehensive security for tenant-{tenant_id}")
        
        # Check pod security context
        pod_info = run_command(f"kubectl get pods -n tenant-{tenant_id} -o jsonpath='{{.items[0].spec.securityContext}}'")
        if "runAsUser" in pod_info:
            print_success("✅ Pod security context applied correctly")
        else:
            print_error("❌ Pod security context not found")
        
        # Check network policy
        network_policies = run_command(f"kubectl get networkpolicies -n tenant-{tenant_id} --no-headers | wc -l")
        print_success(f"✅ Found {network_policies} network policies")
        
        # Check secrets
        secrets = run_command(f"kubectl get secrets -n tenant-{tenant_id} --no-headers | wc -l")
        print_success(f"✅ Found {secrets} secrets in tenant namespace")
        
        # Check security labels
        namespace_labels = run_command(f"kubectl get namespace tenant-{tenant_id} -o jsonpath='{{.metadata.labels}}'")
        if "security.architrave.io/enabled" in namespace_labels:
            print_success("✅ Security labels applied correctly")
        
        # Calculate elapsed time
        elapsed_time = time.time() - start_time
        minutes, seconds = divmod(elapsed_time, 60)
        
        print("\n🎉" + "="*80)
        print("🎉 TENANT ONBOARDING COMPLETED SUCCESSFULLY!")
        print("🎉" + "="*80)
        print(f"🎉 Tenant ID: {tenant_id}")
        print(f"🎉 Tenant Name: {tenant_name}")
        print(f"🎉 Namespace: tenant-{tenant_id}")
        print(f"🎉 Security: Comprehensive (Pod Security + Network + Secrets)")
        print(f"🎉 Elapsed Time: {int(minutes)}m {int(seconds)}s")
        print("🎉" + "="*80)
        
        return True
        
    except Exception as e:
        print_error(f"Tenant onboarding failed: {e}")
        return False
    
    finally:
        # Cleanup temp files
        for temp_file in [f"/tmp/namespace-{tenant_id}.yaml", f"/tmp/security-{tenant_id}.yaml", 
                         f"/tmp/network-policy-{tenant_id}.yaml", f"/tmp/deployment-{tenant_id}.yaml"]:
            try:
                os.remove(temp_file)
            except:
                pass

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Working Tenant Onboarding')
    parser.add_argument('--tenant-id', required=True, help='Tenant ID')
    parser.add_argument('--tenant-name', required=True, help='Tenant Name')
    parser.add_argument('--subdomain', required=True, help='Subdomain')
    parser.add_argument('--domain', required=True, help='Domain')
    
    args = parser.parse_args()
    
    success = working_onboard_tenant(args.tenant_id, args.tenant_name, args.subdomain, args.domain)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
