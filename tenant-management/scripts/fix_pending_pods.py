#!/usr/bin/env python3
"""
Fix Pending Pods Script
Diagnoses and fixes pod scheduling issues
"""

import subprocess
import sys
import time
from datetime import datetime

def run_command(command, timeout=20):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔧 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ FIXED" if success else "❌ ISSUE"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def diagnose_pending_pods():
    """Diagnose why pods are pending."""
    print_header("DIAGNOSING PENDING PODS")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers", 10)
    if not success:
        print_result("Get Namespaces", False, "Cannot get namespaces")
        return []
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    pending_pods = []
    
    for ns in tenant_namespaces:
        # Get pending pods
        success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --field-selector=status.phase=Pending --no-headers", 10)
        if success and stdout:
            pods = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            for pod in pods:
                pending_pods.append((ns, pod))
                
                # Describe pod to get details
                success, desc_stdout, desc_stderr = run_command(f"kubectl describe pod {pod} -n {ns}", 15)
                if success:
                    print(f"\n🔍 Pod {pod} in {ns}:")
                    # Look for specific issues
                    if "Insufficient cpu" in desc_stdout or "Insufficient memory" in desc_stdout:
                        print("   Issue: Insufficient resources")
                    elif "ImagePullBackOff" in desc_stdout or "ErrImagePull" in desc_stdout:
                        print("   Issue: Image pull problems")
                    elif "FailedScheduling" in desc_stdout:
                        print("   Issue: Scheduling failure")
                    elif "PersistentVolumeClaim" in desc_stdout and "not found" in desc_stdout:
                        print("   Issue: PVC not found")
                    else:
                        print("   Issue: Unknown - check events")
    
    return pending_pods

def fix_resource_issues():
    """Fix resource constraint issues."""
    print_header("FIXING RESOURCE ISSUES")
    
    # Check cluster resources
    success, stdout, stderr = run_command("kubectl top nodes", 10)
    if success:
        print("📊 Node resource usage:")
        print(stdout)
    else:
        print("⚠️ Cannot get node resource usage")
    
    # Check if we need to scale down resource requests
    print("\n🔧 Reducing resource requests for pending pods...")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers", 10)
    if not success:
        return False
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    for ns in tenant_namespaces:
        # Get deployments
        success, stdout, stderr = run_command(f"kubectl get deployments -n {ns} --no-headers", 10)
        if success and stdout:
            deployments = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            
            for deployment in deployments:
                # Patch deployment to reduce resource requests
                patch_cmd = f"""kubectl patch deployment {deployment} -n {ns} -p '{{"spec":{{"template":{{"spec":{{"containers":[{{"name":"backend","resources":{{"requests":{{"cpu":"50m","memory":"128Mi"}},"limits":{{"cpu":"200m","memory":"256Mi"}}}}}}]}}}}}}}}' 2>/dev/null"""
                
                success, stdout, stderr = run_command(patch_cmd, 15)
                if success:
                    print_result(f"Reduced resources for {deployment}", True, "CPU: 50m, Memory: 128Mi")
                else:
                    # Try different container names
                    for container in ['frontend', 'rabbitmq']:
                        patch_cmd = f"""kubectl patch deployment {deployment} -n {ns} -p '{{"spec":{{"template":{{"spec":{{"containers":[{{"name":"{container}","resources":{{"requests":{{"cpu":"50m","memory":"128Mi"}},"limits":{{"cpu":"200m","memory":"256Mi"}}}}}}]}}}}}}}}' 2>/dev/null"""
                        run_command(patch_cmd, 10)
    
    return True

def fix_image_issues():
    """Fix image pull issues."""
    print_header("FIXING IMAGE PULL ISSUES")
    
    # Check if images are accessible
    test_images = [
        "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test",
        "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl",
        "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"
    ]
    
    for image in test_images:
        # Try to pull image on a test pod
        test_pod_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: image-test-{int(time.time())}
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: test
    image: {image}
    command: ["echo", "test"]
    resources:
      limits:
        cpu: 50m
        memory: 64Mi
"""
        
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
            f.write(test_pod_yaml)
            pod_file = f.name
        
        try:
            success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}", 10)
            if success:
                pod_name = f"image-test-{int(time.time())}"
                time.sleep(5)
                
                # Check if pod started
                success, stdout, stderr = run_command(f"kubectl get pod {pod_name} --no-headers", 5)
                if success and "Running" in stdout:
                    print_result(f"Image {image.split('/')[-1]}", True, "Image accessible")
                else:
                    print_result(f"Image {image.split('/')[-1]}", False, "Image pull issues")
                
                # Cleanup
                run_command(f"kubectl delete pod {pod_name} --ignore-not-found=true", 5)
        finally:
            import os
            os.unlink(pod_file)

def fix_storage_issues():
    """Fix storage/PVC issues."""
    print_header("FIXING STORAGE ISSUES")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers", 10)
    if not success:
        return False
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    for ns in tenant_namespaces:
        # Check PVCs
        success, stdout, stderr = run_command(f"kubectl get pvc -n {ns} --no-headers", 10)
        if success and stdout:
            pvcs = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            
            for pvc in pvcs:
                # Check PVC status
                success, stdout, stderr = run_command(f"kubectl get pvc {pvc} -n {ns} -o jsonpath='{{.status.phase}}'", 10)
                if success:
                    if stdout != "Bound":
                        print_result(f"PVC {pvc} in {ns}", False, f"Status: {stdout}")
                        
                        # Try to delete and recreate PVC if it's stuck
                        if stdout == "Pending":
                            print(f"   Attempting to fix pending PVC {pvc}")
                            run_command(f"kubectl delete pvc {pvc} -n {ns} --ignore-not-found=true", 10)
                    else:
                        print_result(f"PVC {pvc} in {ns}", True, "Bound successfully")

def restart_failed_deployments():
    """Restart failed deployments."""
    print_header("RESTARTING FAILED DEPLOYMENTS")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers", 10)
    if not success:
        return False
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    for ns in tenant_namespaces:
        # Get deployments
        success, stdout, stderr = run_command(f"kubectl get deployments -n {ns} --no-headers", 10)
        if success and stdout:
            deployments = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            
            for deployment in deployments:
                # Restart deployment
                success, stdout, stderr = run_command(f"kubectl rollout restart deployment {deployment} -n {ns}", 15)
                if success:
                    print_result(f"Restarted {deployment}", True, "Deployment restarted")
                    
                    # Wait a bit for restart
                    time.sleep(2)
                else:
                    print_result(f"Restart {deployment}", False, "Failed to restart")

def verify_fixes():
    """Verify that fixes worked."""
    print_header("VERIFYING FIXES")
    
    time.sleep(10)  # Wait for pods to start
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers", 10)
    if not success:
        return False
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    total_pods = 0
    running_pods = 0
    pending_pods = 0
    
    for ns in tenant_namespaces:
        # Check pod status
        success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers", 10)
        if success and stdout:
            pods = [line for line in stdout.split('\n') if line.strip()]
            total_pods += len(pods)
            
            for pod_line in pods:
                if 'Running' in pod_line:
                    running_pods += 1
                elif 'Pending' in pod_line:
                    pending_pods += 1
    
    print(f"📊 Pod Status Summary:")
    print(f"   Total pods: {total_pods}")
    print(f"   Running pods: {running_pods}")
    print(f"   Pending pods: {pending_pods}")
    
    if running_pods > 0:
        print_result("Pod Fixes", True, f"{running_pods}/{total_pods} pods running")
        return True
    else:
        print_result("Pod Fixes", False, "No pods running yet")
        return False

def main():
    """Main fix function."""
    print("🔧 COMPREHENSIVE POD FIXES")
    print("=" * 50)
    print(f"Started at: {datetime.now()}")
    
    try:
        # Diagnose issues
        pending_pods = diagnose_pending_pods()
        print(f"\nFound {len(pending_pods)} pending pods")
        
        # Apply fixes
        fix_resource_issues()
        fix_image_issues()
        fix_storage_issues()
        restart_failed_deployments()
        
        # Verify fixes
        success = verify_fixes()
        
        if success:
            print("\n🎉 POD FIXES SUCCESSFUL!")
            print("✅ Some pods are now running")
            return 0
        else:
            print("\n⚠️ POD FIXES PARTIALLY SUCCESSFUL")
            print("❌ Some issues may remain")
            return 1
            
    except Exception as e:
        print(f"❌ Fix script error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
