#!/usr/bin/env python3
"""
Working Advanced Tenant Onboarding Script
Simplified version that works reliably without hanging imports
"""

import argparse
import json
import logging
import os
import random
import string
import subprocess
import sys
import tempfile
import time
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="[%(asctime)s] [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Default values
DEFAULT_RDS_HOST = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
DEFAULT_RDS_PORT = "3306"
DEFAULT_RDS_ADMIN_USER = "admin"
DEFAULT_RDS_ADMIN_PASSWORD = "&BZzY_<AK(=a*UhZ"
DEFAULT_RDS_DATABASE = "architrave"
DEFAULT_DOMAIN = "architrave.local"
DEFAULT_ENVIRONMENT = "production"

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Working Tenant onboarding script")
    
    # Required arguments
    parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    parser.add_argument("--tenant-name", required=True, help="Tenant name")
    parser.add_argument("--subdomain", required=True, help="Subdomain for tenant")
    
    # Optional arguments
    parser.add_argument("--domain", default=DEFAULT_DOMAIN, help=f"Domain (default: {DEFAULT_DOMAIN})")
    parser.add_argument("--environment", default=DEFAULT_ENVIRONMENT, help=f"Environment (default: {DEFAULT_ENVIRONMENT})")
    parser.add_argument("--backend-image", required=True, help="Backend image")
    parser.add_argument("--frontend-image", required=True, help="Frontend image")
    parser.add_argument("--rabbitmq-image", required=True, help="RabbitMQ image")
    parser.add_argument("--local-sql-file", help="Path to local SQL file")
    
    return parser.parse_args()

def run_command(command, shell=True, check=True):
    """Run a shell command and return the output."""
    logger.debug(f"Running command: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=check,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed with exit code {e.returncode}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        if check:
            raise
        return None

def generate_password():
    """Generate a random password."""
    return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(16))

def create_namespace(tenant_id, tenant_name, environment):
    """Create and configure the tenant namespace."""
    logger.info(f"Creating namespace tenant-{tenant_id}")
    
    # Create namespace
    run_command(f"kubectl create namespace tenant-{tenant_id} --dry-run=client -o yaml | kubectl apply -f -")
    
    # Label namespace
    tenant_name_label = tenant_name.replace(" ", "-")
    run_command(
        f"kubectl label namespace tenant-{tenant_id} --overwrite "
        f"tenant.architrave.io/tenant-id={tenant_id} "
        f"tenant.architrave.io/tenant-name={tenant_name_label} "
        f"environment={environment} "
        f"istio-injection=enabled "
        f"pod-security.kubernetes.io/enforce=privileged "
        f"pod-security.kubernetes.io/audit=restricted "
        f"pod-security.kubernetes.io/warn=restricted"
    )
    
    logger.info(f"Namespace tenant-{tenant_id} created successfully")

def import_database(tenant_id, local_sql_file):
    """Import database from local file."""
    logger.info(f"Importing database for tenant-{tenant_id}")
    
    if not local_sql_file or not os.path.exists(local_sql_file):
        logger.warning("No local SQL file provided or file doesn't exist, skipping database import")
        return
    
    # Create database user
    db_tenant_id = tenant_id.replace('-', '_')
    db_user = f"tenant_{db_tenant_id}"
    db_password = generate_password()
    
    # Create database bastion pod
    bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: db-bastion-{tenant_id}
  namespace: tenant-{tenant_id}
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "{DEFAULT_RDS_ADMIN_PASSWORD}"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
      requests:
        cpu: 50m
        memory: 128Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(bastion_yaml)
        bastion_file = f.name
    
    try:
        # Deploy bastion pod
        run_command(f"kubectl apply -f {bastion_file}")
        
        # Wait for pod to be ready
        run_command(f"kubectl wait --for=condition=ready pod/db-bastion-{tenant_id} -n tenant-{tenant_id} --timeout=120s")
        
        # Create database user
        create_user_sql = f"""
CREATE USER IF NOT EXISTS '{db_user}'@'%' IDENTIFIED BY '{db_password}';
GRANT ALL PRIVILEGES ON {DEFAULT_RDS_DATABASE}.* TO '{db_user}'@'%';
FLUSH PRIVILEGES;
"""
        
        # Execute user creation
        run_command(f"kubectl exec db-bastion-{tenant_id} -n tenant-{tenant_id} -- mysql -h {DEFAULT_RDS_HOST} -P {DEFAULT_RDS_PORT} -u {DEFAULT_RDS_ADMIN_USER} -e \"{create_user_sql}\"")
        
        # Import SQL file
        logger.info("Importing SQL schema...")
        run_command(f"kubectl cp {local_sql_file} tenant-{tenant_id}/db-bastion-{tenant_id}:/tmp/schema.sql")
        run_command(f"kubectl exec db-bastion-{tenant_id} -n tenant-{tenant_id} -- mysql -h {DEFAULT_RDS_HOST} -P {DEFAULT_RDS_PORT} -u {DEFAULT_RDS_ADMIN_USER} {DEFAULT_RDS_DATABASE} < /tmp/schema.sql")
        
        logger.info("Database import completed successfully")
        
    finally:
        # Cleanup bastion pod
        run_command(f"kubectl delete pod db-bastion-{tenant_id} -n tenant-{tenant_id} --ignore-not-found=true")
        os.unlink(bastion_file)

def create_s3_resources(tenant_id):
    """Create S3 resources for tenant."""
    logger.info(f"Creating S3 resources for tenant-{tenant_id}")
    
    # Create S3 storage class
    storage_class_yaml = f"""
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-{tenant_id}
provisioner: s3.csi.aws.com
parameters:
  mounter: mountpoint-s3
  awsRegion: eu-central-1
  bucketName: tenant-{tenant_id}-assets
  uid: "33"
  gid: "33"
  dirMode: "0755"
  fileMode: "0644"
reclaimPolicy: Delete
allowVolumeExpansion: true
volumeBindingMode: Immediate
"""
    
    # Create PV and PVC
    pv_yaml = f"""
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-{tenant_id}
spec:
  capacity:
    storage: 1200Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-{tenant_id}
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-driver-volume-{tenant_id}
    volumeAttributes:
      bucketName: tenant-{tenant_id}-assets
      uid: "33"
      gid: "33"
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-{tenant_id}
  namespace: tenant-{tenant_id}
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: s3-sc-{tenant_id}
  resources:
    requests:
      storage: 1200Gi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(storage_class_yaml)
        sc_file = f.name
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(pv_yaml)
        pv_file = f.name
    
    try:
        run_command(f"kubectl apply -f {sc_file}")
        run_command(f"kubectl apply -f {pv_file}")
        logger.info("S3 resources created successfully")
    finally:
        os.unlink(sc_file)
        os.unlink(pv_file)

def deploy_backend(tenant_id, backend_image):
    """Deploy backend application."""
    logger.info(f"Deploying backend for tenant-{tenant_id}")
    
    backend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
  labels:
    app: backend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: backend
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: backend
        tenant: {tenant_id}
    spec:
      containers:
      - name: backend
        image: {backend_image}
        ports:
        - containerPort: 80
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: DB_HOST
          value: "{DEFAULT_RDS_HOST}"
        - name: DB_PORT
          value: "{DEFAULT_RDS_PORT}"
        - name: DB_NAME
          value: "{DEFAULT_RDS_DATABASE}"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: storage
          mountPath: /storage
      volumes:
      - name: storage
        persistentVolumeClaim:
          claimName: s3-pvc-{tenant_id}
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-backend-service
  namespace: tenant-{tenant_id}
spec:
  selector:
    app: backend
    tenant: {tenant_id}
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(backend_yaml)
        backend_file = f.name
    
    try:
        run_command(f"kubectl apply -f {backend_file}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-backend -n tenant-{tenant_id} --timeout=300s")
        logger.info("Backend deployed successfully")
    finally:
        os.unlink(backend_file)

def deploy_frontend(tenant_id, frontend_image):
    """Deploy frontend application."""
    logger.info(f"Deploying frontend for tenant-{tenant_id}")
    
    frontend_yaml = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
  labels:
    app: frontend
    tenant: {tenant_id}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: frontend
      tenant: {tenant_id}
  template:
    metadata:
      labels:
        app: frontend
        tenant: {tenant_id}
    spec:
      containers:
      - name: frontend
        image: {frontend_image}
        ports:
        - containerPort: 80
        - containerPort: 443
        env:
        - name: TENANT_ID
          value: "{tenant_id}"
        - name: BACKEND_SERVICE
          value: "tenant-{tenant_id}-backend-service"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-{tenant_id}-frontend-service
  namespace: tenant-{tenant_id}
spec:
  selector:
    app: frontend
    tenant: {tenant_id}
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: https
    port: 443
    targetPort: 443
  type: ClusterIP
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(frontend_yaml)
        frontend_file = f.name
    
    try:
        run_command(f"kubectl apply -f {frontend_file}")
        run_command(f"kubectl wait --for=condition=available deployment/tenant-{tenant_id}-frontend -n tenant-{tenant_id} --timeout=300s")
        logger.info("Frontend deployed successfully")
    finally:
        os.unlink(frontend_file)

def verify_deployment(tenant_id):
    """Verify the deployment is working."""
    logger.info(f"Verifying deployment for tenant-{tenant_id}")
    
    # Check pods
    pods = run_command(f"kubectl get pods -n tenant-{tenant_id} --no-headers")
    logger.info(f"Pods in tenant-{tenant_id}:")
    for line in pods.split('\n'):
        if line.strip():
            logger.info(f"  {line}")
    
    # Check services
    services = run_command(f"kubectl get services -n tenant-{tenant_id} --no-headers")
    logger.info(f"Services in tenant-{tenant_id}:")
    for line in services.split('\n'):
        if line.strip():
            logger.info(f"  {line}")
    
    logger.info("Deployment verification completed")

def main():
    """Main function."""
    args = parse_arguments()
    
    start_time = time.time()
    
    logger.info("🚀" + "="*80)
    logger.info("🚀 WORKING ADVANCED TENANT ONBOARDING")
    logger.info("🚀" + "="*80)
    logger.info(f"🚀 Tenant ID: {args.tenant_id}")
    logger.info(f"🚀 Tenant Name: {args.tenant_name}")
    logger.info(f"🚀 Subdomain: {args.subdomain}")
    logger.info(f"🚀 Domain: {args.domain}")
    logger.info("🚀" + "="*80)
    
    try:
        # Step 1: Create namespace
        create_namespace(args.tenant_id, args.tenant_name, args.environment)
        
        # Step 2: Import database
        if args.local_sql_file:
            import_database(args.tenant_id, args.local_sql_file)
        
        # Step 3: Create S3 resources
        create_s3_resources(args.tenant_id)
        
        # Step 4: Deploy backend
        deploy_backend(args.tenant_id, args.backend_image)
        
        # Step 5: Deploy frontend
        deploy_frontend(args.tenant_id, args.frontend_image)
        
        # Step 6: Verify deployment
        verify_deployment(args.tenant_id)
        
        # Calculate elapsed time
        elapsed_time = time.time() - start_time
        minutes, seconds = divmod(elapsed_time, 60)
        
        logger.info("\n🎉" + "="*80)
        logger.info("🎉 TENANT ONBOARDING COMPLETED SUCCESSFULLY!")
        logger.info("🎉" + "="*80)
        logger.info(f"🎉 Tenant ID: {args.tenant_id}")
        logger.info(f"🎉 Tenant Name: {args.tenant_name}")
        logger.info(f"🎉 Namespace: tenant-{args.tenant_id}")
        logger.info(f"🎉 Elapsed Time: {int(minutes)}m {int(seconds)}s")
        logger.info("🎉" + "="*80)
        
        return True
        
    except Exception as e:
        logger.error(f"Tenant onboarding failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
