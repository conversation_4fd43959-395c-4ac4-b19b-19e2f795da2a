#!/bin/bash
# Immediate Fixes Script
# Addresses common issues identified in tenant management system

echo "🔧 IMMEDIATE FIXES FOR TENANT MANAGEMENT SYSTEM"
echo "==============================================="
echo "Started at: $(date)"
echo ""

# Fix 1: Test kubectl connectivity with timeout
echo "🔧 Fix 1: Testing kubectl connectivity..."
timeout 10 kubectl version --client
if [ $? -eq 0 ]; then
    echo "✅ kubectl client working"
else
    echo "❌ kubectl client issues"
fi

timeout 10 kubectl cluster-info
if [ $? -eq 0 ]; then
    echo "✅ kubectl cluster connectivity working"
else
    echo "❌ kubectl cluster connectivity issues"
fi

# Fix 2: Quick tenant status check
echo ""
echo "🔧 Fix 2: Quick tenant status check..."
TENANT_COUNT=$(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | wc -l)
echo "Found $TENANT_COUNT tenant namespaces"

if [ $TENANT_COUNT -gt 0 ]; then
    echo "📋 Tenant namespaces:"
    timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print "  - " $1}'
    
    # Check first tenant in detail
    FIRST_TENANT=$(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | head -1 | awk '{print $1}')
    if [ ! -z "$FIRST_TENANT" ]; then
        echo ""
        echo "🔍 Checking first tenant: $FIRST_TENANT"
        
        # Check pods
        echo "Pods in $FIRST_TENANT:"
        timeout 10 kubectl get pods -n $FIRST_TENANT --no-headers 2>/dev/null | while read line; do
            if [ ! -z "$line" ]; then
                POD_NAME=$(echo $line | awk '{print $1}')
                POD_STATUS=$(echo $line | awk '{print $3}')
                echo "  - $POD_NAME: $POD_STATUS"
            fi
        done
        
        # Check services
        echo "Services in $FIRST_TENANT:"
        timeout 10 kubectl get services -n $FIRST_TENANT --no-headers 2>/dev/null | while read line; do
            if [ ! -z "$line" ]; then
                SVC_NAME=$(echo $line | awk '{print $1}')
                SVC_TYPE=$(echo $line | awk '{print $2}')
                echo "  - $SVC_NAME: $SVC_TYPE"
            fi
        done
    fi
else
    echo "⚠️ No tenant namespaces found"
fi

# Fix 3: Database connectivity test
echo ""
echo "🔧 Fix 3: Database connectivity test..."

# Create simple database test pod
cat <<EOF | timeout 30 kubectl apply -f - 2>/dev/null
apiVersion: v1
kind: Pod
metadata:
  name: db-quick-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "120"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
EOF

if [ $? -eq 0 ]; then
    echo "✅ Database test pod created"
    
    # Wait for pod to be ready
    echo "⏳ Waiting for pod to be ready..."
    timeout 60 kubectl wait --for=condition=ready pod/db-quick-test 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ Database test pod ready"
        
        # Test database connection
        echo "🔍 Testing database connection..."
        DB_HOST="production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        timeout 15 kubectl exec db-quick-test -- mysql -h $DB_HOST -P 3306 -u admin -e "SELECT 1 as test;" 2>/dev/null
        
        if [ $? -eq 0 ]; then
            echo "✅ Database connection successful"
            
            # Test database schema
            echo "🔍 Testing database schema..."
            timeout 15 kubectl exec db-quick-test -- mysql -h $DB_HOST -P 3306 -u admin architrave -e "SHOW TABLES;" 2>/dev/null | wc -l
            TABLE_COUNT=$(timeout 15 kubectl exec db-quick-test -- mysql -h $DB_HOST -P 3306 -u admin architrave -e "SHOW TABLES;" 2>/dev/null | wc -l)
            
            if [ $TABLE_COUNT -gt 1 ]; then
                echo "✅ Database schema found ($TABLE_COUNT tables)"
            else
                echo "⚠️ Database schema may be missing"
            fi
        else
            echo "❌ Database connection failed"
        fi
    else
        echo "❌ Database test pod not ready"
    fi
    
    # Cleanup
    timeout 10 kubectl delete pod db-quick-test --ignore-not-found=true 2>/dev/null
else
    echo "❌ Failed to create database test pod"
fi

# Fix 4: Check for failed/pending pods and restart them
echo ""
echo "🔧 Fix 4: Checking for failed/pending pods..."

for ns in $(timeout 10 kubectl get namespaces --no-headers 2>/dev/null | grep tenant- | awk '{print $1}'); do
    echo "Checking namespace: $ns"
    
    # Check for failed pods
    FAILED_PODS=$(timeout 10 kubectl get pods -n $ns --field-selector=status.phase=Failed --no-headers 2>/dev/null | wc -l)
    if [ $FAILED_PODS -gt 0 ]; then
        echo "  Found $FAILED_PODS failed pods, deleting..."
        timeout 30 kubectl delete pods --field-selector=status.phase=Failed -n $ns 2>/dev/null
    fi
    
    # Check for pending pods
    PENDING_PODS=$(timeout 10 kubectl get pods -n $ns --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
    if [ $PENDING_PODS -gt 0 ]; then
        echo "  Found $PENDING_PODS pending pods"
        # Don't automatically delete pending pods as they might be starting up
    fi
    
    # Check for running pods
    RUNNING_PODS=$(timeout 10 kubectl get pods -n $ns --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    echo "  Running pods: $RUNNING_PODS"
done

# Fix 5: Check mass operations status
echo ""
echo "🔧 Fix 5: Checking mass operations status..."
echo "Mass operations may still be running in the background."
echo "This could explain why some kubectl commands are slow."

# Fix 6: Generate health check summary
echo ""
echo "🔧 Fix 6: Health check summary..."
echo "📊 SYSTEM STATUS SUMMARY:"
echo "  - kubectl connectivity: $(timeout 5 kubectl version --client >/dev/null 2>&1 && echo "✅ Working" || echo "❌ Issues")"
echo "  - Cluster connectivity: $(timeout 5 kubectl cluster-info >/dev/null 2>&1 && echo "✅ Working" || echo "❌ Issues")"
echo "  - Tenant namespaces: $TENANT_COUNT found"
echo "  - Database connectivity: $(echo "Will be tested by comprehensive health check")"

echo ""
echo "🎯 RECOMMENDATIONS:"
echo "1. Wait for mass operations to complete"
echo "2. Run comprehensive health check: python3 comprehensive_health_check.py"
echo "3. Check individual tenant health if needed"
echo "4. Monitor pod status and restart failed pods"
echo "5. Verify database schema is properly imported"

echo ""
echo "✅ Immediate fixes completed at: $(date)"
echo "🔍 For detailed analysis, run: python3 comprehensive_diagnostic.py"
echo "🔍 For health checks, run: python3 comprehensive_health_check.py"
