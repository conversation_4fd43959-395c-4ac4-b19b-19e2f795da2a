#!/usr/bin/env python3
"""
Enhanced Security Module for Tenant Onboarding
Implements enterprise-grade security measures for multi-tenant isolation
"""

import tempfile
import os
import logging

logger = logging.getLogger(__name__)

def setup_enhanced_security_policies(tenant_id, run_command):
    """Set up enhanced security policies including Pod Security Standards and strict isolation."""
    logger.info(f"🛡️ Setting up enhanced security policies for tenant-{tenant_id}")

    # 1. Pod Security Standards and Enhanced Network Policies
    enhanced_security_yaml = f"""
# Pod Security Standards
apiVersion: v1
kind: Namespace
metadata:
  name: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Enhanced Resource Limits
apiVersion: v1
kind: LimitRange
metadata:
  name: tenant-{tenant_id}-enhanced-limits
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
      ephemeral-storage: "100Mi"
    max:
      cpu: "2"
      memory: "4Gi"
      ephemeral-storage: "10Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
      ephemeral-storage: "50Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Pod
---
# Strict Network Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-strict-isolation
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  podSelector: {{}}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: {tenant_id}
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: {tenant_id}
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
---
# Cross-Tenant Denial Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-{tenant_id}-deny-cross-tenant
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  podSelector: {{}}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["{tenant_id}"]
  egress:
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["{tenant_id}"]
---
# Pod Disruption Budgets
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-{tenant_id}-backend-pdb
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-{tenant_id}-backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-{tenant_id}-frontend-pdb
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-{tenant_id}-frontend
"""

    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(enhanced_security_yaml)
            temp_file = f.name

        run_command(f"kubectl apply -f {temp_file}")
        os.unlink(temp_file)
        logger.info(f"✅ Enhanced security policies created for tenant-{tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to create enhanced security policies: {e}")

def setup_istio_security_policies(tenant_id, run_command):
    """Set up Istio security policies with strict mTLS and authorization."""
    logger.info(f"🔐 Setting up Istio security policies for tenant-{tenant_id}")

    istio_security_yaml = f"""
# Strict mTLS
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-{tenant_id}-strict-mtls
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  mtls:
    mode: STRICT
---
# Deny All Authorization Policy
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-deny-all
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  action: DENY
  rules:
  - from:
    - source:
        notNamespaces: ["tenant-{tenant_id}", "istio-system"]
---
# Allow Frontend Access
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-allow-frontend
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: tenant-{tenant_id}-frontend
  rules:
  - from:
    - source:
        namespaces: ["istio-system"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
---
# Allow Backend Access
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-{tenant_id}-allow-backend
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: tenant-{tenant_id}-backend
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-{tenant_id}/sa/default"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        paths: ["/health*", "/api/*"]
"""

    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(istio_security_yaml)
            temp_file = f.name

        run_command(f"kubectl apply -f {temp_file}")
        os.unlink(temp_file)
        logger.info(f"✅ Istio security policies created for tenant-{tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to create Istio security policies: {e}")

def setup_database_multi_tenancy(tenant_id, run_command):
    """Set up database multi-tenancy with tenant_id columns."""
    logger.info(f"🗄️ Setting up database multi-tenancy for tenant-{tenant_id}")

    # Database schema updates for multi-tenancy
    db_script = f"""
# Add tenant_id columns to major tables
ALTER TABLE users ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT '{tenant_id}';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT '{tenant_id}';
ALTER TABLE folders ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT '{tenant_id}';
ALTER TABLE assets ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT '{tenant_id}';

# Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX IF NOT EXISTS idx_folders_tenant_id ON folders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_assets_tenant_id ON assets(tenant_id);

# Create tenant configuration table
CREATE TABLE IF NOT EXISTS tenant_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL UNIQUE,
    tenant_name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) NOT NULL UNIQUE,
    domain VARCHAR(255) NOT NULL DEFAULT 'architrave.com',
    s3_bucket VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    settings JSON,
    INDEX idx_tenant_config_tenant_id (tenant_id),
    INDEX idx_tenant_config_subdomain (subdomain),
    INDEX idx_tenant_config_status (status)
);

# Insert tenant configuration
INSERT INTO tenant_config (tenant_id, tenant_name, subdomain, s3_bucket)
VALUES ('{tenant_id}', '{tenant_id.title()} Company', '{tenant_id}', 'tenant-{tenant_id}-assets')
ON DUPLICATE KEY UPDATE
    tenant_name = VALUES(tenant_name),
    subdomain = VALUES(subdomain),
    s3_bucket = VALUES(s3_bucket),
    updated_at = CURRENT_TIMESTAMP;
"""

    try:
        # Execute database schema updates
        run_command(
            f'kubectl exec -n tenant-{tenant_id} deployment/tenant-{tenant_id}-backend -c backend -- '
            f'timeout 30 mysql -h production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com '
            f'-P 3306 -u admin -p\'&BZzY_<AK(=a*UhZ\' --ssl architrave -e "{db_script}"',
            check=False
        )
        logger.info(f"✅ Database multi-tenancy configured for tenant-{tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to configure database multi-tenancy: {e}")

def apply_security_contexts(tenant_id, run_command):
    """Apply security contexts to all deployments for Pod Security Standards compliance."""
    logger.info(f"🔒 Applying security contexts for tenant-{tenant_id}")

    # Backend security context patch
    backend_security_patch = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-backend
  namespace: tenant-{tenant_id}
spec:
  template:
    spec:
      securityContext:
        runAsUser: 33
        runAsGroup: 33
        runAsNonRoot: true
        fsGroup: 33
        seccompProfile:
          type: RuntimeDefault
      initContainers:
      - name: install-php-extensions
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
      - name: init-schema
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
      - name: init-php-config
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
      containers:
      - name: backend
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
      - name: nginx
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
"""

    # Frontend security context patch
    frontend_security_patch = f"""
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-{tenant_id}-frontend
  namespace: tenant-{tenant_id}
spec:
  template:
    spec:
      securityContext:
        runAsUser: 101
        runAsGroup: 101
        runAsNonRoot: true
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: frontend
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 101
          runAsGroup: 101
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
"""

    try:
        # Apply backend security context using strategic merge
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(backend_security_patch)
            temp_file = f.name

        run_command(f"kubectl patch deployment tenant-{tenant_id}-backend -n tenant-{tenant_id} --type=strategic --patch-file {temp_file}")
        os.unlink(temp_file)

        # Apply frontend security context using strategic merge
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(frontend_security_patch)
            temp_file = f.name

        run_command(f"kubectl patch deployment tenant-{tenant_id}-frontend -n tenant-{tenant_id} --type=strategic --patch-file {temp_file}")
        os.unlink(temp_file)

        logger.info(f"✅ Security contexts applied for tenant-{tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to apply security contexts: {e}")

def setup_s3_security(tenant_id, bucket_name, run_command):
    """Set up S3 security with proper CSI configuration."""
    logger.info(f"🪣 Setting up S3 security for tenant-{tenant_id}")

    # Enhanced S3 CSI configuration with proper permissions
    s3_security_yaml = f"""
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-{tenant_id}-secure
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
provisioner: s3.csi.aws.com
parameters:
  mounter: mountpoint-s3
  bucketName: {bucket_name}
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-{tenant_id}-secure
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  capacity:
    storage: 1200Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-{tenant_id}-secure
  mountOptions:
    - uid=33
    - gid=33
    - allow-other
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-driver-volume-{tenant_id}-secure
    volumeAttributes:
      bucketName: {bucket_name}
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-{tenant_id}-secure
  namespace: tenant-{tenant_id}
  labels:
    tenant.architrave.io/tenant-id: {tenant_id}
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: s3-sc-{tenant_id}-secure
  resources:
    requests:
      storage: 1200Gi
"""

    try:
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(s3_security_yaml)
            temp_file = f.name

        run_command(f"kubectl apply -f {temp_file}")
        os.unlink(temp_file)
        logger.info(f"✅ S3 security configuration created for tenant-{tenant_id}")
    except Exception as e:
        logger.warning(f"Failed to create S3 security configuration: {e}")
