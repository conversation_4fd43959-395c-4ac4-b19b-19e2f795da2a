#!/bin/bash

# Security Tools Installation Script
# Installs all required security tools for comprehensive tenant security

set -e

echo "🛡️ INSTALLING COMPREHENSIVE SECURITY TOOLS"
echo "==========================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install kubeseal
install_kubeseal() {
    echo "📦 Installing Kubeseal (Sealed Secrets)..."
    
    # Install kubeseal CLI
    if ! command_exists kubeseal; then
        echo "Installing kubeseal CLI..."
        KUBESEAL_VERSION='0.24.0'
        curl -OL "https://github.com/bitnami-labs/sealed-secrets/releases/download/v${KUBESEAL_VERSION}/kubeseal-${KUBESEAL_VERSION}-darwin-amd64.tar.gz"
        tar -xvf kubeseal-${KUBESEAL_VERSION}-darwin-amd64.tar.gz kubeseal
        sudo install -m 755 kubeseal /usr/local/bin/kubeseal
        rm kubeseal kubeseal-${KUBESEAL_VERSION}-darwin-amd64.tar.gz
        echo "✅ Kubeseal CLI installed"
    else
        echo "✅ Kubeseal CLI already installed"
    fi
    
    # Install sealed-secrets controller
    echo "Installing sealed-secrets controller..."
    kubectl apply -f https://github.com/bitnami-labs/sealed-secrets/releases/download/v0.24.0/controller.yaml
    
    # Wait for controller to be ready
    echo "Waiting for sealed-secrets controller to be ready..."
    kubectl wait --for=condition=available deployment/sealed-secrets-controller -n kube-system --timeout=300s
    
    echo "✅ Kubeseal (Sealed Secrets) installed successfully"
}

# Function to install Trivy
install_trivy() {
    echo "📦 Installing Trivy (Vulnerability Scanner)..."
    
    if ! command_exists trivy; then
        echo "Installing Trivy..."
        curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
        echo "✅ Trivy installed"
    else
        echo "✅ Trivy already installed"
    fi
    
    # Test trivy
    trivy --version
    echo "✅ Trivy (Vulnerability Scanner) installed successfully"
}

# Function to install Cosign
install_cosign() {
    echo "📦 Installing Cosign (Container Signing)..."
    
    if ! command_exists cosign; then
        echo "Installing Cosign..."
        go install github.com/sigstore/cosign/v2/cmd/cosign@latest
        
        # Add Go bin to PATH if not already there
        if [[ ":$PATH:" != *":$HOME/go/bin:"* ]]; then
            echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.bashrc
            export PATH=$PATH:$HOME/go/bin
        fi
        echo "✅ Cosign installed"
    else
        echo "✅ Cosign already installed"
    fi
    
    # Test cosign
    cosign version
    echo "✅ Cosign (Container Signing) installed successfully"
}

# Function to install Falco
install_falco() {
    echo "📦 Installing Falco (Runtime Security)..."
    
    # Add Falco Helm repository
    helm repo add falcosecurity https://falcosecurity.github.io/charts
    helm repo update
    
    # Install Falco
    helm upgrade --install falco falcosecurity/falco \
        --namespace falco-system \
        --create-namespace \
        --set falco.grpc.enabled=true \
        --set falco.grpcOutput.enabled=true \
        --set falco.httpOutput.enabled=true \
        --set falco.jsonOutput=true \
        --set falco.logLevel=info
    
    # Wait for Falco to be ready
    echo "Waiting for Falco to be ready..."
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=falco -n falco-system --timeout=300s
    
    echo "✅ Falco (Runtime Security) installed successfully"
}

# Function to install OPA Gatekeeper
install_opa_gatekeeper() {
    echo "📦 Installing OPA Gatekeeper (Policy Engine)..."
    
    # Install Gatekeeper
    kubectl apply -f https://raw.githubusercontent.com/open-policy-agent/gatekeeper/release-3.14/deploy/gatekeeper.yaml
    
    # Wait for Gatekeeper to be ready
    echo "Waiting for OPA Gatekeeper to be ready..."
    kubectl wait --for=condition=available deployment/gatekeeper-controller-manager -n gatekeeper-system --timeout=300s
    kubectl wait --for=condition=available deployment/gatekeeper-audit -n gatekeeper-system --timeout=300s
    
    echo "✅ OPA Gatekeeper (Policy Engine) installed successfully"
}

# Function to install Prometheus and Grafana for monitoring
install_monitoring() {
    echo "📦 Installing Monitoring Stack (Prometheus + Grafana)..."
    
    # Add Prometheus Helm repository
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo update
    
    # Install Prometheus
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace \
        --set prometheus.prometheusSpec.retention=30d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set grafana.adminPassword=admin123 \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.size=10Gi
    
    # Wait for monitoring stack to be ready
    echo "Waiting for monitoring stack to be ready..."
    kubectl wait --for=condition=available deployment/prometheus-kube-prometheus-prometheus-operator -n monitoring --timeout=300s
    kubectl wait --for=condition=available deployment/prometheus-grafana -n monitoring --timeout=300s
    
    echo "✅ Monitoring Stack (Prometheus + Grafana) installed successfully"
}

# Function to create security policies
create_security_policies() {
    echo "📦 Creating Security Policies..."
    
    # Create Pod Security Policy
    cat <<EOF | kubectl apply -f -
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: tenant-restricted
  labels:
    security.architrave.io/policy: "restricted"
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
  readOnlyRootFilesystem: true
EOF
    
    # Create Network Policy Template
    cat <<EOF | kubectl apply -f -
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
EOF
    
    echo "✅ Security Policies created successfully"
}

# Main installation function
main() {
    echo "🚀 Starting comprehensive security tools installation..."
    echo "This will install:"
    echo "  - Kubeseal (Sealed Secrets)"
    echo "  - Trivy (Vulnerability Scanner)"
    echo "  - Cosign (Container Signing)"
    echo "  - Falco (Runtime Security)"
    echo "  - OPA Gatekeeper (Policy Engine)"
    echo "  - Monitoring Stack (Prometheus + Grafana)"
    echo "  - Security Policies"
    echo ""
    
    # Check prerequisites
    if ! command_exists kubectl; then
        echo "❌ kubectl is required but not installed"
        exit 1
    fi
    
    if ! command_exists helm; then
        echo "❌ helm is required but not installed"
        exit 1
    fi
    
    if ! command_exists go; then
        echo "❌ go is required for cosign but not installed"
        exit 1
    fi
    
    echo "✅ Prerequisites check passed"
    echo ""
    
    # Install all security tools
    install_kubeseal
    echo ""
    
    install_trivy
    echo ""
    
    install_cosign
    echo ""
    
    install_falco
    echo ""
    
    install_opa_gatekeeper
    echo ""
    
    install_monitoring
    echo ""
    
    create_security_policies
    echo ""
    
    echo "🎉 COMPREHENSIVE SECURITY TOOLS INSTALLATION COMPLETED!"
    echo "======================================================"
    echo "✅ Kubeseal (Sealed Secrets) - Ready"
    echo "✅ Trivy (Vulnerability Scanner) - Ready"
    echo "✅ Cosign (Container Signing) - Ready"
    echo "✅ Falco (Runtime Security) - Ready"
    echo "✅ OPA Gatekeeper (Policy Engine) - Ready"
    echo "✅ Monitoring Stack (Prometheus + Grafana) - Ready"
    echo "✅ Security Policies - Applied"
    echo ""
    echo "🔧 Next Steps:"
    echo "1. Configure Falco rules for tenant monitoring"
    echo "2. Set up Gatekeeper policies for tenant restrictions"
    echo "3. Configure Grafana dashboards for security metrics"
    echo "4. Test security tools with tenant onboarding"
    echo ""
    echo "📊 Access Grafana: kubectl port-forward svc/prometheus-grafana 3000:80 -n monitoring"
    echo "🔑 Grafana credentials: admin / admin123"
}

# Run main function
main
