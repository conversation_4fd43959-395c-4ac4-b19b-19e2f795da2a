---
# Comprehensive Tenant Management UI
apiVersion: v1
kind: Namespace
metadata:
  name: tenant-management-ui
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Tenant Management Backend API
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-management-api
  namespace: tenant-management-ui
  labels:
    app: tenant-management-api
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-management-api
  template:
    metadata:
      labels:
        app: tenant-management-api
    spec:
      serviceAccountName: tenant-management-api
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: api
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask flask-cors kubernetes boto3 mysql-connector-python prometheus_client
          cat > /app/tenant_api.py << 'EOF'
          #!/usr/bin/env python3
          import os
          import json
          import time
          import subprocess
          from datetime import datetime, timedelta
          from flask import Flask, request, jsonify
          from flask_cors import CORS
          from kubernetes import client, config
          import boto3
          import mysql.connector
          from prometheus_client import Counter, Histogram, start_http_server

          app = Flask(__name__)
          CORS(app)

          # Prometheus metrics
          api_requests = Counter('tenant_api_requests_total', 'Total API requests', ['endpoint', 'method'])
          api_duration = Histogram('tenant_api_request_duration_seconds', 'API request duration', ['endpoint'])

          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()

          k8s_client = client.CoreV1Api()
          apps_client = client.AppsV1Api()

          @app.route('/api/tenants', methods=['GET'])
          def list_tenants():
              api_requests.labels(endpoint='list_tenants', method='GET').inc()
              with api_duration.labels(endpoint='list_tenants').time():
                  try:
                      namespaces = k8s_client.list_namespace()
                      tenants = []

                      for ns in namespaces.items:
                          if ns.metadata.name.startswith('tenant-'):
                              tenant_id = ns.metadata.name.replace('tenant-', '')
                              tenant_info = get_tenant_info(tenant_id)
                              tenants.append(tenant_info)

                      return jsonify({'tenants': tenants, 'count': len(tenants)})

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants/<tenant_id>', methods=['GET'])
          def get_tenant(tenant_id):
              api_requests.labels(endpoint='get_tenant', method='GET').inc()
              with api_duration.labels(endpoint='get_tenant').time():
                  try:
                      tenant_info = get_tenant_info(tenant_id)
                      if tenant_info:
                          return jsonify(tenant_info)
                      else:
                          return jsonify({'error': 'Tenant not found'}), 404

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants', methods=['POST'])
          def create_tenant():
              api_requests.labels(endpoint='create_tenant', method='POST').inc()
              with api_duration.labels(endpoint='create_tenant').time():
                  try:
                      data = request.get_json()
                      tenant_id = data.get('tenant_id')
                      tenant_name = data.get('tenant_name')
                      subdomain = data.get('subdomain', tenant_id)
                      domain = data.get('domain', 'architrave.com')

                      if not tenant_id or not tenant_name:
                          return jsonify({'error': 'tenant_id and tenant_name required'}), 400

                      # Call onboarding script
                      result = subprocess.run([
                          'python3', '/scripts/advanced_tenant_onboard.py',
                          '--tenant-id', tenant_id,
                          '--tenant-name', tenant_name,
                          '--subdomain', subdomain,
                          '--domain', domain
                      ], capture_output=True, text=True)

                      if result.returncode == 0:
                          return jsonify({
                              'status': 'success',
                              'tenant_id': tenant_id,
                              'message': 'Tenant created successfully'
                          })
                      else:
                          return jsonify({
                              'status': 'error',
                              'error': result.stderr
                          }), 500

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants/<tenant_id>', methods=['DELETE'])
          def delete_tenant(tenant_id):
              api_requests.labels(endpoint='delete_tenant', method='DELETE').inc()
              with api_duration.labels(endpoint='delete_tenant').time():
                  try:
                      # Call offboarding script
                      result = subprocess.run([
                          'python3', '/scripts/advanced_tenant_offboard.py',
                          '--tenant-id', tenant_id,
                          '--force'
                      ], capture_output=True, text=True)

                      if result.returncode == 0:
                          return jsonify({
                              'status': 'success',
                              'tenant_id': tenant_id,
                              'message': 'Tenant deleted successfully'
                          })
                      else:
                          return jsonify({
                              'status': 'error',
                              'error': result.stderr
                          }), 500

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants/<tenant_id>/metrics', methods=['GET'])
          def get_tenant_metrics(tenant_id):
              api_requests.labels(endpoint='get_tenant_metrics', method='GET').inc()
              with api_duration.labels(endpoint='get_tenant_metrics').time():
                  try:
                      # Get pod metrics
                      pods = k8s_client.list_namespaced_pod(namespace=f'tenant-{tenant_id}')

                      metrics = {
                          'tenant_id': tenant_id,
                          'pods': {
                              'total': len(pods.items),
                              'running': len([p for p in pods.items if p.status.phase == 'Running']),
                              'pending': len([p for p in pods.items if p.status.phase == 'Pending']),
                              'failed': len([p for p in pods.items if p.status.phase == 'Failed'])
                          },
                          'deployments': get_deployment_status(tenant_id),
                          'services': get_service_status(tenant_id),
                          'resource_usage': get_resource_usage(tenant_id),
                          'cost_estimate': calculate_cost_estimate(tenant_id)
                      }

                      return jsonify(metrics)

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants/<tenant_id>/health', methods=['GET'])
          def get_tenant_health(tenant_id):
              api_requests.labels(endpoint='get_tenant_health', method='GET').inc()
              with api_duration.labels(endpoint='get_tenant_health').time():
                  try:
                      health_status = {
                          'tenant_id': tenant_id,
                          'overall_health': 'healthy',
                          'components': {
                              'backend': check_component_health(tenant_id, 'backend'),
                              'frontend': check_component_health(tenant_id, 'frontend'),
                              'rabbitmq': check_component_health(tenant_id, 'rabbitmq'),
                              'database': check_database_health(tenant_id),
                              's3': check_s3_health(tenant_id)
                          },
                          'last_check': datetime.utcnow().isoformat()
                      }

                      # Determine overall health
                      unhealthy_components = [k for k, v in health_status['components'].items() if v['status'] != 'healthy']
                      if unhealthy_components:
                          health_status['overall_health'] = 'degraded' if len(unhealthy_components) < 3 else 'unhealthy'

                      return jsonify(health_status)

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          @app.route('/api/tenants/<tenant_id>/costs', methods=['GET'])
          def get_tenant_costs(tenant_id):
              api_requests.labels(endpoint='get_tenant_costs', method='GET').inc()
              with api_duration.labels(endpoint='get_tenant_costs').time():
                  try:
                      # Calculate costs for the last 30 days
                      end_date = datetime.utcnow()
                      start_date = end_date - timedelta(days=30)

                      costs = {
                          'tenant_id': tenant_id,
                          'period': {
                              'start': start_date.isoformat(),
                              'end': end_date.isoformat()
                          },
                          'compute': calculate_compute_costs(tenant_id),
                          'storage': calculate_storage_costs(tenant_id),
                          'network': calculate_network_costs(tenant_id),
                          'database': calculate_database_costs(tenant_id),
                          'total': 0
                      }

                      costs['total'] = sum([
                          costs['compute'],
                          costs['storage'],
                          costs['network'],
                          costs['database']
                      ])

                      return jsonify(costs)

                  except Exception as e:
                      return jsonify({'error': str(e)}), 500

          def get_tenant_info(tenant_id):
              try:
                  namespace = k8s_client.read_namespace(f'tenant-{tenant_id}')

                  # Get tenant metadata from annotations
                  annotations = namespace.metadata.annotations or {}

                  return {
                      'tenant_id': tenant_id,
                      'tenant_name': annotations.get('tenant-name', tenant_id),
                      'subdomain': annotations.get('subdomain', tenant_id),
                      'domain': annotations.get('domain', 'architrave.com'),
                      'created_at': namespace.metadata.creation_timestamp.isoformat(),
                      'status': 'active',
                      'url': f"https://{annotations.get('subdomain', tenant_id)}.{annotations.get('domain', 'architrave.com')}",
                      'dns_records': annotations.get('hetzner-dns/record-ids', '{}')
                  }
              except:
                  return None

          def get_deployment_status(tenant_id):
              try:
                  deployments = apps_client.list_namespaced_deployment(namespace=f'tenant-{tenant_id}')
                  return {
                      'total': len(deployments.items),
                      'ready': len([d for d in deployments.items if d.status.ready_replicas == d.status.replicas]),
                      'details': [
                          {
                              'name': d.metadata.name,
                              'ready_replicas': d.status.ready_replicas or 0,
                              'total_replicas': d.status.replicas or 0,
                              'status': 'ready' if d.status.ready_replicas == d.status.replicas else 'not_ready'
                          }
                          for d in deployments.items
                      ]
                  }
              except:
                  return {'total': 0, 'ready': 0, 'details': []}

          def get_service_status(tenant_id):
              try:
                  services = k8s_client.list_namespaced_service(namespace=f'tenant-{tenant_id}')
                  return {
                      'total': len(services.items),
                      'details': [
                          {
                              'name': s.metadata.name,
                              'type': s.spec.type,
                              'cluster_ip': s.spec.cluster_ip,
                              'ports': [{'port': p.port, 'target_port': p.target_port} for p in s.spec.ports]
                          }
                          for s in services.items
                      ]
                  }
              except:
                  return {'total': 0, 'details': []}

          def get_resource_usage(tenant_id):
              # This would integrate with metrics server in a real implementation
              return {
                  'cpu': {'used': '200m', 'limit': '1000m', 'percentage': 20},
                  'memory': {'used': '512Mi', 'limit': '2Gi', 'percentage': 25},
                  'storage': {'used': '5Gi', 'limit': '20Gi', 'percentage': 25}
              }

          def calculate_cost_estimate(tenant_id):
              # Simplified cost calculation
              return {
                  'daily': 15.50,
                  'monthly': 465.00,
                  'currency': 'USD'
              }

          def check_component_health(tenant_id, component):
              try:
                  deployment = apps_client.read_namespaced_deployment(
                      name=f'tenant-{tenant_id}-{component}',
                      namespace=f'tenant-{tenant_id}'
                  )

                  ready_replicas = deployment.status.ready_replicas or 0
                  total_replicas = deployment.status.replicas or 0

                  return {
                      'status': 'healthy' if ready_replicas == total_replicas else 'unhealthy',
                      'ready_replicas': ready_replicas,
                      'total_replicas': total_replicas,
                      'last_check': datetime.utcnow().isoformat()
                  }
              except:
                  return {
                      'status': 'unknown',
                      'error': 'Component not found',
                      'last_check': datetime.utcnow().isoformat()
                  }

          def check_database_health(tenant_id):
              try:
                  # Test database connection
                  conn = mysql.connector.connect(
                      host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com',
                      port=3306,
                      user='admin',
                      password='&BZzY_<AK(=a*UhZ',
                      database='architrave',
                      connection_timeout=5
                  )
                  conn.close()

                  return {
                      'status': 'healthy',
                      'last_check': datetime.utcnow().isoformat()
                  }
              except:
                  return {
                      'status': 'unhealthy',
                      'error': 'Database connection failed',
                      'last_check': datetime.utcnow().isoformat()
                  }

          def check_s3_health(tenant_id):
              try:
                  s3_client = boto3.client('s3', region_name='eu-central-1')
                  bucket_name = f'tenant-{tenant_id}-assets'
                  s3_client.head_bucket(Bucket=bucket_name)

                  return {
                      'status': 'healthy',
                      'bucket': bucket_name,
                      'last_check': datetime.utcnow().isoformat()
                  }
              except:
                  return {
                      'status': 'unhealthy',
                      'error': 'S3 bucket not accessible',
                      'last_check': datetime.utcnow().isoformat()
                  }

          def calculate_compute_costs(tenant_id):
              # Simplified compute cost calculation
              return 120.50

          def calculate_storage_costs(tenant_id):
              # Simplified storage cost calculation
              return 45.20

          def calculate_network_costs(tenant_id):
              # Simplified network cost calculation
              return 12.30

          def calculate_database_costs(tenant_id):
              # Simplified database cost calculation
              return 287.00

          @app.route('/health', methods=['GET'])
          def health_check():
              return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()})

          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)

              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF

          python /app/tenant_api.py
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        - name: scripts
          mountPath: /scripts
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
      - name: scripts
        configMap:
          name: tenant-scripts
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-management-api
  namespace: tenant-management-ui
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-management-api
rules:
- apiGroups: [""]
  resources: ["namespaces", "pods", "services", "secrets", "configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["security.istio.io"]
  resources: ["peerauthentications", "authorizationpolicies"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-management-api
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-management-api
subjects:
- kind: ServiceAccount
  name: tenant-management-api
  namespace: tenant-management-ui
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-management-api
  namespace: tenant-management-ui
  labels:
    app: tenant-management-api
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: tenant-management-api
---
# Tenant Management Frontend UI
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-management-ui
  namespace: tenant-management-ui
  labels:
    app: tenant-management-ui
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tenant-management-ui
  template:
    metadata:
      labels:
        app: tenant-management-ui
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 101
        runAsGroup: 101
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: ui
        image: nginx:alpine
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/nginx.conf
          subPath: nginx.conf
        - name: ui-content
          mountPath: /usr/share/nginx/html
        - name: tmp
          mountPath: /tmp
        - name: var-cache
          mountPath: /var/cache/nginx
        - name: var-run
          mountPath: /var/run
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: nginx-config
        configMap:
          name: ui-nginx-config
      - name: ui-content
        configMap:
          name: ui-content
      - name: tmp
        emptyDir: {}
      - name: var-cache
        emptyDir: {}
      - name: var-run
        emptyDir: {}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-nginx-config
  namespace: tenant-management-ui
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;

    events {
        worker_connections 1024;
    }

    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;

        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';

        access_log /var/log/nginx/access.log main;

        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;

        gzip on;
        gzip_vary on;
        gzip_min_length 10240;
        gzip_proxied expired no-cache no-store private must-revalidate;
        gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

        server {
            listen 8080;
            server_name _;
            root /usr/share/nginx/html;
            index index.html;

            # Security headers
            add_header X-Frame-Options "SAMEORIGIN" always;
            add_header X-Content-Type-Options "nosniff" always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Referrer-Policy "strict-origin-when-cross-origin" always;
            add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;

            # API proxy
            location /api/ {
                proxy_pass http://tenant-management-api:8080/api/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }

            # Static files
            location / {
                try_files $uri $uri/ /index.html;
                expires 1h;
                add_header Cache-Control "public, immutable";
            }

            # Health check
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
        }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ui-content
  namespace: tenant-management-ui
data:
  index.html: |
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Tenant Management Dashboard</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            .tenant-card {
                transition: transform 0.2s;
            }
            .tenant-card:hover {
                transform: translateY(-5px);
            }
            .status-healthy { color: #28a745; }
            .status-degraded { color: #ffc107; }
            .status-unhealthy { color: #dc3545; }
            .metric-card {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .cost-card {
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                color: white;
            }
        </style>
    </head>
    <body>
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-building"></i> Tenant Management Dashboard
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-user"></i> Admin
                    </span>
                </div>
            </div>
        </nav>

        <div class="container mt-4">
            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card metric-card">
                        <div class="card-body text-center">
                            <h5 class="card-title">Total Tenants</h5>
                            <h2 id="total-tenants">-</h2>
                            <i class="fas fa-building fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">Healthy</h5>
                            <h2 id="healthy-tenants">-</h2>
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <h5 class="card-title">Degraded</h5>
                            <h2 id="degraded-tenants">-</h2>
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card cost-card">
                        <div class="card-body text-center">
                            <h5 class="card-title">Monthly Cost</h5>
                            <h2 id="total-cost">$-</h2>
                            <i class="fas fa-dollar-sign fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="row mb-4">
                <div class="col">
                    <button class="btn btn-primary" onclick="showCreateTenantModal()">
                        <i class="fas fa-plus"></i> Create New Tenant
                    </button>
                    <button class="btn btn-secondary" onclick="refreshDashboard()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Tenants List -->
            <div class="row" id="tenants-container">
                <!-- Tenant cards will be populated here -->
            </div>
        </div>

        <!-- Create Tenant Modal -->
        <div class="modal fade" id="createTenantModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Create New Tenant</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="createTenantForm">
                            <div class="mb-3">
                                <label for="tenantId" class="form-label">Tenant ID</label>
                                <input type="text" class="form-control" id="tenantId" required>
                                <div class="form-text">Lowercase letters, numbers, and hyphens only</div>
                            </div>
                            <div class="mb-3">
                                <label for="tenantName" class="form-label">Tenant Name</label>
                                <input type="text" class="form-control" id="tenantName" required>
                            </div>
                            <div class="mb-3">
                                <label for="subdomain" class="form-label">Subdomain</label>
                                <input type="text" class="form-control" id="subdomain">
                                <div class="form-text">Will default to tenant ID if not specified</div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="createTenant()">Create Tenant</button>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
        <script src="app.js"></script>
    </body>
    </html>

  app.js: |
    // Tenant Management Dashboard JavaScript

    let tenants = [];
    let createTenantModal;

    document.addEventListener('DOMContentLoaded', function() {
        createTenantModal = new bootstrap.Modal(document.getElementById('createTenantModal'));
        loadDashboard();

        // Auto-refresh every 30 seconds
        setInterval(loadDashboard, 30000);
    });

    async function loadDashboard() {
        try {
            const response = await fetch('/api/tenants');
            const data = await response.json();
            tenants = data.tenants;

            updateSummaryCards();
            renderTenants();
        } catch (error) {
            console.error('Error loading dashboard:', error);
            showAlert('Error loading dashboard data', 'danger');
        }
    }

    function updateSummaryCards() {
        const totalTenants = tenants.length;
        const healthyTenants = tenants.filter(t => t.health === 'healthy').length;
        const degradedTenants = tenants.filter(t => t.health === 'degraded').length;
        const totalCost = tenants.reduce((sum, t) => sum + (t.cost || 465), 0);

        document.getElementById('total-tenants').textContent = totalTenants;
        document.getElementById('healthy-tenants').textContent = healthyTenants;
        document.getElementById('degraded-tenants').textContent = degradedTenants;
        document.getElementById('total-cost').textContent = `$${totalCost.toLocaleString()}`;
    }

    function renderTenants() {
        const container = document.getElementById('tenants-container');
        container.innerHTML = '';

        tenants.forEach(tenant => {
            const card = createTenantCard(tenant);
            container.appendChild(card);
        });
    }

    function createTenantCard(tenant) {
        const col = document.createElement('div');
        col.className = 'col-md-6 col-lg-4 mb-4';

        const healthIcon = getHealthIcon(tenant.health || 'unknown');
        const healthClass = `status-${tenant.health || 'unknown'}`;

        col.innerHTML = `
            <div class="card tenant-card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">${tenant.tenant_name || tenant.tenant_id}</h6>
                    <span class="${healthClass}">
                        <i class="${healthIcon}"></i>
                    </span>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        <strong>ID:</strong> ${tenant.tenant_id}<br>
                        <strong>URL:</strong> <a href="${tenant.url}" target="_blank">${tenant.url}</a><br>
                        <strong>Created:</strong> ${new Date(tenant.created_at).toLocaleDateString()}
                    </p>
                    <div class="row text-center">
                        <div class="col-4">
                            <small class="text-muted">CPU</small><br>
                            <span class="badge bg-info">20%</span>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Memory</small><br>
                            <span class="badge bg-warning">25%</span>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">Cost</small><br>
                            <span class="badge bg-success">$465</span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewTenant('${tenant.tenant_id}')">
                            <i class="fas fa-eye"></i> View
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewMetrics('${tenant.tenant_id}')">
                            <i class="fas fa-chart-line"></i> Metrics
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteTenant('${tenant.tenant_id}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;

        return col;
    }

    function getHealthIcon(health) {
        switch (health) {
            case 'healthy': return 'fas fa-check-circle';
            case 'degraded': return 'fas fa-exclamation-triangle';
            case 'unhealthy': return 'fas fa-times-circle';
            default: return 'fas fa-question-circle';
        }
    }

    function showCreateTenantModal() {
        document.getElementById('createTenantForm').reset();
        createTenantModal.show();
    }

    async function createTenant() {
        const tenantId = document.getElementById('tenantId').value;
        const tenantName = document.getElementById('tenantName').value;
        const subdomain = document.getElementById('subdomain').value || tenantId;

        if (!tenantId || !tenantName) {
            showAlert('Please fill in all required fields', 'warning');
            return;
        }

        try {
            const response = await fetch('/api/tenants', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    tenant_id: tenantId,
                    tenant_name: tenantName,
                    subdomain: subdomain
                })
            });

            const result = await response.json();

            if (response.ok) {
                showAlert(`Tenant ${tenantId} created successfully!`, 'success');
                createTenantModal.hide();
                setTimeout(loadDashboard, 2000);
            } else {
                showAlert(`Error creating tenant: ${result.error}`, 'danger');
            }
        } catch (error) {
            showAlert(`Error creating tenant: ${error.message}`, 'danger');
        }
    }

    async function deleteTenant(tenantId) {
        if (!confirm(`Are you sure you want to delete tenant ${tenantId}? This action cannot be undone.`)) {
            return;
        }

        try {
            const response = await fetch(`/api/tenants/${tenantId}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok) {
                showAlert(`Tenant ${tenantId} deleted successfully!`, 'success');
                setTimeout(loadDashboard, 2000);
            } else {
                showAlert(`Error deleting tenant: ${result.error}`, 'danger');
            }
        } catch (error) {
            showAlert(`Error deleting tenant: ${error.message}`, 'danger');
        }
    }

    function viewTenant(tenantId) {
        window.open(`/tenant/${tenantId}`, '_blank');
    }

    function viewMetrics(tenantId) {
        window.open(`/metrics/${tenantId}`, '_blank');
    }

    function refreshDashboard() {
        loadDashboard();
        showAlert('Dashboard refreshed', 'info');
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-management-ui
  namespace: tenant-management-ui
  labels:
    app: tenant-management-ui
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: tenant-management-ui
