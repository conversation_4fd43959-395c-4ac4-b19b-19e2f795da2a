# Enhanced Loki configuration with tenant isolation
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-tenant-isolation-config
  namespace: monitoring
  labels:
    app: loki
    component: config
data:
  loki.yaml: |
    auth_enabled: true
    
    server:
      http_listen_port: 3100
      grpc_listen_port: 9096
      log_level: info
    
    common:
      path_prefix: /loki
      storage:
        filesystem:
          chunks_directory: /loki/chunks
          rules_directory: /loki/rules
      replication_factor: 1
      ring:
        instance_addr: 127.0.0.1
        kvstore:
          store: inmemory
    
    # Tenant isolation configuration
    limits_config:
      # Global limits
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
      max_entries_limit_per_query: 5000
      max_streams_per_user: 10000
      max_line_size: 256000
      
      # Per-tenant limits
      per_tenant_override_config: /etc/loki/overrides.yaml
      
      # Retention policies per tenant
      retention_period: 720h  # 30 days default
      
      # Query limits
      max_query_parallelism: 32
      max_query_series: 500
      max_query_lookback: 0s
      max_concurrent_tail_requests: 10
      
      # Ingestion limits
      ingestion_rate_mb: 4
      ingestion_burst_size_mb: 6
      max_streams_per_user: 0
      max_line_size: 256000
      
      # Split queries by interval
      split_queries_by_interval: 15m
    
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
    
    storage_config:
      boltdb_shipper:
        active_index_directory: /loki/boltdb-shipper-active
        cache_location: /loki/boltdb-shipper-cache
        cache_ttl: 24h
        shared_store: filesystem
      filesystem:
        directory: /loki/chunks
    
    compactor:
      working_directory: /loki/boltdb-shipper-compactor
      shared_store: filesystem
      compaction_interval: 10m
      retention_enabled: true
      retention_delete_delay: 2h
      retention_delete_worker_count: 150
    
    ingester:
      max_transfer_retries: 0
      chunk_idle_period: 1h
      chunk_block_size: 262144
      chunk_target_size: 1572864
      chunk_retain_period: 30s
      max_chunk_age: 1h
      lifecycler:
        ring:
          kvstore:
            store: inmemory
          replication_factor: 1
        final_sleep: 0s
      wal:
        enabled: true
        dir: /loki/wal
    
    query_range:
      results_cache:
        cache:
          embedded_cache:
            enabled: true
            max_size_mb: 100
      cache_results: true
      max_retries: 5
      split_queries_by_interval: 15m
      align_queries_with_step: true
    
    frontend:
      log_queries_longer_than: 5s
      downstream_url: http://loki-querier:3100
      compress_responses: true
    
    query_scheduler:
      max_outstanding_requests_per_tenant: 256
    
    ruler:
      storage:
        type: local
        local:
          directory: /loki/rules
      rule_path: /loki/rules
      alertmanager_url: http://alertmanager:9093
      ring:
        kvstore:
          store: inmemory
      enable_api: true
      enable_alertmanager_v2: true
    
    analytics:
      reporting_enabled: false

---
# Per-tenant override configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-tenant-overrides
  namespace: monitoring
  labels:
    app: loki
    component: overrides
data:
  overrides.yaml: |
    overrides:
      # Basic tier tenants
      "tenant-basic-*":
        ingestion_rate_mb: 2
        ingestion_burst_size_mb: 3
        max_streams_per_user: 1000
        max_entries_limit_per_query: 1000
        retention_period: 168h  # 7 days
        max_query_parallelism: 8
        max_concurrent_tail_requests: 5
      
      # Standard tier tenants
      "tenant-standard-*":
        ingestion_rate_mb: 4
        ingestion_burst_size_mb: 6
        max_streams_per_user: 5000
        max_entries_limit_per_query: 3000
        retention_period: 720h  # 30 days
        max_query_parallelism: 16
        max_concurrent_tail_requests: 10
      
      # Premium tier tenants
      "tenant-premium-*":
        ingestion_rate_mb: 8
        ingestion_burst_size_mb: 12
        max_streams_per_user: 10000
        max_entries_limit_per_query: 5000
        retention_period: 2160h  # 90 days
        max_query_parallelism: 32
        max_concurrent_tail_requests: 20
      
      # Monitoring namespace (unlimited)
      "monitoring":
        ingestion_rate_mb: 16
        ingestion_burst_size_mb: 24
        max_streams_per_user: 50000
        max_entries_limit_per_query: 10000
        retention_period: 8760h  # 1 year
        max_query_parallelism: 64
        max_concurrent_tail_requests: 50

---
# Promtail configuration for tenant isolation
apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-tenant-config
  namespace: monitoring
  labels:
    app: promtail
    component: config
data:
  promtail.yaml: |
    server:
      http_listen_port: 3101
      grpc_listen_port: 0
    
    positions:
      filename: /tmp/positions.yaml
    
    clients:
      - url: http://loki:3100/loki/api/v1/push
        tenant_id: '${TENANT_ID}'
        headers:
          X-Scope-OrgID: '${TENANT_ID}'
    
    scrape_configs:
      # Kubernetes pod logs with tenant isolation
      - job_name: kubernetes-pods
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          # Extract tenant ID from namespace
          - regex:
              expression: '^tenant-(?P<tenant_id>[a-zA-Z0-9-]+)$'
              source: __meta_kubernetes_namespace
          - labels:
              tenant_id: '${tenant_id}'
          # Add tenant context to logs
          - template:
              source: tenant_context
              template: 'tenant={{ .tenant_id }} namespace={{ .__meta_kubernetes_namespace }} pod={{ .__meta_kubernetes_pod_name }}'
          - labels:
              tenant_context: '${tenant_context}'
        relabel_configs:
          # Only scrape tenant namespaces
          - source_labels: [__meta_kubernetes_namespace]
            regex: '^tenant-.*$'
            action: keep
          # Set tenant ID as external label
          - source_labels: [__meta_kubernetes_namespace]
            regex: '^tenant-(.*)$'
            target_label: __tenant_id__
            replacement: '${1}'
          # Standard Kubernetes relabeling
          - source_labels: [__meta_kubernetes_pod_annotation_kubernetes_io_config_source]
            action: drop
            regex: api
          - source_labels: [__meta_kubernetes_pod_node_name]
            target_label: __host__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __meta_kubernetes_pod_name
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: pod
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__
      
      # System logs (monitoring namespace only)
      - job_name: kubernetes-system
        kubernetes_sd_configs:
          - role: pod
        pipeline_stages:
          - labels:
              tenant_id: 'monitoring'
        relabel_configs:
          # Only scrape system namespaces
          - source_labels: [__meta_kubernetes_namespace]
            regex: '^(kube-system|monitoring|istio-system|keda-system)$'
            action: keep
          - source_labels: [__meta_kubernetes_pod_node_name]
            target_label: __host__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - action: replace
            replacement: $1
            separator: /
            source_labels:
              - __meta_kubernetes_namespace
              - __meta_kubernetes_pod_name
            target_label: job
          - action: replace
            source_labels:
              - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_name
            target_label: pod
          - action: replace
            source_labels:
              - __meta_kubernetes_pod_container_name
            target_label: container
          - replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
            target_label: __path__

---
# LogQL queries for tenant-specific dashboards
apiVersion: v1
kind: ConfigMap
metadata:
  name: loki-tenant-queries
  namespace: monitoring
  labels:
    app: loki
    component: queries
data:
  tenant-queries.json: |
    {
      "tenant_error_logs": {
        "query": "{namespace=\"tenant-$tenant_id\"} |= \"ERROR\" | json | line_format \"{{.timestamp}} [{{.level}}] {{.message}}\"",
        "description": "Error logs for specific tenant"
      },
      "tenant_application_logs": {
        "query": "{namespace=\"tenant-$tenant_id\", container=\"app\"} | json | line_format \"{{.timestamp}} {{.message}}\"",
        "description": "Application logs for specific tenant"
      },
      "tenant_nginx_access_logs": {
        "query": "{namespace=\"tenant-$tenant_id\", container=\"nginx\"} | logfmt | line_format \"{{.method}} {{.uri}} {{.status}} {{.request_time}}s\"",
        "description": "Nginx access logs for specific tenant"
      },
      "tenant_php_fpm_logs": {
        "query": "{namespace=\"tenant-$tenant_id\", container=\"php-fpm\"} |= \"pool\" | logfmt",
        "description": "PHP-FPM logs for specific tenant"
      },
      "tenant_database_logs": {
        "query": "{namespace=\"tenant-$tenant_id\"} |= \"mysql\" or |= \"database\" | json",
        "description": "Database-related logs for specific tenant"
      },
      "tenant_performance_logs": {
        "query": "{namespace=\"tenant-$tenant_id\"} |= \"slow\" or |= \"timeout\" or |= \"performance\" | json | line_format \"{{.timestamp}} {{.message}}\"",
        "description": "Performance-related logs for specific tenant"
      },
      "tenant_security_logs": {
        "query": "{namespace=\"tenant-$tenant_id\"} |= \"403\" or |= \"401\" or |= \"security\" or |= \"unauthorized\" | json",
        "description": "Security-related logs for specific tenant"
      },
      "tenant_log_volume": {
        "query": "sum by (namespace) (count_over_time({namespace=~\"tenant-.*\"}[1h]))",
        "description": "Log volume per tenant over time"
      }
    }
