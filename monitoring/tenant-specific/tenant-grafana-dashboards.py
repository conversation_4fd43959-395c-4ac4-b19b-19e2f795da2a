#!/usr/bin/env python3
"""
Tenant-Specific Grafana Dashboard Generator
Creates comprehensive monitoring dashboards for each tenant
"""

import json
import logging
import subprocess
import tempfile
from typing import Dict, Any

logger = logging.getLogger(__name__)

def setup_tenant_grafana_dashboard(tenant_id: str, tenant_name: str) -> bool:
    """Create a comprehensive Grafana dashboard for a specific tenant."""
    logger.info(f"Creating Grafana dashboard for tenant: {tenant_id}")
    
    dashboard_config = create_tenant_dashboard_config(tenant_id, tenant_name)
    
    # Create ConfigMap for the dashboard
    configmap_yaml = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-dashboard
  namespace: monitoring
  labels:
    grafana_dashboard: "1"
    tenant-id: "{tenant_id}"
    app.kubernetes.io/name: grafana-dashboard
    app.kubernetes.io/component: monitoring
data:
  tenant-{tenant_id}-dashboard.json: |
{json.dumps(dashboard_config, indent=4)}
"""
    
    # Apply the ConfigMap
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(configmap_yaml)
        temp_file = f.name
    
    try:
        result = subprocess.run(
            f"kubectl apply -f {temp_file}",
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"Grafana dashboard created for tenant: {tenant_id}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create Grafana dashboard: {e}")
        return False
    finally:
        import os
        os.unlink(temp_file)

def create_tenant_dashboard_config(tenant_id: str, tenant_name: str) -> Dict[str, Any]:
    """Create the Grafana dashboard configuration for a tenant."""
    return {
        "dashboard": {
            "id": None,
            "title": f"Tenant {tenant_name} ({tenant_id}) - Comprehensive Monitoring",
            "tags": ["tenant", tenant_id, "multi-tenant"],
            "timezone": "browser",
            "panels": [
                # Resource Usage Panel
                {
                    "id": 1,
                    "title": "Resource Usage Overview",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0},
                    "targets": [
                        {
                            "expr": f'sum(container_memory_usage_bytes{{namespace="tenant-{tenant_id}"}}) / 1024 / 1024',
                            "legendFormat": "Memory Usage (MB)",
                            "refId": "A"
                        },
                        {
                            "expr": f'sum(rate(container_cpu_usage_seconds_total{{namespace="tenant-{tenant_id}"}}[5m])) * 100',
                            "legendFormat": "CPU Usage (%)",
                            "refId": "B"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "short",
                            "min": 0,
                            "thresholds": {
                                "steps": [
                                    {"color": "green", "value": None},
                                    {"color": "yellow", "value": 70},
                                    {"color": "red", "value": 90}
                                ]
                            }
                        }
                    }
                },
                # Pod Status Panel
                {
                    "id": 2,
                    "title": "Pod Status",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0},
                    "targets": [
                        {
                            "expr": f'count(kube_pod_info{{namespace="tenant-{tenant_id}"}}) by (phase)',
                            "legendFormat": "{{phase}} Pods",
                            "refId": "A"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "short",
                            "mappings": [
                                {"options": {"Running": {"color": "green"}}, "type": "value"},
                                {"options": {"Pending": {"color": "yellow"}}, "type": "value"},
                                {"options": {"Failed": {"color": "red"}}, "type": "value"}
                            ]
                        }
                    }
                },
                # Application Performance Panel
                {
                    "id": 3,
                    "title": "Application Performance",
                    "type": "graph",
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8},
                    "targets": [
                        {
                            "expr": f'rate(http_requests_total{{namespace="tenant-{tenant_id}"}}[5m])',
                            "legendFormat": "Requests/sec - {{pod}}",
                            "refId": "A"
                        },
                        {
                            "expr": f'histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{{namespace="tenant-{tenant_id}"}}[5m]))',
                            "legendFormat": "95th percentile latency - {{pod}}",
                            "refId": "B"
                        }
                    ],
                    "yAxes": [
                        {"label": "Requests/sec", "min": 0},
                        {"label": "Latency (s)", "min": 0}
                    ]
                },
                # Database Performance Panel
                {
                    "id": 4,
                    "title": "Database Performance",
                    "type": "graph",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16},
                    "targets": [
                        {
                            "expr": f'mysql_global_status_queries{{namespace="tenant-{tenant_id}"}}',
                            "legendFormat": "Database Queries",
                            "refId": "A"
                        },
                        {
                            "expr": f'mysql_global_status_slow_queries{{namespace="tenant-{tenant_id}"}}',
                            "legendFormat": "Slow Queries",
                            "refId": "B"
                        }
                    ]
                },
                # S3 Storage Panel
                {
                    "id": 5,
                    "title": "S3 Storage Usage",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16},
                    "targets": [
                        {
                            "expr": f's3_bucket_size_bytes{{bucket="tenant-{tenant_id}-assets"}} / 1024 / 1024 / 1024',
                            "legendFormat": "Storage Used (GB)",
                            "refId": "A"
                        },
                        {
                            "expr": f's3_bucket_objects{{bucket="tenant-{tenant_id}-assets"}}',
                            "legendFormat": "Object Count",
                            "refId": "B"
                        }
                    ]
                },
                # Security Events Panel
                {
                    "id": 6,
                    "title": "Security Events",
                    "type": "graph",
                    "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24},
                    "targets": [
                        {
                            "expr": f'rate(security_events_total{{tenant="{tenant_id}"}}[5m])',
                            "legendFormat": "Security Events/sec - {{rule}}",
                            "refId": "A"
                        }
                    ],
                    "alert": {
                        "conditions": [
                            {
                                "evaluator": {"params": [5], "type": "gt"},
                                "operator": {"type": "and"},
                                "query": {"params": ["A", "5m", "now"]},
                                "reducer": {"params": [], "type": "avg"},
                                "type": "query"
                            }
                        ],
                        "executionErrorState": "alerting",
                        "for": "5m",
                        "frequency": "10s",
                        "handler": 1,
                        "name": f"High Security Events - Tenant {tenant_id}",
                        "noDataState": "no_data",
                        "notifications": []
                    }
                },
                # Network Traffic Panel
                {
                    "id": 7,
                    "title": "Network Traffic",
                    "type": "graph",
                    "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24},
                    "targets": [
                        {
                            "expr": f'rate(container_network_receive_bytes_total{{namespace="tenant-{tenant_id}"}}[5m])',
                            "legendFormat": "Inbound Traffic - {{pod}}",
                            "refId": "A"
                        },
                        {
                            "expr": f'rate(container_network_transmit_bytes_total{{namespace="tenant-{tenant_id}"}}[5m])',
                            "legendFormat": "Outbound Traffic - {{pod}}",
                            "refId": "B"
                        }
                    ]
                },
                # Error Rate Panel
                {
                    "id": 8,
                    "title": "Error Rates",
                    "type": "stat",
                    "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32},
                    "targets": [
                        {
                            "expr": f'rate(http_requests_total{{namespace="tenant-{tenant_id}", status=~"5.."}}[5m]) / rate(http_requests_total{{namespace="tenant-{tenant_id}"}}[5m]) * 100',
                            "legendFormat": "5xx Error Rate (%)",
                            "refId": "A"
                        },
                        {
                            "expr": f'rate(http_requests_total{{namespace="tenant-{tenant_id}", status=~"4.."}}[5m]) / rate(http_requests_total{{namespace="tenant-{tenant_id}"}}[5m]) * 100',
                            "legendFormat": "4xx Error Rate (%)",
                            "refId": "B"
                        }
                    ],
                    "fieldConfig": {
                        "defaults": {
                            "unit": "percent",
                            "min": 0,
                            "max": 100,
                            "thresholds": {
                                "steps": [
                                    {"color": "green", "value": None},
                                    {"color": "yellow", "value": 1},
                                    {"color": "red", "value": 5}
                                ]
                            }
                        }
                    }
                }
            ],
            "time": {"from": "now-1h", "to": "now"},
            "timepicker": {},
            "templating": {
                "list": [
                    {
                        "name": "tenant_id",
                        "type": "constant",
                        "current": {"value": tenant_id, "text": tenant_id},
                        "hide": 2
                    }
                ]
            },
            "annotations": {
                "list": [
                    {
                        "name": "Deployments",
                        "datasource": "Prometheus",
                        "enable": True,
                        "expr": f'changes(kube_deployment_status_replicas{{namespace="tenant-{tenant_id}"}}[1h])',
                        "iconColor": "blue",
                        "titleFormat": "Deployment Change",
                        "textFormat": "{{deployment}} replicas changed"
                    }
                ]
            },
            "refresh": "30s",
            "schemaVersion": 27,
            "version": 1,
            "links": [
                {
                    "title": "Tenant Logs",
                    "url": f"/d/logs/logs?var-namespace=tenant-{tenant_id}",
                    "type": "dashboards"
                },
                {
                    "title": "Tenant Alerts",
                    "url": f"/alerting/list?search=tenant-{tenant_id}",
                    "type": "link"
                }
            ]
        },
        "meta": {
            "type": "db",
            "canSave": True,
            "canEdit": True,
            "canAdmin": True,
            "canStar": True,
            "slug": f"tenant-{tenant_id}-comprehensive",
            "url": f"/d/tenant-{tenant_id}/tenant-{tenant_name}-{tenant_id}-comprehensive-monitoring",
            "expires": "0001-01-01T00:00:00Z",
            "created": "2023-01-01T00:00:00Z",
            "updated": "2023-01-01T00:00:00Z",
            "updatedBy": "admin",
            "createdBy": "admin",
            "version": 1,
            "hasAcl": False,
            "isFolder": False,
            "folderId": 0,
            "folderTitle": "General",
            "folderUrl": "",
            "provisioned": True,
            "provisionedExternalId": f"tenant-{tenant_id}-dashboard"
        }
    }

def setup_comprehensive_alerting(tenant_id: str) -> bool:
    """Set up comprehensive alerting rules for a tenant."""
    logger.info(f"Setting up comprehensive alerting for tenant: {tenant_id}")
    
    alerting_rules = f"""
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tenant-{tenant_id}-comprehensive-alerts
  namespace: monitoring
  labels:
    tenant-id: "{tenant_id}"
    prometheus: kube-prometheus
    role: alert-rules
spec:
  groups:
  - name: tenant-{tenant_id}-performance
    rules:
    - alert: TenantHighCPUUsage
      expr: sum(rate(container_cpu_usage_seconds_total{{namespace="tenant-{tenant_id}"}}[5m])) * 100 > 80
      for: 5m
      labels:
        severity: warning
        tenant_id: "{tenant_id}"
        category: performance
      annotations:
        summary: "High CPU usage for tenant {tenant_id}"
        description: "Tenant {tenant_id} is using {{{{ $value }}}}% CPU for more than 5 minutes"
    
    - alert: TenantHighMemoryUsage
      expr: sum(container_memory_usage_bytes{{namespace="tenant-{tenant_id}"}}) / sum(container_spec_memory_limit_bytes{{namespace="tenant-{tenant_id}"}}) * 100 > 85
      for: 5m
      labels:
        severity: warning
        tenant_id: "{tenant_id}"
        category: performance
      annotations:
        summary: "High memory usage for tenant {tenant_id}"
        description: "Tenant {tenant_id} is using {{{{ $value }}}}% of allocated memory"
    
    - alert: TenantPodCrashLooping
      expr: rate(kube_pod_container_status_restarts_total{{namespace="tenant-{tenant_id}"}}[15m]) > 0
      for: 5m
      labels:
        severity: critical
        tenant_id: "{tenant_id}"
        category: availability
      annotations:
        summary: "Pod crash looping for tenant {tenant_id}"
        description: "Pod {{{{ $labels.pod }}}} in tenant {tenant_id} is crash looping"
    
    - alert: TenantHighErrorRate
      expr: rate(http_requests_total{{namespace="tenant-{tenant_id}", status=~"5.."}}[5m]) / rate(http_requests_total{{namespace="tenant-{tenant_id}"}}[5m]) * 100 > 5
      for: 5m
      labels:
        severity: critical
        tenant_id: "{tenant_id}"
        category: application
      annotations:
        summary: "High error rate for tenant {tenant_id}"
        description: "Tenant {tenant_id} has {{{{ $value }}}}% 5xx error rate"
    
    - alert: TenantHighLatency
      expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{{namespace="tenant-{tenant_id}"}}[5m])) > 2
      for: 5m
      labels:
        severity: warning
        tenant_id: "{tenant_id}"
        category: performance
      annotations:
        summary: "High latency for tenant {tenant_id}"
        description: "Tenant {tenant_id} 95th percentile latency is {{{{ $value }}}}s"
    
    - alert: TenantDatabaseConnectionFailure
      expr: mysql_up{{namespace="tenant-{tenant_id}"}} == 0
      for: 1m
      labels:
        severity: critical
        tenant_id: "{tenant_id}"
        category: database
      annotations:
        summary: "Database connection failure for tenant {tenant_id}"
        description: "Database connection for tenant {tenant_id} is down"
    
    - alert: TenantSecurityEvent
      expr: rate(security_events_total{{tenant="{tenant_id}", priority="CRITICAL"}}[5m]) > 0
      for: 0m
      labels:
        severity: critical
        tenant_id: "{tenant_id}"
        category: security
      annotations:
        summary: "Critical security event for tenant {tenant_id}"
        description: "Critical security event detected for tenant {tenant_id}: {{{{ $labels.rule }}}}"
    
    - alert: TenantStorageUsageHigh
      expr: s3_bucket_size_bytes{{bucket="tenant-{tenant_id}-assets"}} / (10 * 1024 * 1024 * 1024) > 0.9
      for: 5m
      labels:
        severity: warning
        tenant_id: "{tenant_id}"
        category: storage
      annotations:
        summary: "High storage usage for tenant {tenant_id}"
        description: "Tenant {tenant_id} is using {{{{ $value | humanizePercentage }}}} of allocated storage"
"""
    
    # Apply the alerting rules
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(alerting_rules)
        temp_file = f.name
    
    try:
        result = subprocess.run(
            f"kubectl apply -f {temp_file}",
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"Comprehensive alerting rules created for tenant: {tenant_id}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create alerting rules: {e}")
        return False
    finally:
        import os
        os.unlink(temp_file)

def setup_sla_monitoring(tenant_id: str) -> bool:
    """Set up SLA monitoring and reporting for a tenant."""
    logger.info(f"Setting up SLA monitoring for tenant: {tenant_id}")
    
    sla_config = f"""
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-{tenant_id}-sla-config
  namespace: monitoring
  labels:
    tenant-id: "{tenant_id}"
    component: sla-monitoring
data:
  sla-targets.yaml: |
    tenant_id: "{tenant_id}"
    sla_targets:
      availability: 99.9  # 99.9% uptime
      response_time_p95: 2.0  # 95th percentile < 2 seconds
      error_rate: 0.1  # < 0.1% error rate
      security_incidents: 0  # Zero critical security incidents
    
    monitoring_queries:
      availability: |
        (
          sum(up{{namespace="tenant-{tenant_id}"}}) /
          count(up{{namespace="tenant-{tenant_id}"}})
        ) * 100
      
      response_time_p95: |
        histogram_quantile(0.95,
          rate(http_request_duration_seconds_bucket{{namespace="tenant-{tenant_id}"}}[5m])
        )
      
      error_rate: |
        (
          rate(http_requests_total{{namespace="tenant-{tenant_id}", status=~"5.."}}[5m]) /
          rate(http_requests_total{{namespace="tenant-{tenant_id}"}}[5m])
        ) * 100
      
      security_incidents: |
        sum(security_events_total{{tenant="{tenant_id}", priority="CRITICAL"}})
    
    reporting:
      frequency: "daily"
      recipients:
        - "tenant-{tenant_id}@architrave.com"
        - "<EMAIL>"
      
      thresholds:
        availability:
          warning: 99.5
          critical: 99.0
        response_time_p95:
          warning: 1.5
          critical: 3.0
        error_rate:
          warning: 0.5
          critical: 1.0
"""
    
    # Apply the SLA configuration
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        f.write(sla_config)
        temp_file = f.name
    
    try:
        result = subprocess.run(
            f"kubectl apply -f {temp_file}",
            shell=True,
            check=True,
            capture_output=True,
            text=True
        )
        logger.info(f"SLA monitoring configuration created for tenant: {tenant_id}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to create SLA monitoring configuration: {e}")
        return False
    finally:
        import os
        os.unlink(temp_file)

if __name__ == "__main__":
    import sys
    if len(sys.argv) != 3:
        print("Usage: python tenant-grafana-dashboards.py <tenant_id> <tenant_name>")
        sys.exit(1)
    
    tenant_id = sys.argv[1]
    tenant_name = sys.argv[2]
    
    # Set up logging
    logging.basicConfig(level=logging.INFO)
    
    # Create dashboard and monitoring
    setup_tenant_grafana_dashboard(tenant_id, tenant_name)
    setup_comprehensive_alerting(tenant_id)
    setup_sla_monitoring(tenant_id)
    
    print(f"✅ Comprehensive monitoring setup completed for tenant: {tenant_id}")
