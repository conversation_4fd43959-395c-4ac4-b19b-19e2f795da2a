{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive tenant performance monitoring dashboard with SLA tracking", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"tenant-$tenant_id\"}[5m])) by (pod) * 100", "interval": "", "legendFormat": "CPU Usage - {{pod}}", "refId": "A"}, {"expr": "sum(container_memory_usage_bytes{namespace=\"tenant-$tenant_id\"}) by (pod) / sum(container_spec_memory_limit_bytes{namespace=\"tenant-$tenant_id\"}) by (pod) * 100", "interval": "", "legendFormat": "Memory Usage - {{pod}}", "refId": "B"}], "title": "Resource Utilization", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 200}, {"color": "red", "value": 500}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "8.0.0", "targets": [{"expr": "histogram_quantile(0.95, sum(rate(nginx_http_request_duration_seconds_bucket{namespace=\"tenant-$tenant_id\"}[5m])) by (le)) * 1000", "interval": "", "legendFormat": "95th Percentile Response Time", "refId": "A"}], "title": "Response Time (95th Percentile)", "type": "gauge"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(rate(nginx_http_requests_total{namespace=\"tenant-$tenant_id\"}[5m]))", "interval": "", "legendFormat": "Request Rate", "refId": "A"}, {"expr": "sum(rate(nginx_http_requests_total{namespace=\"tenant-$tenant_id\", status=~\"5..\"}[5m])) / sum(rate(nginx_http_requests_total{namespace=\"tenant-$tenant_id\"}[5m])) * 100", "interval": "", "legendFormat": "Error Rate %", "refId": "B"}], "title": "Request Rate & Error Rate", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "mysql_global_status_threads_connected{namespace=\"tenant-$tenant_id\"}", "interval": "", "legendFormat": "Active Connections", "refId": "A"}, {"expr": "mysql_global_variables_max_connections{namespace=\"tenant-$tenant_id\"}", "interval": "", "legendFormat": "Max Connections", "refId": "B"}, {"expr": "mysql_global_status_threads_connected{namespace=\"tenant-$tenant_id\"} / mysql_global_variables_max_connections{namespace=\"tenant-$tenant_id\"} * 100", "interval": "", "legendFormat": "Connection Pool Utilization %", "refId": "C"}], "title": "Database Connection Pool", "type": "timeseries"}], "refresh": "30s", "schemaVersion": 27, "style": "dark", "tags": ["tenant", "performance", "sla"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(kube_namespace_labels{label_tenant!=\"\"}, label_tenant)", "hide": 0, "includeAll": true, "label": "Tenant ID", "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(kube_namespace_labels{label_tenant!=\"\"}, label_tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Tenant Performance Dashboard", "uid": "tenant-performance", "version": 1}