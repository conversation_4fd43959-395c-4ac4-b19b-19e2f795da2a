{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Comprehensive cost tracking and billing dashboard for tenant resources", "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": "CloudWatch", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}, "id": 1, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"dimensions": {"ServiceName": "Amazon Elastic Kubernetes Service", "LinkedAccount": "$account_id"}, "expression": "", "id": "", "matchExact": true, "metricName": "EstimatedCharges", "namespace": "AWS/Billing", "period": "", "refId": "A", "region": "us-east-1", "statistics": ["Maximum"]}], "title": "Monthly EKS Cost", "type": "stat"}, {"datasource": "CloudWatch", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 50}, {"color": "red", "value": 200}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}, "id": 2, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"dimensions": {"ServiceName": "Amazon Relational Database Service", "LinkedAccount": "$account_id"}, "expression": "", "id": "", "matchExact": true, "metricName": "EstimatedCharges", "namespace": "AWS/Billing", "period": "", "refId": "A", "region": "us-east-1", "statistics": ["Maximum"]}], "title": "Monthly RDS Cost", "type": "stat"}, {"datasource": "CloudWatch", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 20}, {"color": "red", "value": 100}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 0}, "id": 3, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"dimensions": {"ServiceName": "Amazon Simple Storage Service", "LinkedAccount": "$account_id"}, "expression": "", "id": "", "matchExact": true, "metricName": "EstimatedCharges", "namespace": "AWS/Billing", "period": "", "refId": "A", "region": "us-east-1", "statistics": ["Maximum"]}], "title": "Monthly S3 Cost", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 500}, {"color": "red", "value": 1000}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 4, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "text": {}, "textMode": "auto"}, "pluginVersion": "8.0.0", "targets": [{"expr": "sum(kube_pod_container_resource_requests_cpu_cores{namespace=\"tenant-$tenant_id\"}) * 0.0464 * 24 * 30", "interval": "", "legendFormat": "Estimated Monthly CPU Cost", "refId": "A"}], "title": "Estimated Tenant CPU Cost", "type": "stat"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(kube_pod_container_resource_requests_cpu_cores{namespace=\"tenant-$tenant_id\"}) * 0.0464", "interval": "", "legendFormat": "CPU Cost per Hour", "refId": "A"}, {"expr": "sum(kube_pod_container_resource_requests_memory_bytes{namespace=\"tenant-$tenant_id\"}) / 1024 / 1024 / 1024 * 0.00506", "interval": "", "legendFormat": "Memory Cost per Hour", "refId": "B"}, {"expr": "sum(kube_persistentvolume_capacity_bytes{namespace=\"tenant-$tenant_id\"}) / 1024 / 1024 / 1024 * 0.10 / 24 / 30", "interval": "", "legendFormat": "Storage Cost per Hour", "refId": "C"}], "title": "Hourly Resource Costs", "type": "timeseries"}, {"datasource": "Prometheus", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "unit": "currencyUSD"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "options": {"legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "reduceOptions": {"values": false, "calcs": ["lastNotNull"], "fields": ""}, "tooltip": {"mode": "single"}}, "targets": [{"expr": "sum(kube_pod_container_resource_requests_cpu_cores{namespace=\"tenant-$tenant_id\"}) * 0.0464 * 24 * 30", "interval": "", "legendFormat": "CPU", "refId": "A"}, {"expr": "sum(kube_pod_container_resource_requests_memory_bytes{namespace=\"tenant-$tenant_id\"}) / 1024 / 1024 / 1024 * 0.00506 * 24 * 30", "interval": "", "legendFormat": "Memory", "refId": "B"}, {"expr": "sum(kube_persistentvolume_capacity_bytes{namespace=\"tenant-$tenant_id\"}) / 1024 / 1024 / 1024 * 0.10", "interval": "", "legendFormat": "Storage", "refId": "C"}], "title": "Monthly Cost Breakdown", "type": "piechart"}], "refresh": "5m", "schemaVersion": 27, "style": "dark", "tags": ["tenant", "cost", "billing"], "templating": {"list": [{"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(kube_namespace_labels{label_tenant!=\"\"}, label_tenant)", "hide": 0, "includeAll": true, "label": "Tenant ID", "multi": false, "name": "tenant_id", "options": [], "query": {"query": "label_values(kube_namespace_labels{label_tenant!=\"\"}, label_tenant)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {"selected": false, "text": "************", "value": "************"}, "hide": 0, "label": "AWS Account ID", "name": "account_id", "options": [{"selected": true, "text": "************", "value": "************"}], "query": "************", "skipUrlSync": false, "type": "textbox"}]}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Tenant Cost Tracking Dashboard", "uid": "tenant-cost-tracking", "version": 1}