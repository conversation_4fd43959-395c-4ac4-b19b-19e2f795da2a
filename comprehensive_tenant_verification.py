#!/usr/bin/env python3
"""
Comprehensive Tenant Verification Script
This script performs complete tenant offboarding, onboarding, and verification
"""

import subprocess
import sys
import time
import json
import os

def run_command(command, timeout=300):
    """Run a command and return the result"""
    print(f"\n🔄 Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        print(f"✅ Exit code: {result.returncode}")
        if result.stdout:
            print(f"📤 STDOUT:\n{result.stdout}")
        if result.stderr:
            print(f"📥 STDERR:\n{result.stderr}")
        return result
    except subprocess.TimeoutExpired:
        print(f"⏰ Command timed out after {timeout} seconds")
        return None
    except Exception as e:
        print(f"❌ Error running command: {e}")
        return None

def check_cluster_status():
    """Check Kubernetes cluster status"""
    print("\n" + "="*50)
    print("🔍 CHECKING CLUSTER STATUS")
    print("="*50)
    
    # Check cluster info
    run_command("kubectl cluster-info")
    
    # Check nodes
    run_command("kubectl get nodes")
    
    # Check existing tenants
    run_command("kubectl get namespaces | grep tenant")

def offboard_all_tenants():
    """Offboard all existing tenants"""
    print("\n" + "="*50)
    print("🗑️  OFFBOARDING ALL TENANTS")
    print("="*50)
    
    # Get all tenant namespaces
    result = run_command("kubectl get namespaces -o json")
    if result and result.returncode == 0:
        try:
            namespaces = json.loads(result.stdout)
            tenant_namespaces = [
                ns['metadata']['name'] for ns in namespaces['items']
                if ns['metadata']['name'].startswith('tenant-')
            ]
            
            print(f"Found {len(tenant_namespaces)} tenant namespaces: {tenant_namespaces}")
            
            for ns in tenant_namespaces:
                tenant_id = ns.replace('tenant-', '')
                print(f"\n🗑️  Offboarding tenant: {tenant_id}")
                run_command(f"python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id {tenant_id} --force --verify", timeout=600)
        except Exception as e:
            print(f"❌ Error parsing namespaces: {e}")
    
    # Also try common tenant IDs
    common_tenants = ['fresh-test', 'acme-corp', 'test-tenant', 'demo']
    for tenant_id in common_tenants:
        print(f"\n🗑️  Attempting to offboard common tenant: {tenant_id}")
        run_command(f"python3 tenant-management/scripts/advanced_tenant_offboard.py --tenant-id {tenant_id} --force --verify", timeout=300)

def onboard_fresh_tenant():
    """Onboard a fresh tenant"""
    print("\n" + "="*50)
    print("🚀 ONBOARDING FRESH TENANT")
    print("="*50)
    
    tenant_id = "fresh-test"
    print(f"🚀 Onboarding tenant: {tenant_id}")
    
    run_command(f"""python3 tenant-management/scripts/advanced_tenant_onboard.py \
        --tenant-id {tenant_id} \
        --tenant-name "Fresh Test" \
        --subdomain {tenant_id} \
        --debug""", timeout=900)

def verify_tenant_components():
    """Verify all tenant components"""
    print("\n" + "="*50)
    print("🔍 VERIFYING TENANT COMPONENTS")
    print("="*50)
    
    tenant_id = "fresh-test"
    namespace = f"tenant-{tenant_id}"
    
    # Check namespace
    print(f"\n📁 Checking namespace: {namespace}")
    run_command(f"kubectl get namespace {namespace}")
    run_command(f"kubectl describe namespace {namespace}")
    
    # Check pods
    print(f"\n🐳 Checking pods in {namespace}")
    run_command(f"kubectl get pods -n {namespace}")
    run_command(f"kubectl get pods -n {namespace} -o wide")
    
    # Check services
    print(f"\n🌐 Checking services in {namespace}")
    run_command(f"kubectl get services -n {namespace}")
    
    # Check deployments
    print(f"\n📦 Checking deployments in {namespace}")
    run_command(f"kubectl get deployments -n {namespace}")
    
    # Check configmaps and secrets
    print(f"\n🔧 Checking configmaps and secrets in {namespace}")
    run_command(f"kubectl get configmaps -n {namespace}")
    run_command(f"kubectl get secrets -n {namespace}")

def check_pod_details():
    """Check detailed pod information"""
    print("\n" + "="*50)
    print("🔍 CHECKING POD DETAILS")
    print("="*50)
    
    tenant_id = "fresh-test"
    namespace = f"tenant-{tenant_id}"
    
    # Get pod names
    result = run_command(f"kubectl get pods -n {namespace} -o json")
    if result and result.returncode == 0:
        try:
            pods = json.loads(result.stdout)
            for pod in pods['items']:
                pod_name = pod['metadata']['name']
                print(f"\n🐳 Checking pod: {pod_name}")
                
                # Describe pod
                run_command(f"kubectl describe pod {pod_name} -n {namespace}")
                
                # Check logs
                run_command(f"kubectl logs {pod_name} -n {namespace} --tail=50")
                
                # Check if pod is ready
                run_command(f"kubectl get pod {pod_name} -n {namespace} -o yaml")
        except Exception as e:
            print(f"❌ Error parsing pods: {e}")

def main():
    """Main function"""
    print("🎯 COMPREHENSIVE TENANT VERIFICATION STARTING")
    print("=" * 60)
    
    # Change to the correct directory
    os.chdir('/Users/<USER>/Projects/new_project/infra-provisioning')
    print(f"📁 Working directory: {os.getcwd()}")
    
    try:
        # Step 1: Check cluster status
        check_cluster_status()
        
        # Step 2: Offboard all existing tenants
        offboard_all_tenants()
        
        # Step 3: Onboard fresh tenant
        onboard_fresh_tenant()
        
        # Step 4: Verify tenant components
        verify_tenant_components()
        
        # Step 5: Check pod details
        check_pod_details()
        
        print("\n" + "="*60)
        print("✅ COMPREHENSIVE TENANT VERIFICATION COMPLETED")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\n❌ Verification interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Verification failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
