#!/usr/bin/env python3
"""
Mass Operations Monitor
Monitors multiple onboarding and offboarding operations
"""

import subprocess
import time
import json
from datetime import datetime

def run_command(command, timeout=10):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def monitor_tenant_status():
    """Monitor current tenant status."""
    print_header("TENANT STATUS MONITORING")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers | grep tenant- | wc -l", 8)
    tenant_count = stdout if success else "Unknown"
    print(f"📊 Total tenant namespaces: {tenant_count}")
    
    # Get tenant namespaces list
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers | grep tenant-", 8)
    if success and stdout:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        print(f"📋 Active tenants:")
        for ns in tenant_namespaces:
            tenant_id = ns.replace('tenant-', '')
            print(f"  - {tenant_id}")
            
            # Check pod status for each tenant
            success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers 2>/dev/null", 5)
            if success and stdout:
                pods = [line for line in stdout.split('\n') if line.strip()]
                running_pods = [line for line in pods if 'Running' in line]
                pending_pods = [line for line in pods if 'Pending' in line]
                print(f"    Pods: {len(running_pods)} running, {len(pending_pods)} pending, {len(pods)} total")
            else:
                print(f"    Pods: No pods or namespace not ready")
    else:
        print("📋 No tenant namespaces found")

def monitor_ecr_images():
    """Monitor ECR image usage in deployments."""
    print_header("ECR IMAGE VERIFICATION")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers | grep tenant-", 8)
    if not success or not stdout:
        print("❌ No tenant namespaces found")
        return
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    
    ecr_images_found = {
        'frontend': 0,
        'backend': 0,
        'rabbitmq': 0,
        'nginx': 0
    }
    
    for ns in tenant_namespaces:
        # Check deployments in namespace
        success, stdout, stderr = run_command(f"kubectl get deployments -n {ns} -o yaml 2>/dev/null", 8)
        if success and stdout:
            # Count ECR images
            if "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41" in stdout:
                ecr_images_found['frontend'] += 1
            if "545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test" in stdout:
                ecr_images_found['backend'] += 1
            if "545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02" in stdout:
                ecr_images_found['rabbitmq'] += 1
            if "545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl" in stdout:
                ecr_images_found['nginx'] += 1
    
    print("📊 ECR Image Usage:")
    for component, count in ecr_images_found.items():
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {component.title()}: {count} deployments using correct ECR image")

def monitor_pod_health():
    """Monitor pod health across all tenants."""
    print_header("POD HEALTH MONITORING")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers | grep tenant-", 8)
    if not success or not stdout:
        print("❌ No tenant namespaces found")
        return
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    
    total_pods = 0
    running_pods = 0
    pending_pods = 0
    failed_pods = 0
    
    for ns in tenant_namespaces:
        success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers 2>/dev/null", 5)
        if success and stdout:
            pods = [line for line in stdout.split('\n') if line.strip()]
            total_pods += len(pods)
            
            for pod_line in pods:
                if 'Running' in pod_line:
                    running_pods += 1
                elif 'Pending' in pod_line:
                    pending_pods += 1
                elif 'Failed' in pod_line or 'Error' in pod_line:
                    failed_pods += 1
    
    print(f"📊 Overall Pod Health:")
    print(f"  Total pods: {total_pods}")
    print(f"  ✅ Running: {running_pods}")
    print(f"  ⏳ Pending: {pending_pods}")
    print(f"  ❌ Failed: {failed_pods}")
    
    if total_pods > 0:
        success_rate = (running_pods / total_pods) * 100
        print(f"  📈 Success rate: {success_rate:.1f}%")

def monitor_services():
    """Monitor services across all tenants."""
    print_header("SERVICE MONITORING")
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers | grep tenant-", 8)
    if not success or not stdout:
        print("❌ No tenant namespaces found")
        return
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    
    service_types = {
        'backend': 0,
        'frontend': 0,
        'rabbitmq': 0,
        'nginx': 0
    }
    
    for ns in tenant_namespaces:
        success, stdout, stderr = run_command(f"kubectl get services -n {ns} --no-headers 2>/dev/null", 5)
        if success and stdout:
            services = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            
            for service in services:
                if 'backend' in service:
                    service_types['backend'] += 1
                elif 'frontend' in service:
                    service_types['frontend'] += 1
                elif 'rabbitmq' in service:
                    service_types['rabbitmq'] += 1
                elif 'nginx' in service:
                    service_types['nginx'] += 1
    
    print(f"📊 Service Distribution:")
    for service_type, count in service_types.items():
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {service_type.title()}: {count} services")

def main():
    """Main monitoring function."""
    print("🔍 MASS OPERATIONS MONITORING")
    print("=" * 60)
    print(f"Monitoring started at: {datetime.now()}")
    
    try:
        # Run all monitoring checks
        monitor_tenant_status()
        monitor_ecr_images()
        monitor_pod_health()
        monitor_services()
        
        print_header("MONITORING SUMMARY")
        print("✅ Monitoring completed successfully")
        print(f"📊 Report generated at: {datetime.now()}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Monitoring error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
