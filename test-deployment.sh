#!/bin/bash

# Comprehensive Test Script for Performance Optimization & Tenant Management UI
# This script validates all deployed features

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

# Test functions
test_keda_installation() {
    print_header "Testing KEDA Installation"
    
    if kubectl get pods -n keda-system -l app.kubernetes.io/name=keda-operator | grep -q Running; then
        log_success "KEDA operator is running"
    else
        log_error "KEDA operator is not running"
        return 1
    fi
    
    if kubectl get crd scaledobjects.keda.sh &> /dev/null; then
        log_success "KEDA CRDs are installed"
    else
        log_error "KEDA CRDs are missing"
        return 1
    fi
}

test_prometheus_alerts() {
    print_header "Testing Prometheus Alerting Rules"
    
    if kubectl get prometheusrules enhanced-tenant-alerts -n monitoring &> /dev/null; then
        log_success "Enhanced tenant alerting rules are deployed"
        
        # Count the number of alert rules
        alert_count=$(kubectl get prometheusrules enhanced-tenant-alerts -n monitoring -o jsonpath='{.spec.groups[*].rules[*].alert}' | wc -w)
        log_info "Number of alert rules deployed: $alert_count"
    else
        log_error "Enhanced tenant alerting rules are missing"
        return 1
    fi
}

test_grafana_dashboards() {
    print_header "Testing Grafana Dashboards"
    
    if kubectl get configmap grafana-dashboard-tenant-performance -n monitoring &> /dev/null; then
        log_success "Tenant performance dashboard is deployed"
    else
        log_error "Tenant performance dashboard is missing"
        return 1
    fi
    
    if kubectl get configmap grafana-dashboard-tenant-cost-tracking -n monitoring &> /dev/null; then
        log_success "Tenant cost tracking dashboard is deployed"
    else
        log_error "Tenant cost tracking dashboard is missing"
        return 1
    fi
}

test_log_aggregation() {
    print_header "Testing Log Aggregation Configuration"
    
    if kubectl get configmap loki-tenant-isolation-config -n monitoring &> /dev/null; then
        log_success "Loki tenant isolation configuration is deployed"
    else
        log_error "Loki tenant isolation configuration is missing"
        return 1
    fi
    
    if kubectl get configmap promtail-tenant-config -n monitoring &> /dev/null; then
        log_success "Promtail tenant configuration is deployed"
    else
        log_error "Promtail tenant configuration is missing"
        return 1
    fi
}

test_cost_tracking() {
    print_header "Testing Cost Tracking"
    
    if kubectl get cronjob tenant-cost-calculator -n monitoring &> /dev/null; then
        log_success "Cost calculation CronJob is deployed"
    else
        log_error "Cost calculation CronJob is missing"
        return 1
    fi
    
    if kubectl get servicemonitor tenant-cost-metrics -n monitoring &> /dev/null; then
        log_success "Cost metrics ServiceMonitor is deployed"
    else
        log_error "Cost metrics ServiceMonitor is missing"
        return 1
    fi
    
    # Check if cost annotations are present
    cost_annotations=$(kubectl get namespaces -l tenant -o jsonpath='{.items[*].metadata.annotations.cost\.architrave\.com/hourly}' | wc -w)
    if [ "$cost_annotations" -gt 0 ]; then
        log_success "Cost annotations are present on tenant namespaces"
    else
        log_warning "Cost annotations are not yet populated (this is normal for new deployments)"
    fi
}

test_enhanced_autoscaling() {
    print_header "Testing Enhanced Auto-scaling"
    
    # Check if HPA is deployed for fast-test tenant
    if kubectl get hpa tenant-app-enhanced-hpa -n tenant-fast-test &> /dev/null; then
        log_success "Enhanced HPA is deployed for tenant-fast-test"
        
        # Get HPA details
        hpa_details=$(kubectl get hpa tenant-app-enhanced-hpa -n tenant-fast-test -o jsonpath='{.spec.minReplicas}-{.spec.maxReplicas}')
        log_info "HPA configuration: Min/Max replicas = $hpa_details"
    else
        log_warning "Enhanced HPA is not deployed for tenant-fast-test"
    fi
    
    # Check if PDB is deployed
    if kubectl get pdb tenant-app-pdb -n tenant-fast-test &> /dev/null; then
        log_success "Pod Disruption Budget is deployed for tenant-fast-test"
    else
        log_warning "Pod Disruption Budget is not deployed for tenant-fast-test"
    fi
}

test_tenant_management_ui() {
    print_header "Testing Tenant Management UI"
    
    # Check UI pods
    ui_ready=$(kubectl get pods -n tenant-management -l app=tenant-management-ui --no-headers | grep Running | wc -l)
    if [ "$ui_ready" -gt 0 ]; then
        log_success "Tenant Management UI pods are running ($ui_ready pods)"
    else
        log_error "Tenant Management UI pods are not running"
        return 1
    fi
    
    # Check API pods
    api_ready=$(kubectl get pods -n tenant-management -l app=tenant-management-api-simple --no-headers | grep Running | wc -l)
    if [ "$api_ready" -gt 0 ]; then
        log_success "Tenant Management API pods are running ($api_ready pods)"
    else
        log_error "Tenant Management API pods are not running"
        return 1
    fi
    
    # Check services
    if kubectl get service tenant-management-ui -n tenant-management &> /dev/null; then
        ui_ip=$(kubectl get service tenant-management-ui -n tenant-management -o jsonpath='{.spec.clusterIP}')
        log_success "UI service is available at: $ui_ip"
    else
        log_error "UI service is not available"
        return 1
    fi
    
    # Check ingress
    if kubectl get ingress tenant-management-ingress -n tenant-management &> /dev/null; then
        log_success "Ingress is configured for tenant-management.local"
    else
        log_error "Ingress is not configured"
        return 1
    fi
}

test_tenant_isolation() {
    print_header "Testing Tenant Isolation"
    
    # Check tenant namespace labels
    tenant_count=$(kubectl get namespaces -l tenant --no-headers | wc -l)
    if [ "$tenant_count" -gt 0 ]; then
        log_success "Found $tenant_count tenant namespaces with proper labels"
        
        # List tenant details
        kubectl get namespaces -l tenant -o custom-columns="NAME:.metadata.name,TENANT:.metadata.labels.tenant,TIER:.metadata.labels.tier,MONITORING:.metadata.labels.monitoring" --no-headers | while read line; do
            log_info "Tenant: $line"
        done
    else
        log_warning "No tenant namespaces found with proper labels"
    fi
}

test_rbac_permissions() {
    print_header "Testing RBAC Permissions"
    
    # Check tenant management RBAC
    if kubectl get clusterrole tenant-management-role &> /dev/null; then
        log_success "Tenant management ClusterRole is configured"
    else
        log_error "Tenant management ClusterRole is missing"
        return 1
    fi
    
    if kubectl get clusterrolebinding tenant-management-binding &> /dev/null; then
        log_success "Tenant management ClusterRoleBinding is configured"
    else
        log_error "Tenant management ClusterRoleBinding is missing"
        return 1
    fi
    
    # Check cost calculator RBAC
    if kubectl get clusterrole cost-calculator-role &> /dev/null; then
        log_success "Cost calculator ClusterRole is configured"
    else
        log_error "Cost calculator ClusterRole is missing"
        return 1
    fi
}

# Main test execution
main() {
    print_header "🚀 PERFORMANCE OPTIMIZATION & TENANT MANAGEMENT UI TEST SUITE"
    
    local failed_tests=0
    
    # Run all tests
    test_keda_installation || ((failed_tests++))
    test_prometheus_alerts || ((failed_tests++))
    test_grafana_dashboards || ((failed_tests++))
    test_log_aggregation || ((failed_tests++))
    test_cost_tracking || ((failed_tests++))
    test_enhanced_autoscaling || ((failed_tests++))
    test_tenant_management_ui || ((failed_tests++))
    test_tenant_isolation || ((failed_tests++))
    test_rbac_permissions || ((failed_tests++))
    
    # Summary
    print_header "📊 TEST SUMMARY"
    
    if [ "$failed_tests" -eq 0 ]; then
        log_success "🎉 All tests passed! Your deployment is ready for production."
        echo ""
        log_info "🌐 Access Points:"
        log_info "• Tenant Management UI: http://tenant-management.local"
        log_info "• Add to /etc/hosts: echo '127.0.0.1 tenant-management.local' | sudo tee -a /etc/hosts"
        echo ""
        log_info "📈 Next Steps:"
        log_info "1. Configure authentication for production use"
        log_info "2. Set up alerting channels in AlertManager"
        log_info "3. Customize Grafana dashboards"
        log_info "4. Test auto-scaling with load generation"
        log_info "5. Configure backup and disaster recovery"
    else
        log_error "❌ $failed_tests test(s) failed. Please review the output above."
        exit 1
    fi
}

# Run the test suite
main "$@"
