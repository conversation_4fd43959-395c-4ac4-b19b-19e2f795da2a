apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-fresh-test-backend
  namespace: tenant-fresh-test
spec:
  template:
    spec:
      securityContext:
        runAsUser: 33
        runAsGroup: 33
        runAsNonRoot: true
        fsGroup: 33
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: backend
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
            add:
            - CHOWN
            - DAC_OVERRIDE
            - FOWNER
            - SETGID
            - SETUID
        volumeMounts:
        - name: s3-storage-fixed
          mountPath: /storage/ArchAssets/data/uploads
          subPath: uploads
        - name: s3-storage-fixed
          mountPath: /storage/clear
          subPath: clear
      volumes:
      - name: s3-storage-fixed
        persistentVolumeClaim:
          claimName: s3-pvc-fresh-test-fixed
