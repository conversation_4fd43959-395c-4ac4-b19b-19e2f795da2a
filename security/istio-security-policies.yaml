apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-fresh-test-strict-mtls
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-fresh-test-deny-all
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  action: DENY
  rules:
  - from:
    - source:
        notNamespaces: ["tenant-fresh-test", "istio-system"]
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-fresh-test-allow-frontend
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: tenant-fresh-test-frontend
  rules:
  - from:
    - source:
        namespaces: ["istio-system"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-fresh-test-allow-backend
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: tenant-fresh-test-backend
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-fresh-test/sa/default"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        paths: ["/health*", "/api/*"]
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-fresh-test-allow-rabbitmq
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: tenant-fresh-test-rabbitmq
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-fresh-test/sa/default"]
  - to:
    - operation:
        ports: ["5672", "15672"]
