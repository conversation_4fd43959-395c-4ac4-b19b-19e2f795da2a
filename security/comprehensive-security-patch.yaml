apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-fresh-test-backend
  namespace: tenant-fresh-test
spec:
  template:
    spec:
      securityContext:
        runAsUser: 33
        runAsGroup: 33
        runAsNonRoot: true
        fsGroup: 33
        seccompProfile:
          type: RuntimeDefault
      initContainers:
      - name: install-php-extensions
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      - name: init-schema
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      - name: init-php-config
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      - name: nginx
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
      containers:
      - name: backend
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: s3-storage-simple
          mountPath: /storage/ArchAssets/data/uploads
          subPath: uploads
        - name: s3-storage-simple
          mountPath: /storage/clear
          subPath: clear
      volumes:
      - name: s3-storage-simple
        persistentVolumeClaim:
          claimName: s3-pvc-fresh-test-simple
