---
# Image Security: Scanning and Signing Implementation
apiVersion: v1
kind: Namespace
metadata:
  name: image-security
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Trivy Image Scanner Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trivy-scanner
  namespace: image-security
  labels:
    app: trivy-scanner
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trivy-scanner
  template:
    metadata:
      labels:
        app: trivy-scanner
    spec:
      serviceAccountName: trivy-scanner
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: trivy
        image: aquasec/trivy:latest
        command: ["trivy"]
        args: ["server", "--listen", "0.0.0.0:8080", "--cache-dir", "/tmp/trivy"]
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: cache
          mountPath: /tmp/trivy
        - name: tmp
          mountPath: /tmp
        env:
        - name: TRIVY_CACHE_DIR
          value: "/tmp/trivy"
        - name: TRIVY_TEMP_DIR
          value: "/tmp"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 10
      volumes:
      - name: cache
        emptyDir: {}
      - name: tmp
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: trivy-scanner
  namespace: image-security
---
apiVersion: v1
kind: Service
metadata:
  name: trivy-scanner
  namespace: image-security
  labels:
    app: trivy-scanner
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: trivy-scanner
---
# Image Policy Webhook for Admission Control
apiVersion: apps/v1
kind: Deployment
metadata:
  name: image-policy-webhook
  namespace: image-security
  labels:
    app: image-policy-webhook
spec:
  replicas: 2
  selector:
    matchLabels:
      app: image-policy-webhook
  template:
    metadata:
      labels:
        app: image-policy-webhook
    spec:
      serviceAccountName: image-policy-webhook
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: webhook
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes requests
          cat > /app/webhook.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import base64
          import requests
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          
          app = Flask(__name__)
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          @app.route('/validate', methods=['POST'])
          def validate_image():
              admission_review = request.get_json()
              
              # Extract pod spec
              pod_spec = admission_review['request']['object']['spec']
              allowed = True
              message = ""
              
              # Check all containers
              for container in pod_spec.get('containers', []):
                  image = container['image']
                  
                  # Skip system images
                  if any(sys_img in image for sys_img in ['k8s.gcr.io', 'quay.io/coreos', 'docker.io/istio']):
                      continue
                  
                  # Check image signature
                  if not verify_image_signature(image):
                      allowed = False
                      message += f"Image {image} is not signed. "
                  
                  # Check vulnerability scan
                  if not check_vulnerability_scan(image):
                      allowed = False
                      message += f"Image {image} has critical vulnerabilities. "
              
              # Prepare admission response
              admission_response = {
                  "apiVersion": "admission.k8s.io/v1",
                  "kind": "AdmissionReview",
                  "response": {
                      "uid": admission_review['request']['uid'],
                      "allowed": allowed,
                      "status": {"message": message} if not allowed else {}
                  }
              }
              
              return jsonify(admission_response)
          
          def verify_image_signature(image):
              # Implement Cosign signature verification
              try:
                  # This would integrate with Cosign for signature verification
                  # For now, return True for demo purposes
                  return True
              except Exception as e:
                  print(f"Signature verification failed for {image}: {e}")
                  return False
          
          def check_vulnerability_scan(image):
              # Check with Trivy scanner
              try:
                  response = requests.get(
                      f"http://trivy-scanner.image-security.svc.cluster.local:8080/scan",
                      params={"image": image},
                      timeout=30
                  )
                  
                  if response.status_code == 200:
                      scan_result = response.json()
                      # Check for critical vulnerabilities
                      critical_vulns = scan_result.get('critical_vulnerabilities', 0)
                      return critical_vulns == 0
                  
                  return False
              except Exception as e:
                  print(f"Vulnerability scan failed for {image}: {e}")
                  return False
          
          if __name__ == '__main__':
              app.run(host='0.0.0.0', port=8443, ssl_context='adhoc')
          EOF
          
          python /app/webhook.py
        ports:
        - containerPort: 8443
          name: webhook
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: image-policy-webhook
  namespace: image-security
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: image-policy-webhook
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: image-policy-webhook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: image-policy-webhook
subjects:
- kind: ServiceAccount
  name: image-policy-webhook
  namespace: image-security
---
apiVersion: v1
kind: Service
metadata:
  name: image-policy-webhook
  namespace: image-security
  labels:
    app: image-policy-webhook
spec:
  ports:
  - port: 8443
    targetPort: 8443
    name: webhook
  selector:
    app: image-policy-webhook
---
# Validating Admission Webhook Configuration
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingAdmissionWebhook
metadata:
  name: image-policy-webhook
webhooks:
- name: image-policy.architrave.com
  clientConfig:
    service:
      name: image-policy-webhook
      namespace: image-security
      path: "/validate"
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # Base64 encoded CA cert
  rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  - operations: ["CREATE", "UPDATE"]
    apiGroups: ["apps"]
    apiVersions: ["v1"]
    resources: ["deployments", "replicasets", "daemonsets", "statefulsets"]
  namespaceSelector:
    matchExpressions:
    - key: name
      operator: In
      values: ["tenant-*"]  # Only apply to tenant namespaces
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail
---
# Cosign Image Signing Job
apiVersion: batch/v1
kind: Job
metadata:
  name: setup-cosign
  namespace: image-security
spec:
  template:
    spec:
      serviceAccountName: cosign-signer
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: cosign-setup
        image: gcr.io/projectsigstore/cosign:latest
        command: ["/bin/sh", "-c"]
        args:
        - |
          # Generate signing key pair
          cosign generate-key-pair
          
          # Store keys in Kubernetes secret
          kubectl create secret generic cosign-keys \
            --from-file=cosign.key=cosign.key \
            --from-file=cosign.pub=cosign.pub \
            --namespace=image-security
          
          echo "Cosign keys generated and stored in secret"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
      volumes:
      - name: tmp
        emptyDir: {}
      restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cosign-signer
  namespace: image-security
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cosign-signer
  namespace: image-security
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create", "get", "list", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cosign-signer
  namespace: image-security
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: cosign-signer
subjects:
- kind: ServiceAccount
  name: cosign-signer
  namespace: image-security
