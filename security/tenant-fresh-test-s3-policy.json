{"Version": "2012-10-17", "Statement": [{"Sid": "AllowTenantS3Access", "Effect": "Allow", "Action": ["s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetObjectVersion", "s3:PutObjectAcl", "s3:GetObjectAcl"], "Resource": ["arn:aws:s3:::tenant-fresh-test-assets", "arn:aws:s3:::tenant-fresh-test-assets/*"]}, {"Sid": "<PERSON>y<PERSON>therTenantAccess", "Effect": "<PERSON><PERSON>", "Action": "s3:*", "NotResource": ["arn:aws:s3:::tenant-fresh-test-assets", "arn:aws:s3:::tenant-fresh-test-assets/*"], "Condition": {"StringLike": {"s3:ExistingObjectTag/tenant": "*"}}}]}