apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: s3-sc-fresh-test-working
  labels:
    tenant.architrave.io/tenant-id: fresh-test
provisioner: s3.csi.aws.com
parameters:
  mounter: mountpoint-s3
  bucketName: tenant-fresh-test-assets
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: s3-pv-fresh-test-working
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  capacity:
    storage: 1200Gi
  accessModes:
    - ReadWriteMany
  persistentVolumeReclaimPolicy: Delete
  storageClassName: s3-sc-fresh-test-working
  csi:
    driver: s3.csi.aws.com
    volumeHandle: s3-csi-driver-volume-fresh-test-working
    volumeAttributes:
      bucketName: tenant-fresh-test-assets
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: s3-pvc-fresh-test-working
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: s3-sc-fresh-test-working
  resources:
    requests:
      storage: 1200Gi
