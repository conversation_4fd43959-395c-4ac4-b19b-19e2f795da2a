-- Database Multi-Tenancy Setup Script
-- This script implements complete multi-tenant database isolation

-- Step 1: Create tenant configuration table
CREATE TABLE IF NOT EXISTS tenant_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL UNIQUE,
    tenant_name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) NOT NULL UNIQUE,
    domain VARCHAR(255) NOT NULL DEFAULT 'architrave.com',
    s3_bucket VARCHAR(255) NOT NULL,
    database_user VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    settings JSON,
    INDEX idx_tenant_config_tenant_id (tenant_id),
    INDEX idx_tenant_config_subdomain (subdomain),
    INDEX idx_tenant_config_status (status)
);

-- Step 2: Add tenant_id columns to all major tables
ALTER TABLE users ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE documents ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE folders ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE assets ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE user_groups ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE workflows ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE workflow_steps ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'fresh-test';

-- Step 3: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id);
CREATE INDEX IF NOT EXISTS idx_folders_tenant_id ON folders(tenant_id);
CREATE INDEX IF NOT EXISTS idx_assets_tenant_id ON assets(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);
CREATE INDEX IF NOT EXISTS idx_user_groups_tenant_id ON user_groups(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflows_tenant_id ON workflows(tenant_id);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_tenant_id ON workflow_steps(tenant_id);
CREATE INDEX IF NOT EXISTS idx_notifications_tenant_id ON notifications(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON audit_logs(tenant_id);

-- Step 4: Insert fresh-test tenant configuration
INSERT INTO tenant_config (tenant_id, tenant_name, subdomain, s3_bucket, database_user) 
VALUES ('fresh-test', 'Fresh Test Company', 'fresh-test', 'tenant-fresh-test-assets', 'tenant_fresh_test')
ON DUPLICATE KEY UPDATE 
    tenant_name = VALUES(tenant_name),
    subdomain = VALUES(subdomain),
    s3_bucket = VALUES(s3_bucket),
    database_user = VALUES(database_user),
    updated_at = CURRENT_TIMESTAMP;

-- Step 5: Create tenant-specific database user (commented out for now)
-- CREATE USER IF NOT EXISTS 'tenant_fresh_test'@'%' IDENTIFIED BY 'secure_random_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON architrave.* TO 'tenant_fresh_test'@'%';
-- FLUSH PRIVILEGES;

-- Step 6: Create views for tenant-specific data access
CREATE OR REPLACE VIEW tenant_users AS 
SELECT * FROM users WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_documents AS 
SELECT * FROM documents WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_folders AS 
SELECT * FROM folders WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_assets AS 
SELECT * FROM assets WHERE tenant_id = @tenant_id;

-- Step 7: Create stored procedures for tenant-aware operations
DELIMITER //

CREATE OR REPLACE PROCEDURE SetTenantContext(IN p_tenant_id VARCHAR(50))
BEGIN
    SET @tenant_id = p_tenant_id;
END //

CREATE OR REPLACE PROCEDURE GetTenantUsers(IN p_tenant_id VARCHAR(50))
BEGIN
    SELECT * FROM users WHERE tenant_id = p_tenant_id;
END //

CREATE OR REPLACE PROCEDURE GetTenantDocuments(IN p_tenant_id VARCHAR(50))
BEGIN
    SELECT * FROM documents WHERE tenant_id = p_tenant_id;
END //

CREATE OR REPLACE PROCEDURE CreateTenantUser(
    IN p_tenant_id VARCHAR(50),
    IN p_username VARCHAR(255),
    IN p_email VARCHAR(255),
    IN p_password VARCHAR(255)
)
BEGIN
    INSERT INTO users (tenant_id, username, email, password, created_at)
    VALUES (p_tenant_id, p_username, p_email, p_password, NOW());
END //

DELIMITER ;

-- Step 8: Create triggers to automatically set tenant_id
DELIMITER //

CREATE OR REPLACE TRIGGER users_tenant_id_trigger
BEFORE INSERT ON users
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@tenant_id, 'fresh-test');
    END IF;
END //

CREATE OR REPLACE TRIGGER documents_tenant_id_trigger
BEFORE INSERT ON documents
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@tenant_id, 'fresh-test');
    END IF;
END //

CREATE OR REPLACE TRIGGER folders_tenant_id_trigger
BEFORE INSERT ON folders
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@tenant_id, 'fresh-test');
    END IF;
END //

CREATE OR REPLACE TRIGGER assets_tenant_id_trigger
BEFORE INSERT ON assets
FOR EACH ROW
BEGIN
    IF NEW.tenant_id IS NULL OR NEW.tenant_id = '' THEN
        SET NEW.tenant_id = COALESCE(@tenant_id, 'fresh-test');
    END IF;
END //

DELIMITER ;

-- Step 9: Update existing data to have tenant_id
UPDATE users SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';
UPDATE documents SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';
UPDATE folders SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';
UPDATE assets SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';
UPDATE user_roles SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';
UPDATE user_groups SET tenant_id = 'fresh-test' WHERE tenant_id IS NULL OR tenant_id = '';

-- Step 10: Create audit table for tenant operations
CREATE TABLE IF NOT EXISTS tenant_audit (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL,
    operation VARCHAR(50) NOT NULL,
    table_name VARCHAR(100) NOT NULL,
    record_id INT,
    old_values JSON,
    new_values JSON,
    user_id INT,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_tenant_audit_tenant_id (tenant_id),
    INDEX idx_tenant_audit_timestamp (timestamp)
);

-- Verification queries
SELECT 'Tenant Config Table' as verification, COUNT(*) as count FROM tenant_config;
SELECT 'Users with tenant_id' as verification, COUNT(*) as count FROM users WHERE tenant_id = 'fresh-test';
SELECT 'Documents with tenant_id' as verification, COUNT(*) as count FROM documents WHERE tenant_id = 'fresh-test';
SELECT 'Folders with tenant_id' as verification, COUNT(*) as count FROM folders WHERE tenant_id = 'fresh-test';
SELECT 'Assets with tenant_id' as verification, COUNT(*) as count FROM assets WHERE tenant_id = 'fresh-test';
