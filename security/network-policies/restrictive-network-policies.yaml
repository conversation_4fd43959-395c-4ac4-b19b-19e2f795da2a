---
# Comprehensive Restrictive Network Policies for Multi-Tenant Environment
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
# Template for Tenant Network Policies (to be applied per tenant namespace)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-isolation-policy
  # This will be applied to each tenant namespace
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from Istio ingress gateway
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - podSelector:
        matchLabels:
          app: istio-proxy
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090  # Prometheus metrics
    - protocol: TCP
      port: 8080  # Application metrics
  # Allow ingress within same tenant namespace
  - from:
    - podSelector: {}
  egress:
  # Allow egress to DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow egress to Istio system
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow egress to kube-system for service discovery
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443  # Kubernetes API
  # Allow egress to external HTTPS (for API calls, updates, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress to RDS (Aurora Serverless)
  - to: []
    ports:
    - protocol: TCP
      port: 3306
  # Allow egress within same tenant namespace
  - to:
    - podSelector: {}
---
# Istio System Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: istio-system-policy
  namespace: istio-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from all tenant namespaces
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: In
          values: ["tenant-*"]
  # Allow ingress from external load balancer
  - from: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 15021  # Istio health check
  # Allow ingress within istio-system
  - from:
    - podSelector: {}
  egress:
  # Allow egress to all tenant namespaces
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: In
          values: ["tenant-*"]
  # Allow egress to external (for cert management, etc.)
  - to: []
    ports:
    - protocol: TCP
      port: 443
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow egress within istio-system
  - to:
    - podSelector: {}
---
# Monitoring Namespace Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: monitoring-policy
  namespace: monitoring
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from Istio for Grafana/Prometheus UI
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow ingress within monitoring namespace
  - from:
    - podSelector: {}
  egress:
  # Allow egress to all tenant namespaces for metrics collection
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: In
          values: ["tenant-*"]
    ports:
    - protocol: TCP
      port: 9090  # Prometheus metrics
    - protocol: TCP
      port: 8080  # Application metrics
  # Allow egress to kube-system for cluster metrics
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
  # Allow egress to istio-system for service mesh metrics
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow external HTTPS for alerting webhooks
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress within monitoring namespace
  - to:
    - podSelector: {}
---
# Security System Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: security-system-policy
  namespace: security-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from monitoring for security metrics
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9100  # Security metrics
  # Allow ingress from kube-system for admission webhooks
  - from:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 8443  # Webhook port
  # Allow ingress within security-system
  - from:
    - podSelector: {}
  egress:
  # Allow egress to kube-system for API server communication
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow external HTTPS for security updates and threat intelligence
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress within security-system
  - to:
    - podSelector: {}
---
# Hetzner DNS System Network Policy
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: hetzner-dns-policy
  namespace: hetzner-dns-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080  # Metrics port
  # Allow ingress within hetzner-dns-system
  - from:
    - podSelector: {}
  egress:
  # Allow egress to kube-system for API server communication
  - to:
    - namespaceSelector:
        matchLabels:
          name: kube-system
    ports:
    - protocol: TCP
      port: 443
  # Allow egress to istio-system to get load balancer IP
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS to Hetzner DNS API
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow egress within hetzner-dns-system
  - to:
    - podSelector: {}
---
# Kube-System Network Policy (Selective)
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kube-system-selective-policy
  namespace: kube-system
spec:
  podSelector:
    matchLabels:
      k8s-app: kube-dns
  policyTypes:
  - Ingress
  ingress:
  # Allow DNS queries from all namespaces
  - from: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
---
# Network Policy for Cross-Tenant Communication Prevention
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: prevent-cross-tenant-communication
  # This policy template prevents communication between different tenant namespaces
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Explicitly deny ingress from other tenant namespaces
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: NotIn
          values: ["tenant-*"]  # Allow only from non-tenant namespaces
  egress:
  # Explicitly deny egress to other tenant namespaces
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: name
          operator: NotIn
          values: ["tenant-*"]  # Allow only to non-tenant namespaces
