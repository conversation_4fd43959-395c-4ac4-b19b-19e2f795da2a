apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-fresh-test-backend
  namespace: tenant-fresh-test
  labels:
    app: tenant-fresh-test-backend
    tenant.architrave.io/tenant-id: fresh-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-fresh-test-backend
  template:
    metadata:
      labels:
        app: tenant-fresh-test-backend
        tenant.architrave.io/tenant-id: fresh-test
        version: v1
    spec:
      securityContext:
        runAsUser: 33
        runAsGroup: 33
        runAsNonRoot: true
        fsGroup: 33
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: default
      initContainers:
      - name: init-setup
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
        command: ["/bin/bash"]
        args:
        - -c
        - |
          echo "Initialization completed"
          mkdir -p /storage/ArchAssets/data/uploads /storage/ArchAssets/data/cache
          echo '<?php echo "OK"; ?>' > /storage/ArchAssets/public/health-check.php
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
        volumeMounts:
        - name: s3-storage
          mountPath: /storage/ArchAssets/data/uploads
          subPath: uploads
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      containers:
      - name: backend
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.57-test
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 33
          runAsGroup: 33
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
        env:
        - name: TENANT_ID
          value: "fresh-test"
        - name: DB_HOST
          value: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        - name: DB_PORT
          value: "3306"
        - name: DB_USER
          value: "admin"
        - name: DB_PASSWORD
          value: "&BZzY_<AK(=a*UhZ"
        - name: DB_NAME
          value: "architrave"
        - name: MYSQL_HOST
          value: "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
        - name: MYSQL_PORT
          value: "3306"
        - name: MYSQL_USER
          value: "admin"
        - name: MYSQL_PASSWORD
          value: "&BZzY_<AK(=a*UhZ"
        - name: MYSQL_DATABASE
          value: "architrave"
        volumeMounts:
        - name: s3-storage
          mountPath: /storage/ArchAssets/data/uploads
          subPath: uploads
        - name: s3-storage
          mountPath: /storage/clear
          subPath: clear
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health-check.php
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health-check.php
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: s3-storage
        persistentVolumeClaim:
          claimName: s3-pvc-fresh-test-simple
