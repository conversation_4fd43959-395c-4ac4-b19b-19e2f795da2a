apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-fresh-test-strict-isolation
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow traffic from same namespace
  - from:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: fresh-test
  # Allow traffic from Istio system
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow traffic from monitoring
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 8080
  egress:
  # Allow traffic within same namespace
  - to:
    - namespaceSelector:
        matchLabels:
          tenant.architrave.io/tenant-id: fresh-test
  # Allow traffic to Istio system
  - to:
    - namespaceSelector:
        matchLabels:
          name: istio-system
  # Allow DNS
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow HTTPS for external APIs
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow database access (restricted)
  - to: []
    ports:
    - protocol: TCP
      port: 3306
  # Allow RabbitMQ
  - to: []
    ports:
    - protocol: TCP
      port: 5672
    - protocol: TCP
      port: 15672
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-fresh-test-deny-cross-tenant
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Deny traffic from other tenant namespaces
  - from:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["fresh-test"]
  egress:
  # Deny traffic to other tenant namespaces
  - to:
    - namespaceSelector:
        matchExpressions:
        - key: tenant.architrave.io/tenant-id
          operator: Exists
        - key: tenant.architrave.io/tenant-id
          operator: NotIn
          values: ["fresh-test"]
