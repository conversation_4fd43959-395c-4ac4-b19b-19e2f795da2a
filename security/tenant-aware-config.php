<?php
/**
 * Tenant-Aware Configuration and Middleware
 * This file provides tenant context and filtering capabilities
 */

class TenantContext {
    private static $tenantId = null;
    private static $tenantConfig = null;
    
    /**
     * Set the current tenant context
     */
    public static function setTenant($tenantId) {
        self::$tenantId = $tenantId;
        self::loadTenantConfig();
    }
    
    /**
     * Get the current tenant ID
     */
    public static function getTenantId() {
        if (self::$tenantId === null) {
            // Try to get from environment variable
            self::$tenantId = $_ENV['TENANT_ID'] ?? 'fresh-test';
        }
        return self::$tenantId;
    }
    
    /**
     * Load tenant configuration from database
     */
    private static function loadTenantConfig() {
        if (self::$tenantConfig === null && self::$tenantId !== null) {
            try {
                $pdo = self::getDatabaseConnection();
                $stmt = $pdo->prepare("SELECT * FROM tenant_config WHERE tenant_id = ? AND status = 'active'");
                $stmt->execute([self::$tenantId]);
                self::$tenantConfig = $stmt->fetch(PDO::FETCH_ASSOC);
            } catch (Exception $e) {
                error_log("Failed to load tenant config: " . $e->getMessage());
                self::$tenantConfig = false;
            }
        }
    }
    
    /**
     * Get tenant configuration
     */
    public static function getTenantConfig() {
        if (self::$tenantConfig === null) {
            self::loadTenantConfig();
        }
        return self::$tenantConfig;
    }
    
    /**
     * Add tenant filter to SQL query
     */
    public static function addTenantFilter($query, $tableAlias = '') {
        $tenantId = self::getTenantId();
        $prefix = $tableAlias ? $tableAlias . '.' : '';
        
        // Check if query already has WHERE clause
        if (stripos($query, 'WHERE') !== false) {
            return $query . " AND {$prefix}tenant_id = '" . self::escapeSql($tenantId) . "'";
        } else {
            return $query . " WHERE {$prefix}tenant_id = '" . self::escapeSql($tenantId) . "'";
        }
    }
    
    /**
     * Get database connection with tenant context
     */
    public static function getDatabaseConnection() {
        $host = $_ENV['DB_HOST'] ?? 'production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com';
        $port = $_ENV['DB_PORT'] ?? '3306';
        $dbname = $_ENV['DB_NAME'] ?? 'architrave';
        $username = $_ENV['DB_USER'] ?? 'admin';
        $password = $_ENV['DB_PASSWORD'] ?? '&BZzY_<AK(=a*UhZ';
        
        $dsn = "mysql:host={$host};port={$port};dbname={$dbname};charset=utf8mb4";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT => false,
        ];
        
        $pdo = new PDO($dsn, $username, $password, $options);
        
        // Set tenant context in database session
        $tenantId = self::getTenantId();
        $pdo->exec("SET @tenant_id = '" . self::escapeSql($tenantId) . "'");
        
        return $pdo;
    }
    
    /**
     * Execute tenant-aware query
     */
    public static function query($sql, $params = []) {
        $pdo = self::getDatabaseConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }
    
    /**
     * Get tenant-specific users
     */
    public static function getTenantUsers($limit = 100, $offset = 0) {
        $sql = "SELECT * FROM users WHERE tenant_id = ? LIMIT ? OFFSET ?";
        $stmt = self::query($sql, [self::getTenantId(), $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get tenant-specific documents
     */
    public static function getTenantDocuments($limit = 100, $offset = 0) {
        $sql = "SELECT * FROM documents WHERE tenant_id = ? LIMIT ? OFFSET ?";
        $stmt = self::query($sql, [self::getTenantId(), $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get tenant-specific folders
     */
    public static function getTenantFolders($limit = 100, $offset = 0) {
        $sql = "SELECT * FROM folders WHERE tenant_id = ? LIMIT ? OFFSET ?";
        $stmt = self::query($sql, [self::getTenantId(), $limit, $offset]);
        return $stmt->fetchAll();
    }
    
    /**
     * Create tenant-aware user
     */
    public static function createUser($userData) {
        $userData['tenant_id'] = self::getTenantId();
        $userData['created_at'] = date('Y-m-d H:i:s');
        
        $fields = implode(', ', array_keys($userData));
        $placeholders = ':' . implode(', :', array_keys($userData));
        
        $sql = "INSERT INTO users ({$fields}) VALUES ({$placeholders})";
        $stmt = self::query($sql, $userData);
        
        return self::getDatabaseConnection()->lastInsertId();
    }
    
    /**
     * Create tenant-aware document
     */
    public static function createDocument($documentData) {
        $documentData['tenant_id'] = self::getTenantId();
        $documentData['created_at'] = date('Y-m-d H:i:s');
        
        $fields = implode(', ', array_keys($documentData));
        $placeholders = ':' . implode(', :', array_keys($documentData));
        
        $sql = "INSERT INTO documents ({$fields}) VALUES ({$placeholders})";
        $stmt = self::query($sql, $documentData);
        
        return self::getDatabaseConnection()->lastInsertId();
    }
    
    /**
     * Validate tenant access to resource
     */
    public static function validateTenantAccess($table, $resourceId) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE id = ? AND tenant_id = ?";
        $stmt = self::query($sql, [$resourceId, self::getTenantId()]);
        $result = $stmt->fetch();
        
        return $result['count'] > 0;
    }
    
    /**
     * Get tenant statistics
     */
    public static function getTenantStats() {
        $tenantId = self::getTenantId();
        
        $stats = [];
        
        // Count users
        $stmt = self::query("SELECT COUNT(*) as count FROM users WHERE tenant_id = ?", [$tenantId]);
        $stats['users'] = $stmt->fetch()['count'];
        
        // Count documents
        $stmt = self::query("SELECT COUNT(*) as count FROM documents WHERE tenant_id = ?", [$tenantId]);
        $stats['documents'] = $stmt->fetch()['count'];
        
        // Count folders
        $stmt = self::query("SELECT COUNT(*) as count FROM folders WHERE tenant_id = ?", [$tenantId]);
        $stats['folders'] = $stmt->fetch()['count'];
        
        // Count assets
        $stmt = self::query("SELECT COUNT(*) as count FROM assets WHERE tenant_id = ?", [$tenantId]);
        $stats['assets'] = $stmt->fetch()['count'];
        
        return $stats;
    }
    
    /**
     * Simple SQL escaping
     */
    private static function escapeSql($value) {
        return addslashes($value);
    }
    
    /**
     * Middleware to ensure tenant context is set
     */
    public static function middleware() {
        // Set tenant context from environment or request
        $tenantId = $_ENV['TENANT_ID'] ?? $_SERVER['HTTP_X_TENANT_ID'] ?? 'fresh-test';
        self::setTenant($tenantId);
        
        // Validate tenant exists and is active
        $config = self::getTenantConfig();
        if (!$config) {
            http_response_code(403);
            echo json_encode(['error' => 'Invalid or inactive tenant']);
            exit;
        }
    }
}

// Auto-initialize tenant context
TenantContext::middleware();

// Create tenant-aware health check
function tenantHealthCheck() {
    try {
        $tenantId = TenantContext::getTenantId();
        $config = TenantContext::getTenantConfig();
        $stats = TenantContext::getTenantStats();
        
        return [
            'status' => 'healthy',
            'tenant_id' => $tenantId,
            'tenant_name' => $config['tenant_name'] ?? 'Unknown',
            'database_connection' => 'ok',
            'tenant_stats' => $stats,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    } catch (Exception $e) {
        return [
            'status' => 'unhealthy',
            'error' => $e->getMessage(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Example usage functions
function getTenantData() {
    return [
        'users' => TenantContext::getTenantUsers(10),
        'documents' => TenantContext::getTenantDocuments(10),
        'folders' => TenantContext::getTenantFolders(10),
        'stats' => TenantContext::getTenantStats()
    ];
}
?>
