-- Multi-tenancy database schema updates
-- Add tenant_id column to all major tables

-- Add tenant_id to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);

-- Add tenant_id to documents table
ALTER TABLE documents ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_documents_tenant_id ON documents(tenant_id);

-- Add tenant_id to folders table
ALTER TABLE folders ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_folders_tenant_id ON folders(tenant_id);

-- Add tenant_id to assets table
ALTER TABLE assets ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_assets_tenant_id ON assets(tenant_id);

-- Add tenant_id to user_roles table
ALTER TABLE user_roles ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_user_roles_tenant_id ON user_roles(tenant_id);

-- Add tenant_id to user_groups table
ALTER TABLE user_groups ADD COLUMN IF NOT EXISTS tenant_id VARCHAR(50) NOT NULL DEFAULT 'default';
CREATE INDEX IF NOT EXISTS idx_user_groups_tenant_id ON user_groups(tenant_id);

-- Create tenant configuration table
CREATE TABLE IF NOT EXISTS tenant_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tenant_id VARCHAR(50) NOT NULL UNIQUE,
    tenant_name VARCHAR(255) NOT NULL,
    subdomain VARCHAR(100) NOT NULL UNIQUE,
    domain VARCHAR(255) NOT NULL DEFAULT 'architrave.com',
    s3_bucket VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    settings JSON,
    INDEX idx_tenant_config_tenant_id (tenant_id),
    INDEX idx_tenant_config_subdomain (subdomain),
    INDEX idx_tenant_config_status (status)
);

-- Insert fresh-test tenant configuration
INSERT INTO tenant_config (tenant_id, tenant_name, subdomain, s3_bucket) 
VALUES ('fresh-test', 'Fresh Test Company', 'fresh-test', 'tenant-fresh-test-assets')
ON DUPLICATE KEY UPDATE 
    tenant_name = VALUES(tenant_name),
    subdomain = VALUES(subdomain),
    s3_bucket = VALUES(s3_bucket),
    updated_at = CURRENT_TIMESTAMP;

-- Create tenant-specific database user
-- Note: This should be run with admin privileges
-- CREATE USER IF NOT EXISTS 'tenant_fresh_test'@'%' IDENTIFIED BY 'secure_random_password_here';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON architrave.* TO 'tenant_fresh_test'@'%';
-- FLUSH PRIVILEGES;

-- Create views for tenant-specific data access
CREATE OR REPLACE VIEW tenant_users AS 
SELECT * FROM users WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_documents AS 
SELECT * FROM documents WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_folders AS 
SELECT * FROM folders WHERE tenant_id = @tenant_id;

CREATE OR REPLACE VIEW tenant_assets AS 
SELECT * FROM assets WHERE tenant_id = @tenant_id;
