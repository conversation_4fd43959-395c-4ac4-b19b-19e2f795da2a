---
# Per-Tenant Encryption and Secrets Management System
apiVersion: v1
kind: Namespace
metadata:
  name: encryption-system
  labels:
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
# Vault for Secrets Management
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vault
  namespace: encryption-system
  labels:
    app: vault
spec:
  serviceName: vault
  replicas: 1
  selector:
    matchLabels:
      app: vault
  template:
    metadata:
      labels:
        app: vault
    spec:
      serviceAccountName: vault
      securityContext:
        runAsNonRoot: true
        runAsUser: 100
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: vault
        image: vault:latest
        ports:
        - containerPort: 8200
          name: vault
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
            add: ["IPC_LOCK"]
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        env:
        - name: VAULT_DEV_ROOT_TOKEN_ID
          value: "root-token"
        - name: VAULT_DEV_LISTEN_ADDRESS
          value: "0.0.0.0:8200"
        - name: VAULT_ADDR
          value: "http://127.0.0.1:8200"
        volumeMounts:
        - name: vault-data
          mountPath: /vault/data
        - name: vault-config
          mountPath: /vault/config
        - name: tmp
          mountPath: /tmp
        command:
        - vault
        - server
        - -dev
        - -dev-root-token-id=root-token
        - -dev-listen-address=0.0.0.0:8200
        livenessProbe:
          httpGet:
            path: /v1/sys/health
            port: 8200
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /v1/sys/health
            port: 8200
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: vault-config
        configMap:
          name: vault-config
      - name: tmp
        emptyDir: {}
  volumeClaimTemplates:
  - metadata:
      name: vault-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: vault
  namespace: encryption-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: vault-config
  namespace: encryption-system
data:
  vault.hcl: |
    ui = true
    
    storage "file" {
      path = "/vault/data"
    }
    
    listener "tcp" {
      address = "0.0.0.0:8200"
      tls_disable = 1
    }
    
    api_addr = "http://127.0.0.1:8200"
    cluster_addr = "http://127.0.0.1:8201"
---
apiVersion: v1
kind: Service
metadata:
  name: vault
  namespace: encryption-system
  labels:
    app: vault
spec:
  ports:
  - port: 8200
    targetPort: 8200
    name: vault
  selector:
    app: vault
---
# Tenant Encryption Key Manager
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-key-manager
  namespace: encryption-system
  labels:
    app: tenant-key-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-key-manager
  template:
    metadata:
      labels:
        app: tenant-key-manager
    spec:
      serviceAccountName: tenant-key-manager
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: key-manager
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes hvac cryptography
          cat > /app/key_manager.py << 'EOF'
          #!/usr/bin/env python3
          import os
          import json
          import base64
          import hvac
          from flask import Flask, request, jsonify
          from kubernetes import client, config
          from cryptography.fernet import Fernet
          from cryptography.hazmat.primitives import hashes
          from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
          
          app = Flask(__name__)
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          
          # Initialize Vault client
          vault_client = hvac.Client(url='http://vault.encryption-system.svc.cluster.local:8200')
          vault_client.token = 'root-token'  # In production, use proper authentication
          
          @app.route('/create-tenant-keys', methods=['POST'])
          def create_tenant_keys():
              data = request.get_json()
              tenant_id = data.get('tenant_id')
              
              if not tenant_id:
                  return jsonify({'error': 'tenant_id required'}), 400
              
              try:
                  # Generate encryption key for tenant
                  encryption_key = Fernet.generate_key()
                  
                  # Generate database encryption key
                  db_key = Fernet.generate_key()
                  
                  # Generate S3 encryption key
                  s3_key = Fernet.generate_key()
                  
                  # Store keys in Vault
                  vault_client.secrets.kv.v2.create_or_update_secret(
                      path=f'tenant-keys/{tenant_id}',
                      secret={
                          'encryption_key': encryption_key.decode(),
                          'database_key': db_key.decode(),
                          's3_key': s3_key.decode(),
                          'created_at': str(time.time())
                      }
                  )
                  
                  # Create Kubernetes secret for tenant
                  secret_data = {
                      'encryption-key': base64.b64encode(encryption_key).decode(),
                      'database-key': base64.b64encode(db_key).decode(),
                      's3-key': base64.b64encode(s3_key).decode()
                  }
                  
                  secret = client.V1Secret(
                      metadata=client.V1ObjectMeta(
                          name=f'tenant-{tenant_id}-encryption-keys',
                          namespace=f'tenant-{tenant_id}',
                          annotations={
                              'encryption.architrave.com/managed': 'true',
                              'encryption.architrave.com/tenant-id': tenant_id
                          }
                      ),
                      type='Opaque',
                      data=secret_data
                  )
                  
                  k8s_client.create_namespaced_secret(
                      namespace=f'tenant-{tenant_id}',
                      body=secret
                  )
                  
                  return jsonify({
                      'status': 'success',
                      'tenant_id': tenant_id,
                      'keys_created': ['encryption_key', 'database_key', 's3_key']
                  })
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/rotate-tenant-keys', methods=['POST'])
          def rotate_tenant_keys():
              data = request.get_json()
              tenant_id = data.get('tenant_id')
              key_type = data.get('key_type', 'all')
              
              if not tenant_id:
                  return jsonify({'error': 'tenant_id required'}), 400
              
              try:
                  # Get existing keys
                  existing_keys = vault_client.secrets.kv.v2.read_secret_version(
                      path=f'tenant-keys/{tenant_id}'
                  )['data']['data']
                  
                  # Generate new keys based on type
                  if key_type == 'all' or key_type == 'encryption':
                      existing_keys['encryption_key'] = Fernet.generate_key().decode()
                  
                  if key_type == 'all' or key_type == 'database':
                      existing_keys['database_key'] = Fernet.generate_key().decode()
                  
                  if key_type == 'all' or key_type == 's3':
                      existing_keys['s3_key'] = Fernet.generate_key().decode()
                  
                  existing_keys['rotated_at'] = str(time.time())
                  
                  # Update Vault
                  vault_client.secrets.kv.v2.create_or_update_secret(
                      path=f'tenant-keys/{tenant_id}',
                      secret=existing_keys
                  )
                  
                  # Update Kubernetes secret
                  secret_data = {
                      'encryption-key': base64.b64encode(existing_keys['encryption_key'].encode()).decode(),
                      'database-key': base64.b64encode(existing_keys['database_key'].encode()).decode(),
                      's3-key': base64.b64encode(existing_keys['s3_key'].encode()).decode()
                  }
                  
                  # Patch the existing secret
                  k8s_client.patch_namespaced_secret(
                      name=f'tenant-{tenant_id}-encryption-keys',
                      namespace=f'tenant-{tenant_id}',
                      body={'data': secret_data}
                  )
                  
                  return jsonify({
                      'status': 'success',
                      'tenant_id': tenant_id,
                      'rotated_keys': [key_type] if key_type != 'all' else ['encryption_key', 'database_key', 's3_key']
                  })
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/get-tenant-key', methods=['GET'])
          def get_tenant_key():
              tenant_id = request.args.get('tenant_id')
              key_type = request.args.get('key_type')
              
              if not tenant_id or not key_type:
                  return jsonify({'error': 'tenant_id and key_type required'}), 400
              
              try:
                  keys = vault_client.secrets.kv.v2.read_secret_version(
                      path=f'tenant-keys/{tenant_id}'
                  )['data']['data']
                  
                  if key_type not in keys:
                      return jsonify({'error': 'Key type not found'}), 404
                  
                  return jsonify({
                      'tenant_id': tenant_id,
                      'key_type': key_type,
                      'key': keys[key_type]
                  })
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          @app.route('/delete-tenant-keys', methods=['DELETE'])
          def delete_tenant_keys():
              tenant_id = request.args.get('tenant_id')
              
              if not tenant_id:
                  return jsonify({'error': 'tenant_id required'}), 400
              
              try:
                  # Delete from Vault
                  vault_client.secrets.kv.v2.delete_metadata_and_all_versions(
                      path=f'tenant-keys/{tenant_id}'
                  )
                  
                  # Delete Kubernetes secret
                  k8s_client.delete_namespaced_secret(
                      name=f'tenant-{tenant_id}-encryption-keys',
                      namespace=f'tenant-{tenant_id}'
                  )
                  
                  return jsonify({
                      'status': 'success',
                      'tenant_id': tenant_id,
                      'action': 'deleted'
                  })
                  
              except Exception as e:
                  return jsonify({'error': str(e)}), 500
          
          if __name__ == '__main__':
              # Enable KV secrets engine in Vault
              try:
                  vault_client.sys.enable_secrets_engine(
                      backend_type='kv',
                      path='tenant-keys',
                      options={'version': '2'}
                  )
              except:
                  pass  # Already enabled
              
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/key_manager.py
        ports:
        - containerPort: 8080
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
        env:
        - name: VAULT_ADDR
          value: "http://vault.encryption-system.svc.cluster.local:8200"
        - name: VAULT_TOKEN
          value: "root-token"
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-key-manager
  namespace: encryption-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: tenant-key-manager
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create", "get", "list", "update", "patch", "delete"]
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: tenant-key-manager
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: tenant-key-manager
subjects:
- kind: ServiceAccount
  name: tenant-key-manager
  namespace: encryption-system
---
apiVersion: v1
kind: Service
metadata:
  name: tenant-key-manager
  namespace: encryption-system
  labels:
    app: tenant-key-manager
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: http
  selector:
    app: tenant-key-manager
---
# Secret Rotation CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: secret-rotation
  namespace: encryption-system
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: secret-rotation
          securityContext:
            runAsNonRoot: true
            runAsUser: 65534
            runAsGroup: 65534
            fsGroup: 65534
            seccompProfile:
              type: RuntimeDefault
          containers:
          - name: rotation
            image: python:3.11-alpine
            command: ["/bin/sh", "-c"]
            args:
            - |
              pip install kubernetes requests
              cat > /app/rotate_secrets.py << 'EOF'
              #!/usr/bin/env python3
              import requests
              from kubernetes import client, config
              
              # Load Kubernetes config
              try:
                  config.load_incluster_config()
              except:
                  config.load_kube_config()
              
              k8s_client = client.CoreV1Api()
              
              # Get all tenant namespaces
              namespaces = k8s_client.list_namespace()
              tenant_namespaces = [ns.metadata.name for ns in namespaces.items 
                                 if ns.metadata.name.startswith('tenant-')]
              
              # Rotate keys for each tenant
              for namespace in tenant_namespaces:
                  tenant_id = namespace.replace('tenant-', '')
                  
                  try:
                      response = requests.post(
                          'http://tenant-key-manager.encryption-system.svc.cluster.local:8080/rotate-tenant-keys',
                          json={'tenant_id': tenant_id, 'key_type': 'all'},
                          timeout=30
                      )
                      
                      if response.status_code == 200:
                          print(f"Successfully rotated keys for tenant: {tenant_id}")
                      else:
                          print(f"Failed to rotate keys for tenant: {tenant_id}")
                  
                  except Exception as e:
                      print(f"Error rotating keys for tenant {tenant_id}: {e}")
              EOF
              
              python /app/rotate_secrets.py
            securityContext:
              allowPrivilegeEscalation: false
              readOnlyRootFilesystem: true
              capabilities:
                drop: ["ALL"]
            resources:
              requests:
                memory: "128Mi"
                cpu: "100m"
              limits:
                memory: "256Mi"
                cpu: "200m"
            volumeMounts:
            - name: tmp
              mountPath: /tmp
            - name: app
              mountPath: /app
          volumes:
          - name: tmp
            emptyDir: {}
          - name: app
            emptyDir: {}
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: secret-rotation
  namespace: encryption-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: secret-rotation
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: secret-rotation
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: secret-rotation
subjects:
- kind: ServiceAccount
  name: secret-rotation
  namespace: encryption-system
