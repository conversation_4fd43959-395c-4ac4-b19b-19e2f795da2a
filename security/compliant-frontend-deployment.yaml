apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-fresh-test-frontend
  namespace: tenant-fresh-test
  labels:
    app: tenant-fresh-test-frontend
    tenant.architrave.io/tenant-id: fresh-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tenant-fresh-test-frontend
  template:
    metadata:
      labels:
        app: tenant-fresh-test-frontend
        tenant.architrave.io/tenant-id: fresh-test
        version: v1
    spec:
      securityContext:
        runAsUser: 101
        runAsGroup: 101
        runAsNonRoot: true
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      serviceAccountName: default
      containers:
      - name: frontend
        image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
        ports:
        - containerPort: 80
          name: http
        - containerPort: 443
          name: https
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 101
          runAsGroup: 101
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop: ["ALL"]
            add: ["NET_BIND_SERVICE"]
        env:
        - name: TENANT_ID
          value: "fresh-test"
        - name: BACKEND_SERVICE
          value: "webapp"
        - name: BACKEND_PORT
          value: "8080"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5
