---
# Runtime Security Monitoring with Falco
apiVersion: v1
kind: Namespace
metadata:
  name: runtime-security
  labels:
    pod-security.kubernetes.io/enforce: privileged  # <PERSON><PERSON><PERSON> needs privileged access
    pod-security.kubernetes.io/audit: privileged
    pod-security.kubernetes.io/warn: privileged
---
# Falco DaemonSet for Runtime Security Monitoring
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: falco
  namespace: runtime-security
  labels:
    app: falco
spec:
  selector:
    matchLabels:
      app: falco
  template:
    metadata:
      labels:
        app: falco
    spec:
      serviceAccountName: falco
      hostNetwork: true
      hostPID: true
      tolerations:
      - effect: NoSchedule
        key: node-role.kubernetes.io/master
      - effect: NoSchedule
        key: node-role.kubernetes.io/control-plane
      containers:
      - name: falco
        image: falcosecurity/falco:latest
        args:
        - /usr/bin/falco
        - --cri=/run/containerd/containerd.sock
        - --k8s-api=https://kubernetes.default.svc.cluster.local
        - --k8s-api-cert=/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        - --k8s-api-token=/var/run/secrets/kubernetes.io/serviceaccount/token
        - -pk
        securityContext:
          privileged: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - mountPath: /host/var/run/docker.sock
          name: docker-socket
          readOnly: true
        - mountPath: /host/run/containerd/containerd.sock
          name: containerd-socket
          readOnly: true
        - mountPath: /host/dev
          name: dev-fs
          readOnly: true
        - mountPath: /host/proc
          name: proc-fs
          readOnly: true
        - mountPath: /host/boot
          name: boot-fs
          readOnly: true
        - mountPath: /host/lib/modules
          name: lib-modules
          readOnly: true
        - mountPath: /host/usr
          name: usr-fs
          readOnly: true
        - mountPath: /host/etc
          name: etc-fs
          readOnly: true
        - mountPath: /etc/falco
          name: falco-config
        env:
        - name: FALCO_K8S_NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8765
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8765
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: docker-socket
        hostPath:
          path: /var/run/docker.sock
      - name: containerd-socket
        hostPath:
          path: /run/containerd/containerd.sock
      - name: dev-fs
        hostPath:
          path: /dev
      - name: proc-fs
        hostPath:
          path: /proc
      - name: boot-fs
        hostPath:
          path: /boot
      - name: lib-modules
        hostPath:
          path: /lib/modules
      - name: usr-fs
        hostPath:
          path: /usr
      - name: etc-fs
        hostPath:
          path: /etc
      - name: falco-config
        configMap:
          name: falco-config
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: falco
  namespace: runtime-security
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: falco
rules:
- apiGroups: [""]
  resources: ["nodes", "namespaces", "pods", "replicationcontrollers", "services", "events"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["daemonsets", "deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: falco
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: falco
subjects:
- kind: ServiceAccount
  name: falco
  namespace: runtime-security
---
# Falco Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-config
  namespace: runtime-security
data:
  falco.yaml: |
    rules_file:
      - /etc/falco/falco_rules.yaml
      - /etc/falco/falco_rules.local.yaml
      - /etc/falco/k8s_audit_rules.yaml
      - /etc/falco/rules.d
    
    time_format_iso_8601: true
    json_output: true
    json_include_output_property: true
    json_include_tags_property: true
    
    log_stderr: true
    log_syslog: true
    log_level: info
    
    priority: debug
    
    buffered_outputs: false
    
    syscall_event_drops:
      actions:
        - log
        - alert
      rate: 0.03333
      max_burst: 1000
    
    outputs:
      rate: 1
      max_burst: 1000
    
    syslog_output:
      enabled: true
    
    file_output:
      enabled: false
    
    stdout_output:
      enabled: true
    
    webserver:
      enabled: true
      listen_port: 8765
      k8s_healthz_endpoint: /healthz
      ssl_enabled: false
    
    grpc:
      enabled: false
    
    grpc_output:
      enabled: false
  
  falco_rules.local.yaml: |
    # Custom rules for multi-tenant environment
    
    - rule: Unauthorized Process in Tenant Container
      desc: Detect unauthorized processes in tenant containers
      condition: >
        spawned_process and
        k8s.ns.name startswith "tenant-" and
        not proc.name in (php-fpm, nginx, mysql, redis, node, python, java)
      output: >
        Unauthorized process in tenant container
        (user=%user.name command=%proc.cmdline container=%container.name
        namespace=%k8s.ns.name pod=%k8s.pod.name)
      priority: WARNING
      tags: [tenant, process, security]
    
    - rule: Tenant Container Privilege Escalation
      desc: Detect privilege escalation attempts in tenant containers
      condition: >
        spawned_process and
        k8s.ns.name startswith "tenant-" and
        (proc.name in (sudo, su, doas) or
         proc.args contains "chmod +s" or
         proc.args contains "setuid")
      output: >
        Privilege escalation attempt in tenant container
        (user=%user.name command=%proc.cmdline container=%container.name
        namespace=%k8s.ns.name pod=%k8s.pod.name)
      priority: CRITICAL
      tags: [tenant, privilege_escalation, security]
    
    - rule: Tenant Network Connection to External
      desc: Detect unexpected external network connections from tenant containers
      condition: >
        outbound and
        k8s.ns.name startswith "tenant-" and
        not fd.sip in (10.0.0.0/8, **********/12, ***********/16) and
        not fd.sport in (80, 443, 53, 3306)
      output: >
        Unexpected external network connection from tenant
        (connection=%fd.name container=%container.name
        namespace=%k8s.ns.name pod=%k8s.pod.name)
      priority: WARNING
      tags: [tenant, network, security]
    
    - rule: Tenant File System Modification
      desc: Detect unauthorized file system modifications in tenant containers
      condition: >
        open_write and
        k8s.ns.name startswith "tenant-" and
        fd.name startswith "/etc" and
        not proc.name in (php-fpm, nginx, mysql)
      output: >
        Unauthorized file system modification in tenant container
        (file=%fd.name user=%user.name command=%proc.cmdline
        container=%container.name namespace=%k8s.ns.name pod=%k8s.pod.name)
      priority: WARNING
      tags: [tenant, filesystem, security]
    
    - rule: Cross-Tenant Communication Attempt
      desc: Detect attempts to communicate between different tenant namespaces
      condition: >
        outbound and
        k8s.ns.name startswith "tenant-" and
        fd.sip_name startswith "tenant-" and
        k8s.ns.name != fd.sip_name
      output: >
        Cross-tenant communication attempt detected
        (source_namespace=%k8s.ns.name target=%fd.sip_name
        container=%container.name pod=%k8s.pod.name)
      priority: CRITICAL
      tags: [tenant, isolation, security]
---
# Falco Sidekick for Alert Processing
apiVersion: apps/v1
kind: Deployment
metadata:
  name: falco-sidekick
  namespace: runtime-security
  labels:
    app: falco-sidekick
spec:
  replicas: 1
  selector:
    matchLabels:
      app: falco-sidekick
  template:
    metadata:
      labels:
        app: falco-sidekick
    spec:
      serviceAccountName: falco-sidekick
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: falco-sidekick
        image: falcosecurity/falco-sidekick:latest
        ports:
        - containerPort: 2801
          name: http
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        env:
        - name: WEBUI_URL
          value: "http://falco-sidekick-ui.runtime-security.svc.cluster.local:2802"
        - name: SLACK_WEBHOOKURL
          valueFrom:
            secretKeyRef:
              name: falco-sidekick-config
              key: slack-webhook-url
              optional: true
        - name: PROMETHEUS_EXTRA_LABELS
          value: "tenant_security=true"
        livenessProbe:
          httpGet:
            path: /ping
            port: 2801
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /ping
            port: 2801
          initialDelaySeconds: 5
          periodSeconds: 10
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: falco-sidekick
  namespace: runtime-security
---
apiVersion: v1
kind: Service
metadata:
  name: falco-sidekick
  namespace: runtime-security
  labels:
    app: falco-sidekick
spec:
  ports:
  - port: 2801
    targetPort: 2801
    name: http
  selector:
    app: falco-sidekick
---
# Security Event Processor
apiVersion: apps/v1
kind: Deployment
metadata:
  name: security-event-processor
  namespace: runtime-security
  labels:
    app: security-event-processor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: security-event-processor
  template:
    metadata:
      labels:
        app: security-event-processor
    spec:
      serviceAccountName: security-event-processor
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        runAsGroup: 65534
        fsGroup: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: processor
        image: python:3.11-alpine
        command: ["/bin/sh", "-c"]
        args:
        - |
          pip install flask kubernetes requests prometheus_client
          cat > /app/processor.py << 'EOF'
          #!/usr/bin/env python3
          import json
          import time
          from flask import Flask, request
          from kubernetes import client, config
          from prometheus_client import Counter, Histogram, start_http_server
          
          app = Flask(__name__)
          
          # Prometheus metrics
          security_events = Counter('security_events_total', 'Total security events', ['tenant', 'rule', 'priority'])
          event_processing_time = Histogram('security_event_processing_seconds', 'Time spent processing security events')
          
          # Load Kubernetes config
          try:
              config.load_incluster_config()
          except:
              config.load_kube_config()
          
          k8s_client = client.CoreV1Api()
          
          @app.route('/webhook', methods=['POST'])
          def process_security_event():
              with event_processing_time.time():
                  event = request.get_json()
                  
                  # Extract tenant information
                  tenant_id = extract_tenant_id(event)
                  rule_name = event.get('rule', 'unknown')
                  priority = event.get('priority', 'INFO')
                  
                  # Update metrics
                  security_events.labels(tenant=tenant_id, rule=rule_name, priority=priority).inc()
                  
                  # Process based on priority
                  if priority in ['CRITICAL', 'ERROR']:
                      handle_critical_event(event, tenant_id)
                  elif priority == 'WARNING':
                      handle_warning_event(event, tenant_id)
                  
                  return {'status': 'processed'}
          
          def extract_tenant_id(event):
              output_fields = event.get('output_fields', {})
              namespace = output_fields.get('k8s.ns.name', '')
              
              if namespace.startswith('tenant-'):
                  return namespace.replace('tenant-', '')
              
              return 'unknown'
          
          def handle_critical_event(event, tenant_id):
              # Create Kubernetes event for critical security issues
              try:
                  event_obj = client.CoreV1Event(
                      metadata=client.V1ObjectMeta(
                          name=f"security-alert-{int(time.time())}",
                          namespace=f"tenant-{tenant_id}" if tenant_id != 'unknown' else 'default'
                      ),
                      involved_object=client.V1ObjectReference(
                          kind="Namespace",
                          name=f"tenant-{tenant_id}" if tenant_id != 'unknown' else 'default'
                      ),
                      reason="SecurityAlert",
                      message=f"CRITICAL: {event.get('output', 'Security event detected')}",
                      type="Warning",
                      first_timestamp=time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                      last_timestamp=time.strftime('%Y-%m-%dT%H:%M:%SZ'),
                      count=1
                  )
                  
                  k8s_client.create_namespaced_event(
                      namespace=f"tenant-{tenant_id}" if tenant_id != 'unknown' else 'default',
                      body=event_obj
                  )
              except Exception as e:
                  print(f"Failed to create Kubernetes event: {e}")
          
          def handle_warning_event(event, tenant_id):
              # Log warning events
              print(f"WARNING for tenant {tenant_id}: {event.get('output', 'Security event detected')}")
          
          if __name__ == '__main__':
              # Start Prometheus metrics server
              start_http_server(8000)
              
              # Start Flask app
              app.run(host='0.0.0.0', port=8080)
          EOF
          
          python /app/processor.py
        ports:
        - containerPort: 8080
          name: webhook
        - containerPort: 8000
          name: metrics
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop: ["ALL"]
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: app
          mountPath: /app
      volumes:
      - name: tmp
        emptyDir: {}
      - name: app
        emptyDir: {}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: security-event-processor
  namespace: runtime-security
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: security-event-processor
rules:
- apiGroups: [""]
  resources: ["events"]
  verbs: ["create", "get", "list", "watch"]
- apiGroups: [""]
  resources: ["namespaces", "pods"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: security-event-processor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: security-event-processor
subjects:
- kind: ServiceAccount
  name: security-event-processor
  namespace: runtime-security
---
apiVersion: v1
kind: Service
metadata:
  name: security-event-processor
  namespace: runtime-security
  labels:
    app: security-event-processor
spec:
  ports:
  - port: 8080
    targetPort: 8080
    name: webhook
  - port: 8000
    targetPort: 8000
    name: metrics
  selector:
    app: security-event-processor
