apiVersion: v1
kind: Namespace
metadata:
  name: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted
---
apiVersion: v1
kind: LimitRange
metadata:
  name: tenant-fresh-test-limits
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
      ephemeral-storage: "1Gi"
    defaultRequest:
      cpu: "100m"
      memory: "128Mi"
      ephemeral-storage: "100Mi"
    max:
      cpu: "2"
      memory: "4Gi"
      ephemeral-storage: "10Gi"
    min:
      cpu: "50m"
      memory: "64Mi"
      ephemeral-storage: "50Mi"
    type: Container
  - max:
      cpu: "4"
      memory: "8Gi"
    min:
      cpu: "100m"
      memory: "128Mi"
    type: Pod
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-fresh-test-backend-pdb
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-fresh-test-backend
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: tenant-fresh-test-frontend-pdb
  namespace: tenant-fresh-test
  labels:
    tenant.architrave.io/tenant-id: fresh-test
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: tenant-fresh-test-frontend
