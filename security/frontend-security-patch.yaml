apiVersion: apps/v1
kind: Deployment
metadata:
  name: tenant-fresh-test-frontend
  namespace: tenant-fresh-test
spec:
  template:
    spec:
      securityContext:
        runAsUser: 101
        runAsGroup: 101
        runAsNonRoot: true
        fsGroup: 101
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: frontend
        securityContext:
          allowPrivilegeEscalation: false
          runAsUser: 101
          runAsGroup: 101
          runAsNonRoot: true
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
            add:
            - CHOWN
            - DAC_OVERRIDE
            - SETGID
            - SETUID
