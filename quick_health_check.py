#!/usr/bin/env python3
"""
Quick Health Check
Fast verification of system status, database, backend, frontend, nginx
"""

import subprocess
import sys
import time
import tempfile
from datetime import datetime

def run_command(command, timeout=10):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   {details}")

def quick_database_check():
    """Quick database connectivity check."""
    print("\n🔍 DATABASE QUICK CHECK")
    print("=" * 30)
    
    # Create simple test pod
    pod_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: quick-db-test
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "60"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 50m
        memory: 128Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(pod_yaml)
        pod_file = f.name
    
    try:
        # Create and test
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if success:
            time.sleep(5)
            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/quick-db-test --timeout=30s")
            if success:
                # Test database
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
                success, stdout, stderr = run_command(
                    f"kubectl exec quick-db-test -- mysql -h {db_host} -P 3306 -u admin -e 'SELECT 1 as test;'", 10)
                
                if success:
                    print_result("Database Connection", True, "Aurora Serverless accessible")
                    
                    # Test SELECT statements
                    queries = [
                        ("SELECT NOW();", "Current Time"),
                        ("SHOW DATABASES;", "Show Databases"),
                        ("USE architrave; SHOW TABLES;", "Show Tables")
                    ]
                    
                    for query, name in queries:
                        success, stdout, stderr = run_command(
                            f"kubectl exec quick-db-test -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\"", 8)
                        print_result(f"SELECT - {name}", success, "Query executed" if success else "Query failed")
                else:
                    print_result("Database Connection", False, "Cannot connect to Aurora")
            else:
                print_result("Database Test Pod", False, "Pod not ready")
        else:
            print_result("Database Test Setup", False, "Cannot create test pod")
    finally:
        run_command("kubectl delete pod quick-db-test --ignore-not-found=true")
        import os
        os.unlink(pod_file)

def quick_tenant_check():
    """Quick tenant status check."""
    print("\n🔍 TENANT STATUS QUICK CHECK")
    print("=" * 35)
    
    # Get tenant count
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant- | wc -l", 8)
    if success:
        tenant_count = stdout
        print_result("Tenant Namespaces", True, f"{tenant_count} tenant namespaces found")
        
        # Get first few tenants
        success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant- | head -3", 8)
        if success and stdout:
            tenants = [line.split()[0] for line in stdout.split('\n') if line.strip()]
            
            for ns in tenants:
                tenant_id = ns.replace('tenant-', '')
                
                # Quick pod check
                success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers --request-timeout=3s", 5)
                if success:
                    if stdout:
                        pods = [line for line in stdout.split('\n') if line.strip()]
                        running = len([line for line in pods if 'Running' in line])
                        total = len(pods)
                        print_result(f"Tenant {tenant_id} Pods", running > 0, f"{running}/{total} pods running")
                    else:
                        print_result(f"Tenant {tenant_id} Pods", False, "No pods found")
                else:
                    print_result(f"Tenant {tenant_id} Pods", False, "Cannot check pods")
    else:
        print_result("Tenant Namespaces", False, "Cannot get tenant namespaces")

def quick_component_check():
    """Quick component check."""
    print("\n🔍 COMPONENT QUICK CHECK")
    print("=" * 30)
    
    # Get first tenant
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=5s | grep tenant- | head -1", 8)
    if success and stdout:
        ns = stdout.split()[0]
        tenant_id = ns.replace('tenant-', '')
        
        print(f"Checking tenant: {tenant_id}")
        
        # Check deployments
        components = ['backend', 'frontend', 'nginx', 'rabbitmq']
        for component in components:
            success, stdout, stderr = run_command(f"kubectl get deployment -n {ns} --no-headers --request-timeout=3s | grep {component}", 5)
            if success and stdout:
                deployment_name = stdout.split()[0]
                print_result(f"{component.title()} Deployment", True, f"Found: {deployment_name}")
            else:
                print_result(f"{component.title()} Deployment", False, "Not found")
        
        # Check services
        success, stdout, stderr = run_command(f"kubectl get services -n {ns} --no-headers --request-timeout=3s", 5)
        if success:
            service_count = len([line for line in stdout.split('\n') if line.strip()])
            print_result("Services", service_count > 0, f"{service_count} services found")
        else:
            print_result("Services", False, "Cannot check services")
    else:
        print_result("Component Check", False, "No tenants found for component check")

def quick_feature_flags_check():
    """Quick feature flags check."""
    print("\n🔍 FEATURE FLAGS QUICK CHECK")
    print("=" * 35)
    
    # Check onboarding script
    success, stdout, stderr = run_command("grep -c 'skip-' tenant-management/scripts/advanced_tenant_onboard.py")
    if success:
        flag_count = stdout
        print_result("Onboarding Feature Flags", True, f"{flag_count} skip flags found")
    else:
        print_result("Onboarding Feature Flags", False, "Cannot check flags")
    
    # Check if script has ECR images
    success, stdout, stderr = run_command("grep -c '545009857703.dkr.ecr.eu-central-1.amazonaws.com' tenant-management/scripts/advanced_tenant_onboard.py")
    if success:
        ecr_count = stdout
        print_result("ECR Images in Script", True, f"{ecr_count} ECR image references found")
    else:
        print_result("ECR Images in Script", False, "No ECR images found in script")

def quick_issues_check():
    """Quick issues check."""
    print("\n🔍 ISSUES QUICK CHECK")
    print("=" * 25)
    
    issues = []
    
    # Check kubectl
    success, stdout, stderr = run_command("kubectl version --client", 5)
    if success:
        print_result("kubectl Client", True, "Working")
    else:
        issues.append("kubectl client issues")
        print_result("kubectl Client", False, "Not working")
    
    # Check cluster
    success, stdout, stderr = run_command("kubectl cluster-info --request-timeout=3s", 5)
    if success:
        print_result("Cluster Connectivity", True, "Working")
    else:
        issues.append("Cluster connectivity issues")
        print_result("Cluster Connectivity", False, "Connection issues")
    
    # Check for pending pods
    success, stdout, stderr = run_command("kubectl get pods --all-namespaces --field-selector=status.phase=Pending --no-headers --request-timeout=5s | wc -l", 8)
    if success:
        pending_count = stdout
        if int(pending_count) > 10:
            issues.append(f"Many pending pods ({pending_count})")
            print_result("Pending Pods", False, f"{pending_count} pending pods (high)")
        else:
            print_result("Pending Pods", True, f"{pending_count} pending pods (normal)")
    else:
        print_result("Pending Pods Check", False, "Cannot check pending pods")
    
    return issues

def main():
    """Main quick health check."""
    print("🔍 QUICK HEALTH CHECK")
    print("=" * 30)
    print(f"Started at: {datetime.now()}")
    
    # Run quick checks
    issues = quick_issues_check()
    quick_database_check()
    quick_tenant_check()
    quick_component_check()
    quick_feature_flags_check()
    
    # Summary
    print("\n🎯 QUICK HEALTH SUMMARY")
    print("=" * 30)
    
    if issues:
        print("❌ ISSUES FOUND:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ NO MAJOR ISSUES DETECTED")
    
    print(f"\n📊 Quick health check completed at: {datetime.now()}")
    
    return len(issues)

if __name__ == "__main__":
    sys.exit(main())
