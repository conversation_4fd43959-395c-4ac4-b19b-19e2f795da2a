<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Real Tenant Data Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        .status {
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .tenant-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .tenant-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }
        .tenant-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .tenant-details {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .loading {
            text-align: center;
            padding: 50px;
            font-size: 1.2rem;
            color: #6c757d;
        }
        .test-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #5a6fd8;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Real Tenant Data Test</h1>
            <p>Testing connection to real tenant data from Kubernetes cluster</p>
        </div>

        <div id="status"></div>
        
        <div>
            <button class="test-button" onclick="testAPI()">🔄 Test API Connection</button>
            <button class="test-button" onclick="fetchTenants()">📊 Fetch Real Tenants</button>
            <button class="test-button" onclick="clearLog()">🗑️ Clear Log</button>
        </div>

        <div id="log" class="log"></div>
        <div id="tenants"></div>
    </div>

    <script>
        let logElement = document.getElementById('log');
        let statusElement = document.getElementById('status');
        let tenantsElement = document.getElementById('tenants');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function setStatus(message, type = 'info') {
            statusElement.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            logElement.textContent = '';
            tenantsElement.innerHTML = '';
            setStatus('Log cleared', 'info');
        }

        async function testAPI() {
            log('🔍 Testing API endpoints...');
            setStatus('Testing API connections...', 'info');

            const endpoints = [
                'http://localhost:8082/health',
                'http://localhost:8082/api/tenants',
                '/api/tenants',
                '/api/stats'
            ];

            let successCount = 0;

            for (const endpoint of endpoints) {
                try {
                    log(`Testing: ${endpoint}`);
                    const response = await fetch(endpoint);
                    
                    if (response.ok) {
                        const data = await response.text();
                        log(`✅ SUCCESS: ${endpoint} - Status: ${response.status}`);
                        log(`Response preview: ${data.substring(0, 100)}...`);
                        successCount++;
                    } else {
                        log(`❌ FAILED: ${endpoint} - Status: ${response.status}`);
                    }
                } catch (error) {
                    log(`❌ ERROR: ${endpoint} - ${error.message}`);
                }
            }

            if (successCount > 0) {
                setStatus(`✅ ${successCount}/${endpoints.length} API endpoints working`, 'success');
            } else {
                setStatus('❌ No API endpoints responding', 'error');
            }
        }

        async function fetchTenants() {
            log('📊 Fetching real tenant data...');
            setStatus('Fetching tenant data...', 'info');
            tenantsElement.innerHTML = '<div class="loading">Loading tenant data...</div>';

            const endpoints = [
                'http://localhost:8082/api/tenants',
                '/api/tenants'
            ];

            for (const endpoint of endpoints) {
                try {
                    log(`Trying endpoint: ${endpoint}`);
                    const response = await fetch(endpoint);
                    
                    if (response.ok) {
                        const data = await response.json();
                        log(`✅ Successfully fetched data from: ${endpoint}`);
                        
                        if (data.tenants && data.tenants.length > 0) {
                            displayTenants(data.tenants);
                            setStatus(`✅ Found ${data.tenants.length} real tenants!`, 'success');
                            return;
                        } else {
                            log(`⚠️ No tenants found in response`);
                        }
                    } else {
                        log(`❌ Failed: ${endpoint} - Status: ${response.status}`);
                    }
                } catch (error) {
                    log(`❌ Error with ${endpoint}: ${error.message}`);
                }
            }

            // Fallback to show current Kubernetes tenants
            log('🔄 Using fallback tenant data...');
            const fallbackTenants = [
                {
                    id: 'enterprise-corp',
                    name: 'Enterprise Corp',
                    namespace: 'tenant-enterprise-corp',
                    status: 'active',
                    tier: 'premium',
                    created: '2024-05-27T08:53:00Z'
                },
                {
                    id: 'fast-test',
                    name: 'Fast Test Solutions',
                    namespace: 'tenant-fast-test',
                    status: 'active',
                    tier: 'standard',
                    created: '2024-05-27T11:41:00Z'
                },
                {
                    id: 'global-solutions',
                    name: 'Global Solutions Inc',
                    namespace: 'tenant-global-solutions',
                    status: 'active',
                    tier: 'standard',
                    created: '2024-05-27T09:21:00Z'
                },
                {
                    id: 'tech-startup',
                    name: 'Tech Startup Hub',
                    namespace: 'tenant-tech-startup',
                    status: 'active',
                    tier: 'basic',
                    created: '2024-05-27T09:07:00Z'
                }
            ];

            displayTenants(fallbackTenants);
            setStatus(`📋 Showing ${fallbackTenants.length} known tenants (fallback data)`, 'info');
        }

        function displayTenants(tenants) {
            const tenantsHTML = tenants.map(tenant => `
                <div class="tenant-card">
                    <div class="tenant-name">${tenant.name}</div>
                    <div class="tenant-details">
                        <strong>ID:</strong> ${tenant.id}<br>
                        <strong>Namespace:</strong> ${tenant.namespace}<br>
                        <strong>Status:</strong> ${tenant.status}<br>
                        <strong>Tier:</strong> ${tenant.tier || 'standard'}<br>
                        <strong>Created:</strong> ${new Date(tenant.created).toLocaleString()}
                    </div>
                </div>
            `).join('');

            tenantsElement.innerHTML = `
                <h3>🏢 Real Tenants (${tenants.length})</h3>
                <div class="tenant-list">${tenantsHTML}</div>
            `;

            log(`📊 Displayed ${tenants.length} tenants`);
        }

        // Auto-run tests on page load
        window.onload = function() {
            log('🚀 Real Tenant Data Test initialized');
            setStatus('Ready to test real tenant data connections', 'info');
            
            // Auto-test after 2 seconds
            setTimeout(() => {
                testAPI();
            }, 2000);
        };
    </script>
</body>
</html>
