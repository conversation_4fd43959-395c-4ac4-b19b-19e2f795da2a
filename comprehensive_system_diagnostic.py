#!/usr/bin/env python3
"""
Comprehensive System Diagnostic
Identifies and analyzes all current issues across tenant management infrastructure
"""

import subprocess
import sys
import time
import tempfile
import json
from datetime import datetime

def run_command(command, timeout=15):
    """Run a command with timeout and detailed output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subheader(title):
    """Print formatted subheader."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def print_diagnostic(test_name, success, details="", error_details="", kubectl_cmd=""):
    """Print detailed diagnostic result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if kubectl_cmd:
        print(f"   Command: {kubectl_cmd}")
    if details:
        print(f"   Details: {details}")
    if error_details and not success:
        print(f"   Error: {error_details}")
    if not success:
        print(f"   Root Cause: Analysis needed")

def diagnostic1_database_issues():
    """Diagnostic 1: Database Issues Analysis"""
    print_header("DIAGNOSTIC 1: DATABASE ISSUES ANALYSIS")

    # Create database diagnostic pod
    db_diagnostic_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-diagnostic-pod
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 200m
        memory: 512Mi
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_diagnostic_yaml)
        pod_file = f.name

    results = []

    try:
        print_subheader("Creating Database Diagnostic Pod")
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if success:
            print_diagnostic("Database Diagnostic Pod Creation", True, "Pod created successfully", kubectl_cmd="kubectl apply -f db-diagnostic.yaml")
            time.sleep(10)

            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/db-diagnostic-pod --timeout=45s")
            if success:
                print_diagnostic("Database Diagnostic Pod Ready", True, "Pod is ready for diagnostics", kubectl_cmd="kubectl wait --for=condition=ready pod/db-diagnostic-pod")

                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"

                print_subheader("Aurora Serverless 'architrave' Database Diagnostics")

                # Comprehensive database diagnostics
                diagnostic_queries = [
                    ("SELECT 1 as connectivity_test;", "Basic Connectivity Test"),
                    ("SHOW DATABASES;", "Database Enumeration"),
                    ("USE architrave; SHOW TABLES;", "Architrave Tables List"),
                    ("USE architrave; SELECT COUNT(*) as total_tables FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave';", "Total Tables Count"),
                    ("USE architrave; DESCRIBE tenant_config;", "Tenant Config Structure"),
                    ("USE architrave; SELECT COUNT(*) as tenant_count FROM tenant_config;", "Tenant Count"),
                    ("USE architrave; SELECT * FROM tenant_config;", "All Tenant Configurations"),
                    ("USE architrave; SELECT TABLE_NAME, TABLE_ROWS FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave' AND TABLE_ROWS > 0 ORDER BY TABLE_ROWS DESC LIMIT 10;", "Tables with Most Data"),
                    ("USE architrave; SELECT CONSTRAINT_NAME, TABLE_NAME, CONSTRAINT_TYPE FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_SCHEMA='architrave' LIMIT 10;", "Database Constraints"),
                    ("USE architrave; SHOW PROCESSLIST;", "Active Database Processes"),
                ]

                for query, test_name in diagnostic_queries:
                    kubectl_cmd = f"kubectl exec db-diagnostic-pod -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\""
                    success, stdout, stderr = run_command(kubectl_cmd, 20)

                    if success and stdout:
                        print_diagnostic(f"DB QUERY - {test_name}", True, "Query executed successfully", kubectl_cmd=kubectl_cmd)
                        results.append(True)

                        # Show important results
                        if any(keyword in query.lower() for keyword in ['count', 'tenant_config', 'describe']):
                            lines = stdout.split('\n')
                            if len(lines) > 1:
                                print(f"     Result: {lines[1]}")
                                if len(lines) > 2 and 'tenant_config' in query:
                                    for line in lines[2:5]:  # Show up to 3 more lines
                                        if line.strip():
                                            print(f"             {line}")
                    else:
                        print_diagnostic(f"DB QUERY - {test_name}", False, "Query failed", stderr, kubectl_cmd)
                        results.append(False)

                print_subheader("Tenant Database Configuration Verification")

                # Verify all tenants use 'architrave' database
                tenant_db_queries = [
                    ("USE architrave; SELECT tenant_id, tenant_name FROM tenant_config;", "Tenant Database Usage"),
                    ("SHOW DATABASES LIKE 'tenant_%';", "Check for Separate Tenant Databases"),
                    ("USE architrave; SELECT COUNT(DISTINCT TABLE_SCHEMA) as schema_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA LIKE 'tenant_%';", "Tenant-specific Schemas"),
                ]

                for query, test_name in tenant_db_queries:
                    kubectl_cmd = f"kubectl exec db-diagnostic-pod -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\""
                    success, stdout, stderr = run_command(kubectl_cmd, 15)

                    if success:
                        print_diagnostic(f"TENANT DB - {test_name}", True, "Verification completed", kubectl_cmd=kubectl_cmd)
                        results.append(True)
                        lines = stdout.split('\n')
                        if len(lines) > 1:
                            print(f"     Result: {lines[1]}")
                    else:
                        print_diagnostic(f"TENANT DB - {test_name}", False, "Verification failed", stderr, kubectl_cmd)
                        results.append(False)

                print_subheader("Table Accessibility Testing")

                # Test accessibility of key tables
                table_tests = [
                    ("USE architrave; SELECT COUNT(*) FROM tenant_config;", "tenant_config Table"),
                    ("USE architrave; SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave' AND TABLE_NAME='users';", "users Table Existence"),
                    ("USE architrave; SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave' AND TABLE_NAME LIKE '%user%';", "User-related Tables"),
                    ("USE architrave; SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave' AND TABLE_NAME LIKE '%tenant%';", "Tenant-related Tables"),
                ]

                for query, test_name in table_tests:
                    kubectl_cmd = f"kubectl exec db-diagnostic-pod -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\""
                    success, stdout, stderr = run_command(kubectl_cmd, 15)

                    if success:
                        print_diagnostic(f"TABLE ACCESS - {test_name}", True, "Table accessible", kubectl_cmd=kubectl_cmd)
                        results.append(True)
                        lines = stdout.split('\n')
                        if len(lines) > 1:
                            print(f"     Count: {lines[1]}")
                    else:
                        print_diagnostic(f"TABLE ACCESS - {test_name}", False, "Table access failed", stderr, kubectl_cmd)
                        results.append(False)

            else:
                print_diagnostic("Database Diagnostic Pod Ready", False, "Pod not ready", stderr, "kubectl wait --for=condition=ready pod/db-diagnostic-pod")
                results.extend([False] * 17)
        else:
            print_diagnostic("Database Diagnostic Pod Creation", False, "Cannot create pod", stderr, "kubectl apply -f db-diagnostic.yaml")
            results.extend([False] * 17)
    finally:
        run_command("kubectl delete pod db-diagnostic-pod --ignore-not-found=true")
        import os
        os.unlink(pod_file)

    return results

def diagnostic2_frontend_issues():
    """Diagnostic 2: Frontend Issues Analysis"""
    print_header("DIAGNOSTIC 2: FRONTEND ISSUES ANALYSIS")

    results = []

    print_subheader("Getting Tenant Namespaces for Frontend Analysis")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("Frontend Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    print_diagnostic("Frontend Namespaces Discovery", True, f"Found {len(tenant_namespaces)} tenant namespaces", kubectl_cmd=kubectl_cmd)

    print_subheader("Frontend CrashLoopBackOff Diagnosis")

    for ns in tenant_namespaces[:3]:  # Analyze first 3 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Get frontend pods
        kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            pod_info = stdout.split()
            pod_name = pod_info[0]
            pod_status = pod_info[2] if len(pod_info) > 2 else "Unknown"

            print_diagnostic(f"Frontend Pod Status - {tenant_id}", "Running" in pod_status, f"Pod: {pod_name}, Status: {pod_status}", kubectl_cmd=kubectl_cmd)
            results.append("Running" in pod_status)

            # Get detailed pod logs for CrashLoopBackOff analysis
            if "CrashLoopBackOff" in pod_status or "Error" in pod_status:
                kubectl_cmd = f"kubectl logs {pod_name} -n {ns} --tail=10"
                success, logs, stderr = run_command(kubectl_cmd, 10)
                if success:
                    print_diagnostic(f"Frontend Pod Logs - {tenant_id}", False, "CrashLoopBackOff logs retrieved", logs[:200] + "...", kubectl_cmd)

                    # Analyze SSL certificate issues
                    if "architrave.key" in logs or "Permission denied" in logs:
                        print_diagnostic(f"SSL Certificate Issue - {tenant_id}", False, "SSL certificate permission error detected", "Certificate file permission issue", kubectl_cmd)
                        results.append(False)
                    else:
                        results.append(True)
                else:
                    print_diagnostic(f"Frontend Pod Logs - {tenant_id}", False, "Cannot retrieve logs", stderr, kubectl_cmd)
                    results.append(False)
            else:
                results.append(True)

            # Check frontend deployment ECR image
            kubectl_cmd = f"kubectl get deployment -n {ns} -o yaml | grep 'image:.*webapp_dev:2.0.41'"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            print_diagnostic(f"Frontend ECR Image - {tenant_id}", success, "Using correct webapp_dev:2.0.41" if success else "Not using correct ECR image", kubectl_cmd=kubectl_cmd)
            results.append(success)

            # Test frontend service accessibility
            kubectl_cmd = f"kubectl get service -n {ns} | grep frontend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                service_info = stdout.split()
                service_name = service_info[0]
                cluster_ip = service_info[2] if len(service_info) > 2 else "Unknown"
                print_diagnostic(f"Frontend Service - {tenant_id}", True, f"Service: {service_name}, ClusterIP: {cluster_ip}", kubectl_cmd=kubectl_cmd)
                results.append(True)
            else:
                print_diagnostic(f"Frontend Service - {tenant_id}", False, "Frontend service not found", stderr, kubectl_cmd)
                results.append(False)
        else:
            print_diagnostic(f"Frontend Pod Discovery - {tenant_id}", False, "No frontend pods found", stderr, kubectl_cmd)
            results.extend([False, False, False, False])

    return results

def diagnostic3_backend_issues():
    """Diagnostic 3: Backend Issues Analysis"""
    print_header("DIAGNOSTIC 3: BACKEND ISSUES ANALYSIS")

    results = []

    print_subheader("Getting Tenant Namespaces for Backend Analysis")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("Backend Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    print_subheader("Backend Pod Pending/Missing Diagnosis")

    for ns in tenant_namespaces[:3]:  # Analyze first 3 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check backend deployment
        kubectl_cmd = f"kubectl get deployment -n {ns} | grep backend"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            deployment_info = stdout.split()
            deployment_name = deployment_info[0]
            ready_replicas = deployment_info[1] if len(deployment_info) > 1 else "0/0"
            print_diagnostic(f"Backend Deployment - {tenant_id}", True, f"Deployment: {deployment_name}, Ready: {ready_replicas}", kubectl_cmd=kubectl_cmd)
            results.append(True)

            # Check backend pods with detailed analysis
            kubectl_cmd = f"kubectl get pods -n {ns} | grep backend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                pod_info = stdout.split()
                pod_name = pod_info[0]
                pod_status = pod_info[2] if len(pod_info) > 2 else "Unknown"
                age = pod_info[4] if len(pod_info) > 4 else "Unknown"

                if "Pending" in pod_status:
                    print_diagnostic(f"Backend Pod Status - {tenant_id}", False, f"Pod: {pod_name}, Status: {pod_status}, Age: {age}", "Pod stuck in Pending state", kubectl_cmd)

                    # Analyze why pod is pending
                    kubectl_cmd = f"kubectl describe pod {pod_name} -n {ns}"
                    success, describe_output, stderr = run_command(kubectl_cmd, 15)
                    if success:
                        if "FailedScheduling" in describe_output:
                            print_diagnostic(f"Backend Scheduling Issue - {tenant_id}", False, "FailedScheduling detected", "Resource constraints or node issues", kubectl_cmd)
                        elif "ImagePullBackOff" in describe_output:
                            print_diagnostic(f"Backend Image Pull Issue - {tenant_id}", False, "ImagePullBackOff detected", "ECR authentication or image issues", kubectl_cmd)
                        else:
                            print_diagnostic(f"Backend Pod Analysis - {tenant_id}", False, "Pod pending for unknown reason", describe_output[:200] + "...", kubectl_cmd)
                    results.append(False)
                elif "Running" in pod_status:
                    print_diagnostic(f"Backend Pod Status - {tenant_id}", True, f"Pod: {pod_name}, Status: {pod_status}, Age: {age}", kubectl_cmd=kubectl_cmd)
                    results.append(True)
                else:
                    print_diagnostic(f"Backend Pod Status - {tenant_id}", False, f"Pod: {pod_name}, Status: {pod_status}, Age: {age}", "Pod not in expected state", kubectl_cmd)
                    results.append(False)
            else:
                print_diagnostic(f"Backend Pod Discovery - {tenant_id}", False, "No backend pods found", stderr, kubectl_cmd)
                results.append(False)

            # Check backend ECR image
            kubectl_cmd = f"kubectl get deployment {deployment_name} -n {ns} -o yaml | grep 'image:.*webapp_dev:2.0.56-test'"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            print_diagnostic(f"Backend ECR Image - {tenant_id}", success, "Using correct webapp_dev:2.0.56-test" if success else "Not using correct ECR image", kubectl_cmd=kubectl_cmd)
            results.append(success)

            # Check backend service
            kubectl_cmd = f"kubectl get service -n {ns} | grep backend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                service_info = stdout.split()
                service_name = service_info[0]
                print_diagnostic(f"Backend Service - {tenant_id}", True, f"Service: {service_name} exists", kubectl_cmd=kubectl_cmd)
                results.append(True)
            else:
                print_diagnostic(f"Backend Service - {tenant_id}", False, "Backend service not found", stderr, kubectl_cmd)
                results.append(False)
        else:
            print_diagnostic(f"Backend Deployment - {tenant_id}", False, "No backend deployment found", stderr, kubectl_cmd)
            results.extend([False, False, False, False])

    return results

def diagnostic4_nginx_issues():
    """Diagnostic 4: Nginx Issues Analysis"""
    print_header("DIAGNOSTIC 4: NGINX ISSUES ANALYSIS")

    results = []

    print_subheader("Nginx Integration Verification")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("Nginx Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    for ns in tenant_namespaces[:3]:  # Analyze first 3 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check nginx deployment (separate or integrated)
        kubectl_cmd = f"kubectl get deployment -n {ns} | grep nginx"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            deployment_info = stdout.split()
            deployment_name = deployment_info[0]
            print_diagnostic(f"Nginx Deployment - {tenant_id}", True, f"Separate nginx deployment: {deployment_name}", kubectl_cmd=kubectl_cmd)
            results.append(True)
        else:
            # Check if nginx is integrated with frontend
            kubectl_cmd = f"kubectl get deployment -n {ns} -o yaml | grep 'nginx_dev:1.0.6-update_ssl'"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success:
                print_diagnostic(f"Nginx Integration - {tenant_id}", True, "Nginx integrated with frontend deployment", kubectl_cmd=kubectl_cmd)
                results.append(True)
            else:
                print_diagnostic(f"Nginx Integration - {tenant_id}", False, "Nginx not found in deployments", stderr, kubectl_cmd)
                results.append(False)

        # Verify nginx ECR image
        kubectl_cmd = f"kubectl get deployment -n {ns} -o yaml | grep 'nginx_dev:1.0.6-update_ssl'"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        print_diagnostic(f"Nginx ECR Image - {tenant_id}", success, "Using correct nginx_dev:1.0.6-update_ssl" if success else "Not using correct ECR image", kubectl_cmd=kubectl_cmd)
        results.append(success)

        # Test nginx configuration
        kubectl_cmd = f"kubectl get configmap -n {ns} | grep nginx"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            print_diagnostic(f"Nginx Configuration - {tenant_id}", True, "Nginx ConfigMap found", kubectl_cmd=kubectl_cmd)
            results.append(True)
        else:
            print_diagnostic(f"Nginx Configuration - {tenant_id}", False, "No nginx ConfigMap found", stderr, kubectl_cmd)
            results.append(False)

    return results

def diagnostic5_rabbitmq_issues():
    """Diagnostic 5: RabbitMQ Issues Analysis"""
    print_header("DIAGNOSTIC 5: RABBITMQ ISSUES ANALYSIS")

    results = []

    print_subheader("RabbitMQ Deployment Status Analysis")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("RabbitMQ Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    for ns in tenant_namespaces[:3]:  # Analyze first 3 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check RabbitMQ deployment
        kubectl_cmd = f"kubectl get deployment -n {ns} | grep rabbitmq"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            deployment_info = stdout.split()
            deployment_name = deployment_info[0]
            ready_replicas = deployment_info[1] if len(deployment_info) > 1 else "0/0"
            print_diagnostic(f"RabbitMQ Deployment - {tenant_id}", True, f"Deployment: {deployment_name}, Ready: {ready_replicas}", kubectl_cmd=kubectl_cmd)
            results.append(True)

            # Check RabbitMQ ECR image
            kubectl_cmd = f"kubectl get deployment {deployment_name} -n {ns} -o yaml | grep 'rabbitmq_dev:1.02'"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            print_diagnostic(f"RabbitMQ ECR Image - {tenant_id}", success, "Using correct rabbitmq_dev:1.02" if success else "Not using correct ECR image", kubectl_cmd=kubectl_cmd)
            results.append(success)

            # Check RabbitMQ pods
            kubectl_cmd = f"kubectl get pods -n {ns} | grep rabbitmq"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                pod_info = stdout.split()
                pod_name = pod_info[0]
                pod_status = pod_info[2] if len(pod_info) > 2 else "Unknown"
                print_diagnostic(f"RabbitMQ Pod - {tenant_id}", "Running" in pod_status, f"Pod: {pod_name}, Status: {pod_status}", kubectl_cmd=kubectl_cmd)
                results.append("Running" in pod_status)
            else:
                print_diagnostic(f"RabbitMQ Pod - {tenant_id}", False, "No RabbitMQ pods found", stderr, kubectl_cmd)
                results.append(False)

            # Check RabbitMQ service
            kubectl_cmd = f"kubectl get service -n {ns} | grep rabbitmq"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                service_info = stdout.split()
                service_name = service_info[0]
                print_diagnostic(f"RabbitMQ Service - {tenant_id}", True, f"Service: {service_name} exists", kubectl_cmd=kubectl_cmd)
                results.append(True)
            else:
                print_diagnostic(f"RabbitMQ Service - {tenant_id}", False, "RabbitMQ service not found", stderr, kubectl_cmd)
                results.append(False)
        else:
            print_diagnostic(f"RabbitMQ Deployment - {tenant_id}", False, "No RabbitMQ deployment found", stderr, kubectl_cmd)
            results.extend([False, False, False, False])

    return results

def diagnostic6_frontend_backend_connectivity():
    """Diagnostic 6: Frontend-Backend Connectivity Testing"""
    print_header("DIAGNOSTIC 6: FRONTEND-BACKEND CONNECTIVITY TESTING")

    results = []

    print_subheader("API Connectivity Testing")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("Connectivity Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    for ns in tenant_namespaces[:2]:  # Test first 2 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check if both frontend and backend services exist
        kubectl_cmd = f"kubectl get service -n {ns}"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success:
            has_frontend = "frontend" in stdout
            has_backend = "backend" in stdout
            print_diagnostic(f"Service Discovery - {tenant_id}", has_frontend and has_backend, f"Frontend: {has_frontend}, Backend: {has_backend}", kubectl_cmd=kubectl_cmd)
            results.append(has_frontend and has_backend)

            if has_frontend and has_backend:
                # Test connectivity from frontend to backend
                kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
                success, stdout, stderr = run_command(kubectl_cmd, 10)
                if success and stdout:
                    frontend_pod = stdout.split()[0]

                    # Test backend service connectivity
                    kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- curl -s -o /dev/null -w '%{{http_code}}' http://tenant-{tenant_id}-backend-service/health || echo 'connection_failed'"
                    success, stdout, stderr = run_command(kubectl_cmd, 15)
                    if success and "200" in stdout:
                        print_diagnostic(f"Frontend-Backend API - {tenant_id}", True, "API connectivity successful", kubectl_cmd=kubectl_cmd)
                        results.append(True)
                    else:
                        print_diagnostic(f"Frontend-Backend API - {tenant_id}", False, f"API connectivity failed: {stdout}", stderr, kubectl_cmd)
                        results.append(False)
                else:
                    print_diagnostic(f"Frontend Pod for Testing - {tenant_id}", False, "No frontend pod available for testing", stderr, kubectl_cmd)
                    results.append(False)
            else:
                results.append(False)
        else:
            print_diagnostic(f"Service Discovery - {tenant_id}", False, "Cannot get services", stderr, kubectl_cmd)
            results.extend([False, False])

    return results

def diagnostic7_ssl_security_analysis():
    """Diagnostic 7: SSL and Security Analysis"""
    print_header("DIAGNOSTIC 7: SSL AND SECURITY ANALYSIS")

    results = []

    print_subheader("SSL Certificate Permission Analysis")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("SSL Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    for ns in tenant_namespaces[:2]:  # Analyze first 2 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check SSL certificate issues in frontend pods
        kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success and stdout:
            frontend_pod = stdout.split()[0]

            # Check SSL certificate files
            kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- ls -la /etc/nginx/cert/ 2>/dev/null || echo 'cert_dir_not_found'"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and "architrave.key" in stdout:
                print_diagnostic(f"SSL Certificate Files - {tenant_id}", True, "SSL certificate files found", kubectl_cmd=kubectl_cmd)

                # Check certificate permissions
                kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- stat -c '%a %n' /etc/nginx/cert/architrave.key 2>/dev/null || echo 'permission_check_failed'"
                success, stdout, stderr = run_command(kubectl_cmd, 10)
                if success and not "permission_check_failed" in stdout:
                    permissions = stdout.split()[0] if stdout else "unknown"
                    print_diagnostic(f"SSL Certificate Permissions - {tenant_id}", "600" in permissions or "644" in permissions, f"Permissions: {permissions}", kubectl_cmd=kubectl_cmd)
                    results.append("600" in permissions or "644" in permissions)
                else:
                    print_diagnostic(f"SSL Certificate Permissions - {tenant_id}", False, "Cannot check permissions", stderr, kubectl_cmd)
                    results.append(False)
            else:
                print_diagnostic(f"SSL Certificate Files - {tenant_id}", False, "SSL certificate files not found or accessible", stderr, kubectl_cmd)
                results.append(False)
        else:
            print_diagnostic(f"Frontend Pod for SSL Check - {tenant_id}", False, "No frontend pod available", stderr, kubectl_cmd)
            results.append(False)

    print_subheader("Aurora Serverless SSL/TLS Analysis")

    # Test Aurora SSL connection
    db_ssl_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: ssl-diagnostic-pod
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "120"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
"""

    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_ssl_yaml)
        pod_file = f.name

    try:
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if success:
            time.sleep(8)
            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/ssl-diagnostic-pod --timeout=30s")
            if success:
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"

                # Test SSL cipher
                kubectl_cmd = f"kubectl exec ssl-diagnostic-pod -- mysql -h {db_host} -P 3306 -u admin -e 'SHOW STATUS LIKE \"Ssl_cipher\";'"
                success, stdout, stderr = run_command(kubectl_cmd, 15)
                if success and "TLS_AES_256_GCM_SHA384" in stdout:
                    print_diagnostic("Aurora SSL Cipher", True, "Using TLS_AES_256_GCM_SHA384", kubectl_cmd=kubectl_cmd)
                    results.append(True)
                else:
                    print_diagnostic("Aurora SSL Cipher", False, "SSL cipher check failed", stderr, kubectl_cmd)
                    results.append(False)

                # Test secure transport requirement
                kubectl_cmd = f"kubectl exec ssl-diagnostic-pod -- mysql -h {db_host} -P 3306 -u admin -e 'SHOW VARIABLES LIKE \"require_secure_transport\";'"
                success, stdout, stderr = run_command(kubectl_cmd, 15)
                if success and "ON" in stdout:
                    print_diagnostic("Aurora Secure Transport", True, "Secure transport required", kubectl_cmd=kubectl_cmd)
                    results.append(True)
                else:
                    print_diagnostic("Aurora Secure Transport", False, "Secure transport check failed", stderr, kubectl_cmd)
                    results.append(False)
            else:
                print_diagnostic("SSL Diagnostic Pod Ready", False, "Pod not ready", stderr, "kubectl wait --for=condition=ready pod/ssl-diagnostic-pod")
                results.extend([False, False])
        else:
            print_diagnostic("SSL Diagnostic Pod Creation", False, "Cannot create pod", stderr, "kubectl apply -f ssl-diagnostic.yaml")
            results.extend([False, False])
    finally:
        run_command("kubectl delete pod ssl-diagnostic-pod --ignore-not-found=true")
        import os
        os.unlink(pod_file)

    return results

def diagnostic8_networking_analysis():
    """Diagnostic 8: Networking Analysis"""
    print_header("DIAGNOSTIC 8: NETWORKING ANALYSIS")

    results = []

    print_subheader("Pod-to-Pod Communication Testing")

    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_diagnostic("Networking Namespaces Discovery", False, "Cannot get tenant namespaces", stderr, kubectl_cmd)
        return [False]

    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]

    for ns in tenant_namespaces[:2]:  # Test first 2 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Test service discovery and DNS resolution
        kubectl_cmd = f"kubectl get service -n {ns}"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success:
            services = [line.split()[0] for line in stdout.split('\n')[1:] if line.strip()]
            print_diagnostic(f"Service Discovery - {tenant_id}", len(services) > 0, f"Found {len(services)} services", kubectl_cmd=kubectl_cmd)
            results.append(len(services) > 0)

            # Test DNS resolution from a pod
            kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                frontend_pod = stdout.split()[0]

                # Test DNS resolution
                for service in services[:2]:  # Test first 2 services
                    kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- nslookup {service} 2>/dev/null || echo 'dns_failed'"
                    success, stdout, stderr = run_command(kubectl_cmd, 10)
                    dns_works = success and "dns_failed" not in stdout and "Address" in stdout
                    print_diagnostic(f"DNS Resolution {service} - {tenant_id}", dns_works, f"DNS resolution {'successful' if dns_works else 'failed'}", kubectl_cmd=kubectl_cmd)
                    results.append(dns_works)
            else:
                print_diagnostic(f"Pod for DNS Testing - {tenant_id}", False, "No pod available for DNS testing", stderr, kubectl_cmd)
                results.append(False)
        else:
            print_diagnostic(f"Service Discovery - {tenant_id}", False, "Cannot get services", stderr, kubectl_cmd)
            results.extend([False, False])

    print_subheader("ClusterIP Accessibility and Port Configuration")

    for ns in tenant_namespaces[:2]:  # Test first 2 namespaces
        tenant_id = ns.replace('tenant-', '')

        # Check ClusterIP accessibility
        kubectl_cmd = f"kubectl get service -n {ns} -o wide"
        success, stdout, stderr = run_command(kubectl_cmd, 10)
        if success:
            lines = stdout.split('\n')[1:]  # Skip header
            for line in lines:
                if line.strip():
                    parts = line.split()
                    service_name = parts[0]
                    cluster_ip = parts[2] if len(parts) > 2 else "None"
                    ports = parts[4] if len(parts) > 4 else "Unknown"

                    if cluster_ip != "None" and cluster_ip != "<none>":
                        print_diagnostic(f"ClusterIP {service_name} - {tenant_id}", True, f"ClusterIP: {cluster_ip}, Ports: {ports}", kubectl_cmd=kubectl_cmd)
                        results.append(True)
                    else:
                        print_diagnostic(f"ClusterIP {service_name} - {tenant_id}", False, f"No ClusterIP assigned", kubectl_cmd=kubectl_cmd)
                        results.append(False)
        else:
            print_diagnostic(f"ClusterIP Check - {tenant_id}", False, "Cannot get service details", stderr, kubectl_cmd)
            results.append(False)

    return results

def main():
    """Main comprehensive diagnostic function."""
    print("🔍 COMPREHENSIVE SYSTEM DIAGNOSTIC")
    print("=" * 80)
    print(f"Diagnostic started at: {datetime.now()}")

    all_results = []
    diagnostic_results = {}

    # Execute diagnostics 1-4
    print("\n🚀 EXECUTING COMPREHENSIVE SYSTEM DIAGNOSTICS")

    # Diagnostic 1: Database Issues Analysis
    diag1_results = diagnostic1_database_issues()
    all_results.extend(diag1_results)
    diagnostic_results['Diagnostic 1 - Database Issues'] = diag1_results

    # Diagnostic 2: Frontend Issues Analysis
    diag2_results = diagnostic2_frontend_issues()
    all_results.extend(diag2_results)
    diagnostic_results['Diagnostic 2 - Frontend Issues'] = diag2_results

    # Diagnostic 3: Backend Issues Analysis
    diag3_results = diagnostic3_backend_issues()
    all_results.extend(diag3_results)
    diagnostic_results['Diagnostic 3 - Backend Issues'] = diag3_results

    # Diagnostic 4: Nginx Issues Analysis
    diag4_results = diagnostic4_nginx_issues()
    all_results.extend(diag4_results)
    diagnostic_results['Diagnostic 4 - Nginx Issues'] = diag4_results

    # Diagnostic 5: RabbitMQ Issues Analysis
    diag5_results = diagnostic5_rabbitmq_issues()
    all_results.extend(diag5_results)
    diagnostic_results['Diagnostic 5 - RabbitMQ Issues'] = diag5_results

    # Diagnostic 6: Frontend-Backend Connectivity Testing
    diag6_results = diagnostic6_frontend_backend_connectivity()
    all_results.extend(diag6_results)
    diagnostic_results['Diagnostic 6 - Frontend-Backend Connectivity'] = diag6_results

    # Diagnostic 7: SSL and Security Analysis
    diag7_results = diagnostic7_ssl_security_analysis()
    all_results.extend(diag7_results)
    diagnostic_results['Diagnostic 7 - SSL and Security'] = diag7_results

    # Diagnostic 8: Networking Analysis
    diag8_results = diagnostic8_networking_analysis()
    all_results.extend(diag8_results)
    diagnostic_results['Diagnostic 8 - Networking'] = diag8_results

    return all_results, diagnostic_results

if __name__ == "__main__":
    all_results, diagnostic_results = main()

    # Calculate and display diagnostic results
    passed = sum(all_results)
    total = len(all_results)
    success_rate = (passed / total) * 100 if total > 0 else 0

    print_header("COMPREHENSIVE DIAGNOSTIC SUMMARY")

    print(f"📊 OVERALL DIAGNOSTIC RESULTS:")
    print(f"  Tests passed: {passed}/{total}")
    print(f"  Success rate: {success_rate:.1f}%")

    print(f"\n📋 DIAGNOSTIC-BY-DIAGNOSTIC BREAKDOWN:")
    for diag_name, results in diagnostic_results.items():
        diag_passed = sum(results)
        diag_total = len(results)
        diag_rate = (diag_passed / diag_total) * 100 if diag_total > 0 else 0
        status = "✅" if diag_rate >= 70 else "⚠️" if diag_rate >= 50 else "❌"
        print(f"  {status} {diag_name}: {diag_passed}/{diag_total} ({diag_rate:.1f}%)")

    if success_rate >= 75:
        print(f"\n🎉 COMPREHENSIVE DIAGNOSTIC SUCCESSFUL!")
        print(f"✅ System is {success_rate:.1f}% functional")
        sys.exit(0)
    else:
        print(f"\n⚠️ COMPREHENSIVE DIAGNOSTIC ISSUES DETECTED")
        print(f"❌ System is {success_rate:.1f}% functional")
        sys.exit(1)
