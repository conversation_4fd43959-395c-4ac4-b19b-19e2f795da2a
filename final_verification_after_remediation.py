#!/usr/bin/env python3
"""
Final Verification After Remediation
Comprehensive verification of all system components after remediation
"""

import subprocess
import sys
import time
import tempfile
import os
from datetime import datetime

def run_command(command, timeout=20):
    """Run a command with timeout and detailed output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*80}")
    print(f"🎯 {title}")
    print(f"{'='*80}")

def print_subheader(title):
    """Print formatted subheader."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def print_final_verification(test_name, success, details="", kubectl_cmd=""):
    """Print final verification result."""
    status = "✅ VERIFIED" if success else "❌ ISSUE"
    print(f"{status}: {test_name}")
    if kubectl_cmd:
        print(f"   Command: {kubectl_cmd}")
    if details:
        print(f"   Details: {details}")

def final_verification_ecr_images():
    """Final Verification: ECR Images"""
    print_header("FINAL VERIFICATION: ECR IMAGES")
    
    results = []
    
    # Define correct ECR images
    correct_images = {
        'frontend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41',
        'backend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test',
        'rabbitmq': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02'
    }
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_final_verification("ECR Image Verification", False, "Cannot get tenant namespaces", kubectl_cmd)
        return [False]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    print_final_verification("Tenant Namespaces Discovery", True, f"Found {len(tenant_namespaces)} tenant namespaces", kubectl_cmd)
    
    print_subheader("ECR Image Verification by Component")
    
    for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
        tenant_id = ns.replace('tenant-', '')
        
        for component, correct_image in correct_images.items():
            # Get deployment image
            kubectl_cmd = f"kubectl get deployment -n {ns} -o yaml | grep 'image:.*{correct_image}'"
            success, stdout, stderr = run_command(kubectl_cmd, 15)
            
            if success and correct_image in stdout:
                print_final_verification(f"ECR Image {component.title()} - {tenant_id}", True, f"Using correct image: {correct_image.split('/')[-1]}", kubectl_cmd)
                results.append(True)
            else:
                print_final_verification(f"ECR Image {component.title()} - {tenant_id}", False, f"Not using correct ECR image", kubectl_cmd)
                results.append(False)
    
    return results

def final_verification_database_connectivity():
    """Final Verification: Database Connectivity"""
    print_header("FINAL VERIFICATION: DATABASE CONNECTIVITY")
    
    results = []
    
    # Create database verification pod with proper resources
    db_test_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: final-db-verification
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "180"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      requests:
        cpu: 50m
        memory: 128Mi
      limits:
        cpu: 200m
        memory: 512Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_test_yaml)
        pod_file = f.name
    
    try:
        kubectl_cmd = f"kubectl apply -f {pod_file}"
        success, stdout, stderr = run_command(kubectl_cmd)
        if success:
            print_final_verification("Database Test Pod Creation", True, "Pod created successfully", kubectl_cmd)
            time.sleep(10)
            
            kubectl_cmd = "kubectl wait --for=condition=ready pod/final-db-verification --timeout=45s"
            success, stdout, stderr = run_command(kubectl_cmd)
            if success:
                print_final_verification("Database Test Pod Ready", True, "Pod is ready for verification", kubectl_cmd)
                
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
                
                # Test database connectivity and schema
                db_queries = [
                    ("SELECT 1 as connectivity_test;", "Basic Connectivity"),
                    ("USE architrave; SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave';", "Table Count"),
                    ("USE architrave; SELECT * FROM tenant_config;", "Tenant Configurations"),
                    ("USE architrave; SHOW STATUS LIKE 'Ssl_cipher';", "SSL Connection"),
                ]
                
                for query, test_name in db_queries:
                    kubectl_cmd = f"kubectl exec final-db-verification -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\""
                    success, stdout, stderr = run_command(kubectl_cmd, 20)
                    
                    if success:
                        print_final_verification(f"DATABASE - {test_name}", True, "Query executed successfully", kubectl_cmd)
                        results.append(True)
                        
                        # Show important results
                        if "table_count" in query or "tenant_config" in query:
                            lines = stdout.split('\n')
                            if len(lines) > 1:
                                print(f"     Result: {lines[1]}")
                    else:
                        print_final_verification(f"DATABASE - {test_name}", False, f"Query failed: {stderr}", kubectl_cmd)
                        results.append(False)
            else:
                print_final_verification("Database Test Pod Ready", False, "Pod not ready", kubectl_cmd)
                results.extend([False] * 4)
        else:
            print_final_verification("Database Test Pod Creation", False, "Cannot create pod", kubectl_cmd)
            results.extend([False] * 4)
    finally:
        run_command("kubectl delete pod final-db-verification --ignore-not-found=true")
        os.unlink(pod_file)
    
    return results

def final_verification_ssl_certificates():
    """Final Verification: SSL Certificates"""
    print_header("FINAL VERIFICATION: SSL CERTIFICATES")
    
    results = []
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check if SSL secret exists
            kubectl_cmd = f"kubectl get secret ssl-certs -n {ns}"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success:
                print_final_verification(f"SSL Secret - {tenant_id}", True, "SSL secret exists", kubectl_cmd)
                results.append(True)
            else:
                print_final_verification(f"SSL Secret - {tenant_id}", False, "SSL secret not found", kubectl_cmd)
                results.append(False)
    else:
        print_final_verification("SSL Certificate Verification", False, "Cannot get tenant namespaces", kubectl_cmd)
        results.extend([False] * 3)
    
    return results

def final_verification_backend_services():
    """Final Verification: Backend Services"""
    print_header("FINAL VERIFICATION: BACKEND SERVICES")
    
    results = []
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check backend service
            kubectl_cmd = f"kubectl get service -n {ns} | grep backend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                service_info = stdout.split()
                service_name = service_info[0]
                cluster_ip = service_info[2] if len(service_info) > 2 else "Unknown"
                print_final_verification(f"Backend Service - {tenant_id}", True, f"Service: {service_name}, ClusterIP: {cluster_ip}", kubectl_cmd)
                results.append(True)
            else:
                print_final_verification(f"Backend Service - {tenant_id}", False, "Backend service not found", kubectl_cmd)
                results.append(False)
    else:
        print_final_verification("Backend Services Verification", False, "Cannot get tenant namespaces", kubectl_cmd)
        results.extend([False] * 3)
    
    return results

def final_verification_pod_status():
    """Final Verification: Pod Status"""
    print_header("FINAL VERIFICATION: POD STATUS")
    
    results = []
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check pod status
            kubectl_cmd = f"kubectl get pods -n {ns}"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success:
                lines = stdout.split('\n')[1:]  # Skip header
                total_pods = len([line for line in lines if line.strip()])
                running_pods = len([line for line in lines if 'Running' in line])
                pending_pods = len([line for line in lines if 'Pending' in line])
                
                if running_pods > 0:
                    print_final_verification(f"Pod Status - {tenant_id}", True, f"{running_pods}/{total_pods} pods running, {pending_pods} pending", kubectl_cmd)
                    results.append(True)
                else:
                    print_final_verification(f"Pod Status - {tenant_id}", False, f"No running pods ({total_pods} total, {pending_pods} pending)", kubectl_cmd)
                    results.append(False)
            else:
                print_final_verification(f"Pod Status - {tenant_id}", False, "Cannot get pod status", kubectl_cmd)
                results.append(False)
    else:
        print_final_verification("Pod Status Verification", False, "Cannot get tenant namespaces", kubectl_cmd)
        results.extend([False] * 3)
    
    return results

def final_verification_istio_sidecars():
    """Final Verification: Istio Sidecars"""
    print_header("FINAL VERIFICATION: ISTIO SIDECARS")
    
    results = []
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check if namespace has Istio injection enabled
            kubectl_cmd = f"kubectl get namespace {ns} -o yaml | grep istio-injection"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and "enabled" in stdout:
                print_final_verification(f"Istio Injection - {tenant_id}", True, "Istio injection enabled", kubectl_cmd)
                results.append(True)
            else:
                print_final_verification(f"Istio Injection - {tenant_id}", False, "Istio injection not enabled", kubectl_cmd)
                results.append(False)
            
            # Check for Istio sidecars in pods
            kubectl_cmd = f"kubectl get pods -n {ns} -o jsonpath='{{.items[*].spec.containers[*].name}}' | grep istio-proxy"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and "istio-proxy" in stdout:
                sidecar_count = stdout.count("istio-proxy")
                print_final_verification(f"Istio Sidecars - {tenant_id}", True, f"{sidecar_count} Istio sidecars present", kubectl_cmd)
                results.append(True)
            else:
                print_final_verification(f"Istio Sidecars - {tenant_id}", False, "No Istio sidecars found", kubectl_cmd)
                results.append(False)
    else:
        print_final_verification("Istio Sidecars Verification", False, "Cannot get tenant namespaces", kubectl_cmd)
        results.extend([False] * 6)
    
    return results

def final_verification_frontend_backend_connectivity():
    """Final Verification: Frontend-Backend Connectivity"""
    print_header("FINAL VERIFICATION: FRONTEND-BACKEND CONNECTIVITY")
    
    results = []
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:2]:  # Test first 2 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check if both frontend and backend services exist
            kubectl_cmd = f"kubectl get service -n {ns}"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success:
                has_frontend = "frontend" in stdout
                has_backend = "backend" in stdout
                
                if has_frontend and has_backend:
                    print_final_verification(f"Service Discovery - {tenant_id}", True, "Both frontend and backend services exist", kubectl_cmd)
                    results.append(True)
                else:
                    print_final_verification(f"Service Discovery - {tenant_id}", False, f"Frontend: {has_frontend}, Backend: {has_backend}", kubectl_cmd)
                    results.append(False)
            else:
                print_final_verification(f"Service Discovery - {tenant_id}", False, "Cannot get services", kubectl_cmd)
                results.append(False)
    else:
        print_final_verification("Frontend-Backend Connectivity", False, "Cannot get tenant namespaces", kubectl_cmd)
        results.extend([False] * 2)
    
    return results

def main():
    """Main final verification function."""
    print("🎯 FINAL VERIFICATION AFTER REMEDIATION")
    print("=" * 80)
    print(f"Final verification started at: {datetime.now()}")
    
    all_results = []
    verification_results = {}
    
    # Execute all final verifications
    print("\n🚀 EXECUTING FINAL VERIFICATIONS")
    
    # Final Verification 1: ECR Images
    ver1_results = final_verification_ecr_images()
    all_results.extend(ver1_results)
    verification_results['ECR Images'] = ver1_results
    
    # Final Verification 2: Database Connectivity
    ver2_results = final_verification_database_connectivity()
    all_results.extend(ver2_results)
    verification_results['Database Connectivity'] = ver2_results
    
    # Final Verification 3: SSL Certificates
    ver3_results = final_verification_ssl_certificates()
    all_results.extend(ver3_results)
    verification_results['SSL Certificates'] = ver3_results
    
    # Final Verification 4: Backend Services
    ver4_results = final_verification_backend_services()
    all_results.extend(ver4_results)
    verification_results['Backend Services'] = ver4_results
    
    # Final Verification 5: Pod Status
    ver5_results = final_verification_pod_status()
    all_results.extend(ver5_results)
    verification_results['Pod Status'] = ver5_results
    
    # Final Verification 6: Istio Sidecars
    ver6_results = final_verification_istio_sidecars()
    all_results.extend(ver6_results)
    verification_results['Istio Sidecars'] = ver6_results
    
    # Final Verification 7: Frontend-Backend Connectivity
    ver7_results = final_verification_frontend_backend_connectivity()
    all_results.extend(ver7_results)
    verification_results['Frontend-Backend Connectivity'] = ver7_results
    
    return all_results, verification_results

if __name__ == "__main__":
    all_results, verification_results = main()
    
    # Calculate and display final verification results
    verified = sum(all_results)
    total = len(all_results)
    success_rate = (verified / total) * 100 if total > 0 else 0
    
    print_header("FINAL VERIFICATION SUMMARY")
    
    print(f"📊 OVERALL FINAL VERIFICATION RESULTS:")
    print(f"  Components verified: {verified}/{total}")
    print(f"  Success rate: {success_rate:.1f}%")
    
    print(f"\n📋 COMPONENT-BY-COMPONENT BREAKDOWN:")
    for comp_name, results in verification_results.items():
        comp_verified = sum(results)
        comp_total = len(results)
        comp_rate = (comp_verified / comp_total) * 100 if comp_total > 0 else 0
        status = "✅" if comp_rate >= 70 else "⚠️" if comp_rate >= 50 else "❌"
        print(f"  {status} {comp_name}: {comp_verified}/{comp_total} ({comp_rate:.1f}%)")
    
    print(f"\n🎯 FINAL SYSTEM STATUS:")
    if success_rate >= 85:
        print(f"🎉 SYSTEM FULLY OPERATIONAL!")
        print(f"✅ All components verified and working at {success_rate:.1f}% capacity")
        print(f"🚀 Tenant management system is production-ready!")
    elif success_rate >= 70:
        print(f"✅ SYSTEM MOSTLY OPERATIONAL!")
        print(f"⚠️ System verified and working at {success_rate:.1f}% capacity")
        print(f"🔧 Minor issues remain but system is functional")
    else:
        print(f"⚠️ SYSTEM PARTIALLY OPERATIONAL")
        print(f"❌ System verified at {success_rate:.1f}% capacity")
        print(f"🔧 Additional remediation may be required")
    
    if success_rate >= 70:
        sys.exit(0)
    else:
        sys.exit(1)
