#!/usr/bin/env python3
"""
Tenant Event Integration System

This script integrates with existing tenant onboarding/offboarding scripts
to automatically store and manage tenant events in the centralized database.
It provides automated event tracking for 100+ tenants.
"""

import os
import sys
import json
import logging
import requests
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TenantEventManager:
    """Manages tenant events and integrates with the event storage API."""
    
    def __init__(self, api_base_url: str = "http://tenant-event-api.tenant-management.svc.cluster.local"):
        self.api_base_url = api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'TenantEventManager/1.0'
        })
    
    def create_event(self, tenant_id: str, event_type: str, event_data: Dict[str, Any], 
                    initiated_by: str = "system") -> Optional[int]:
        """Create a new tenant event."""
        try:
            payload = {
                'event_type': event_type,
                'event_data': event_data,
                'initiated_by': initiated_by
            }
            
            response = self.session.post(
                f"{self.api_base_url}/api/tenants/{tenant_id}/events",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                result = response.json()
                event_id = result.get('event_id')
                logger.info(f"Created event {event_id} for tenant {tenant_id}: {event_type}")
                return event_id
            else:
                logger.error(f"Failed to create event: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating event: {e}")
            return None
    
    def complete_event(self, event_id: int, status: str, error_message: Optional[str] = None) -> bool:
        """Mark an event as completed."""
        try:
            payload = {
                'status': status,
                'error_message': error_message
            }
            
            response = self.session.put(
                f"{self.api_base_url}/api/events/{event_id}/complete",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Completed event {event_id} with status: {status}")
                return True
            else:
                logger.error(f"Failed to complete event: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error completing event: {e}")
            return False
    
    def update_tenant_resources(self, tenant_id: str, resources: Dict[str, Dict[str, Any]]) -> bool:
        """Update tenant resource allocation and usage."""
        try:
            payload = {'resources': resources}
            
            response = self.session.put(
                f"{self.api_base_url}/api/tenants/{tenant_id}/resources",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Updated resources for tenant {tenant_id}")
                return True
            else:
                logger.error(f"Failed to update resources: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error updating resources: {e}")
            return False
    
    def register_tenant(self, tenant_data: Dict[str, Any]) -> bool:
        """Register a new tenant in the system."""
        try:
            # This would typically call a tenant registration endpoint
            # For now, we'll create an onboard event
            event_id = self.create_event(
                tenant_data['id'],
                'onboard',
                {
                    'tenant_data': tenant_data,
                    'components': tenant_data.get('components', []),
                    'tier': tenant_data.get('tier', 'basic'),
                    'environment': tenant_data.get('environment', 'production')
                },
                tenant_data.get('created_by', 'system')
            )
            
            return event_id is not None
            
        except Exception as e:
            logger.error(f"Error registering tenant: {e}")
            return False

class TenantOnboardingIntegration:
    """Integrates with existing tenant onboarding scripts."""
    
    def __init__(self, event_manager: TenantEventManager):
        self.event_manager = event_manager
        self.onboarding_script = "tenant-management/scripts/advanced_tenant_onboard.py"
    
    def onboard_tenant_with_events(self, tenant_id: str, tenant_name: str, 
                                  subdomain: str, tier: str = "standard",
                                  initiated_by: str = "admin") -> bool:
        """Onboard a tenant with comprehensive event tracking."""
        
        # Create initial onboarding event
        event_data = {
            'tenant_name': tenant_name,
            'subdomain': subdomain,
            'tier': tier,
            'components': ['database', 'frontend', 'backend', 'rabbitmq', 's3'],
            'environment': 'production'
        }
        
        event_id = self.event_manager.create_event(
            tenant_id, 'onboard', event_data, initiated_by
        )
        
        if not event_id:
            logger.error(f"Failed to create onboarding event for {tenant_id}")
            return False
        
        try:
            # Execute the actual onboarding script
            cmd = [
                'python3', self.onboarding_script,
                '--tenant-id', tenant_id,
                '--tenant-name', tenant_name,
                '--subdomain', subdomain,
                '--tier', tier,
                '--verify',
                '--debug'
            ]
            
            logger.info(f"Starting onboarding for tenant {tenant_id}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                # Onboarding successful
                self.event_manager.complete_event(event_id, 'completed')
                
                # Update resource information
                resources = self._extract_resources_from_output(result.stdout, tier)
                if resources:
                    self.event_manager.update_tenant_resources(tenant_id, resources)
                
                logger.info(f"Successfully onboarded tenant {tenant_id}")
                return True
            else:
                # Onboarding failed
                error_message = result.stderr or result.stdout
                self.event_manager.complete_event(event_id, 'failed', error_message)
                logger.error(f"Onboarding failed for tenant {tenant_id}: {error_message}")
                return False
                
        except subprocess.TimeoutExpired:
            self.event_manager.complete_event(event_id, 'failed', 'Onboarding timeout')
            logger.error(f"Onboarding timeout for tenant {tenant_id}")
            return False
        except Exception as e:
            self.event_manager.complete_event(event_id, 'failed', str(e))
            logger.error(f"Onboarding error for tenant {tenant_id}: {e}")
            return False
    
    def _extract_resources_from_output(self, output: str, tier: str) -> Dict[str, Dict[str, Any]]:
        """Extract resource information from onboarding script output."""
        # Default resource allocations based on tier
        tier_resources = {
            'basic': {
                'cpu': {'allocated': 500, 'used': 0, 'unit': 'millicores'},
                'memory': {'allocated': 1024, 'used': 0, 'unit': 'MB'},
                'storage': {'allocated': 25, 'used': 0, 'unit': 'GB'},
                'pods': {'allocated': 3, 'used': 0, 'unit': 'count'}
            },
            'standard': {
                'cpu': {'allocated': 1000, 'used': 0, 'unit': 'millicores'},
                'memory': {'allocated': 2048, 'used': 0, 'unit': 'MB'},
                'storage': {'allocated': 50, 'used': 0, 'unit': 'GB'},
                'pods': {'allocated': 5, 'used': 0, 'unit': 'count'}
            },
            'premium': {
                'cpu': {'allocated': 2000, 'used': 0, 'unit': 'millicores'},
                'memory': {'allocated': 4096, 'used': 0, 'unit': 'MB'},
                'storage': {'allocated': 100, 'used': 0, 'unit': 'GB'},
                'pods': {'allocated': 10, 'used': 0, 'unit': 'count'}
            }
        }
        
        return tier_resources.get(tier, tier_resources['standard'])

class TenantOffboardingIntegration:
    """Integrates with existing tenant offboarding scripts."""
    
    def __init__(self, event_manager: TenantEventManager):
        self.event_manager = event_manager
        self.offboarding_script = "tenant-management/scripts/advanced_tenant_offboard.py"
    
    def offboard_tenant_with_events(self, tenant_id: str, backup: bool = True,
                                   initiated_by: str = "admin") -> bool:
        """Offboard a tenant with comprehensive event tracking."""
        
        # Create initial offboarding event
        event_data = {
            'backup_requested': backup,
            'components': ['database', 'frontend', 'backend', 'rabbitmq', 's3'],
            'cleanup_type': 'complete'
        }
        
        event_id = self.event_manager.create_event(
            tenant_id, 'offboard', event_data, initiated_by
        )
        
        if not event_id:
            logger.error(f"Failed to create offboarding event for {tenant_id}")
            return False
        
        try:
            # Execute the actual offboarding script
            cmd = [
                'python3', self.offboarding_script,
                '--tenant-id', tenant_id,
                '--verify',
                '--debug'
            ]
            
            if backup:
                cmd.extend(['--backup', '--backup-path', f'/tmp/backups/{tenant_id}'])
            
            logger.info(f"Starting offboarding for tenant {tenant_id}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                # Offboarding successful
                self.event_manager.complete_event(event_id, 'completed')
                logger.info(f"Successfully offboarded tenant {tenant_id}")
                return True
            else:
                # Offboarding failed
                error_message = result.stderr or result.stdout
                self.event_manager.complete_event(event_id, 'failed', error_message)
                logger.error(f"Offboarding failed for tenant {tenant_id}: {error_message}")
                return False
                
        except subprocess.TimeoutExpired:
            self.event_manager.complete_event(event_id, 'failed', 'Offboarding timeout')
            logger.error(f"Offboarding timeout for tenant {tenant_id}")
            return False
        except Exception as e:
            self.event_manager.complete_event(event_id, 'failed', str(e))
            logger.error(f"Offboarding error for tenant {tenant_id}: {e}")
            return False

def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description='Tenant Event Integration System')
    parser.add_argument('action', choices=['onboard', 'offboard', 'test'], 
                       help='Action to perform')
    parser.add_argument('--tenant-id', required=True, help='Tenant ID')
    parser.add_argument('--tenant-name', help='Tenant name (for onboarding)')
    parser.add_argument('--subdomain', help='Subdomain (for onboarding)')
    parser.add_argument('--tier', default='standard', choices=['basic', 'standard', 'premium'],
                       help='Tenant tier (for onboarding)')
    parser.add_argument('--backup', action='store_true', help='Create backup (for offboarding)')
    parser.add_argument('--initiated-by', default='admin', help='Who initiated the action')
    parser.add_argument('--api-url', default='http://tenant-event-api.tenant-management.svc.cluster.local',
                       help='API base URL')
    
    args = parser.parse_args()
    
    # Initialize event manager
    event_manager = TenantEventManager(args.api_url)
    
    if args.action == 'onboard':
        if not args.tenant_name or not args.subdomain:
            logger.error("Tenant name and subdomain are required for onboarding")
            sys.exit(1)
        
        integration = TenantOnboardingIntegration(event_manager)
        success = integration.onboard_tenant_with_events(
            args.tenant_id, args.tenant_name, args.subdomain, 
            args.tier, args.initiated_by
        )
        
        sys.exit(0 if success else 1)
    
    elif args.action == 'offboard':
        integration = TenantOffboardingIntegration(event_manager)
        success = integration.offboard_tenant_with_events(
            args.tenant_id, args.backup, args.initiated_by
        )
        
        sys.exit(0 if success else 1)
    
    elif args.action == 'test':
        # Test API connectivity
        try:
            response = requests.get(f"{args.api_url}/health", timeout=10)
            if response.status_code == 200:
                logger.info("API connectivity test successful")
                print(json.dumps(response.json(), indent=2))
                sys.exit(0)
            else:
                logger.error(f"API test failed: {response.status_code}")
                sys.exit(1)
        except Exception as e:
            logger.error(f"API test failed: {e}")
            sys.exit(1)

if __name__ == '__main__':
    main()
