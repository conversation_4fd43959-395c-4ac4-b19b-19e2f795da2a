#!/usr/bin/env python3
"""
Comprehensive Verification and Remediation Process
Verifies and remediates all tenant management system components
"""

import subprocess
import sys
import time
import tempfile
import os
from datetime import datetime

def run_command(command, timeout=20):
    """Run a command with timeout and detailed output."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*80}")
    print(f"🔍 {title}")
    print(f"{'='*80}")

def print_subheader(title):
    """Print formatted subheader."""
    print(f"\n{'-'*60}")
    print(f"📋 {title}")
    print(f"{'-'*60}")

def print_verification(test_name, success, details="", kubectl_cmd="", remediation=""):
    """Print detailed verification result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if kubectl_cmd:
        print(f"   Command: {kubectl_cmd}")
    if details:
        print(f"   Details: {details}")
    if not success and remediation:
        print(f"   Remediation: {remediation}")

def verification1_ecr_images():
    """Verification 1: ECR Image Verification"""
    print_header("VERIFICATION 1: ECR IMAGE VERIFICATION")
    
    results = []
    
    # Define correct ECR images
    correct_images = {
        'frontend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41',
        'backend': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test',
        'rabbitmq': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02',
        'nginx': '545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl'
    }
    
    print_subheader("Getting Tenant Namespaces")
    
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if not success:
        print_verification("ECR Image Verification", False, "Cannot get tenant namespaces", kubectl_cmd, "Check cluster connectivity")
        return [False]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
    print_verification("Tenant Namespaces Discovery", True, f"Found {len(tenant_namespaces)} tenant namespaces", kubectl_cmd)
    
    print_subheader("ECR Image Verification by Component")
    
    for ns in tenant_namespaces[:3]:  # Check first 3 namespaces
        tenant_id = ns.replace('tenant-', '')
        
        for component, correct_image in correct_images.items():
            # Get deployments for this component
            kubectl_cmd = f"kubectl get deployment -n {ns} -o yaml | grep 'image:.*{component}' || kubectl get deployment -n {ns} -o yaml | grep '{correct_image}'"
            success, stdout, stderr = run_command(kubectl_cmd, 15)
            
            if success and correct_image in stdout:
                print_verification(f"ECR Image {component.title()} - {tenant_id}", True, f"Using correct image: {correct_image.split('/')[-1]}", kubectl_cmd)
                results.append(True)
            else:
                # Check if deployment exists but with wrong image
                kubectl_cmd_check = f"kubectl get deployment -n {ns} | grep {component}"
                success_check, stdout_check, stderr_check = run_command(kubectl_cmd_check, 10)
                
                if success_check:
                    deployment_name = stdout_check.split()[0]
                    remediation_cmd = f"kubectl set image deployment/{deployment_name} -n {ns} {component}={correct_image}"
                    print_verification(f"ECR Image {component.title()} - {tenant_id}", False, f"Wrong image in deployment: {deployment_name}", kubectl_cmd, remediation_cmd)
                else:
                    print_verification(f"ECR Image {component.title()} - {tenant_id}", False, f"No {component} deployment found", kubectl_cmd, f"Create {component} deployment")
                results.append(False)
    
    return results

def verification2_database_schema():
    """Verification 2: Database Schema Verification"""
    print_header("VERIFICATION 2: DATABASE SCHEMA VERIFICATION")
    
    results = []
    
    print_subheader("Checking architrave_1.45.2.sql Schema File")
    
    schema_file = "/Users/<USER>/Projects/new_project/infra-provisioning/architrave_1.45.2.sql"
    if os.path.exists(schema_file):
        print_verification("Schema File Existence", True, f"Schema file found: {schema_file}")
        results.append(True)
    else:
        print_verification("Schema File Existence", False, f"Schema file not found: {schema_file}", "", "Ensure schema file is available")
        results.append(False)
    
    print_subheader("Database Schema Verification")
    
    # Create database verification pod
    db_schema_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: db-schema-verification
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 200m
        memory: 512Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(db_schema_yaml)
        pod_file = f.name
    
    try:
        kubectl_cmd = f"kubectl apply -f {pod_file}"
        success, stdout, stderr = run_command(kubectl_cmd)
        if success:
            print_verification("Database Schema Pod Creation", True, "Pod created successfully", kubectl_cmd)
            time.sleep(10)
            
            kubectl_cmd = "kubectl wait --for=condition=ready pod/db-schema-verification --timeout=45s"
            success, stdout, stderr = run_command(kubectl_cmd)
            if success:
                print_verification("Database Schema Pod Ready", True, "Pod is ready for verification", kubectl_cmd)
                
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
                
                # Verify shared 'architrave' database usage
                schema_queries = [
                    ("SHOW DATABASES;", "Database List"),
                    ("USE architrave; SHOW TABLES;", "Architrave Tables"),
                    ("USE architrave; SELECT COUNT(*) as table_count FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave';", "Table Count"),
                    ("USE architrave; SELECT * FROM tenant_config;", "Tenant Configurations"),
                    ("SHOW DATABASES LIKE 'tenant_%';", "Separate Tenant Databases Check"),
                ]
                
                for query, test_name in schema_queries:
                    kubectl_cmd = f"kubectl exec db-schema-verification -- mysql -h {db_host} -P 3306 -u admin -e \"{query}\""
                    success, stdout, stderr = run_command(kubectl_cmd, 20)
                    
                    if success:
                        print_verification(f"SCHEMA - {test_name}", True, "Query executed successfully", kubectl_cmd)
                        results.append(True)
                        
                        # Show important results
                        if "table_count" in query or "tenant_config" in query:
                            lines = stdout.split('\n')
                            if len(lines) > 1:
                                print(f"     Result: {lines[1]}")
                    else:
                        print_verification(f"SCHEMA - {test_name}", False, "Query failed", kubectl_cmd, "Check database connectivity")
                        results.append(False)
            else:
                print_verification("Database Schema Pod Ready", False, "Pod not ready", kubectl_cmd, "Check pod status")
                results.extend([False] * 5)
        else:
            print_verification("Database Schema Pod Creation", False, "Cannot create pod", kubectl_cmd, "Check cluster resources")
            results.extend([False] * 5)
    finally:
        run_command("kubectl delete pod db-schema-verification --ignore-not-found=true")
        os.unlink(pod_file)
    
    return results

def verification3_ssl_certificates():
    """Verification 3: SSL Certificate Configuration"""
    print_header("VERIFICATION 3: SSL CERTIFICATE CONFIGURATION")
    
    results = []
    
    print_subheader("Checking SSL Certificate Files")
    
    cert_file = "/Users/<USER>/Projects/new_project/infra-provisioning/architrave.crt"
    key_file = "/Users/<USER>/Projects/new_project/infra-provisioning/architrave.key"
    
    # Check certificate files
    if os.path.exists(cert_file):
        print_verification("SSL Certificate File", True, f"Certificate found: {cert_file}")
        results.append(True)
    else:
        print_verification("SSL Certificate File", False, f"Certificate not found: {cert_file}", "", "Ensure certificate file is available")
        results.append(False)
    
    if os.path.exists(key_file):
        print_verification("SSL Private Key File", True, f"Private key found: {key_file}")
        results.append(True)
    else:
        print_verification("SSL Private Key File", False, f"Private key not found: {key_file}", "", "Ensure private key file is available")
        results.append(False)
    
    print_subheader("SSL Certificate Configuration in Pods")
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:2]:  # Check first 2 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check if SSL secret exists
            kubectl_cmd = f"kubectl get secret -n {ns} | grep ssl"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                print_verification(f"SSL Secret - {tenant_id}", True, "SSL secret exists", kubectl_cmd)
                results.append(True)
            else:
                remediation_cmd = f"kubectl create secret tls ssl-certs -n {ns} --cert={cert_file} --key={key_file}"
                print_verification(f"SSL Secret - {tenant_id}", False, "SSL secret not found", kubectl_cmd, remediation_cmd)
                results.append(False)
            
            # Check SSL certificate accessibility in frontend pods
            kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                frontend_pod = stdout.split()[0]
                
                kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- ls -la /etc/nginx/cert/ 2>/dev/null || echo 'cert_dir_not_found'"
                success, stdout, stderr = run_command(kubectl_cmd, 10)
                if success and "architrave" in stdout:
                    print_verification(f"SSL Cert Access - {tenant_id}", True, "SSL certificates accessible", kubectl_cmd)
                    results.append(True)
                else:
                    remediation_cmd = f"Mount SSL certificates in frontend deployment"
                    print_verification(f"SSL Cert Access - {tenant_id}", False, "SSL certificates not accessible", kubectl_cmd, remediation_cmd)
                    results.append(False)
            else:
                print_verification(f"Frontend Pod - {tenant_id}", False, "No frontend pod for SSL check", kubectl_cmd, "Check frontend deployment")
                results.append(False)
    else:
        print_verification("SSL Certificate Verification", False, "Cannot get tenant namespaces", kubectl_cmd, "Check cluster connectivity")
        results.extend([False] * 6)
    
    return results

def verification4_istio_service_mesh():
    """Verification 4: Istio Service Mesh Verification"""
    print_header("VERIFICATION 4: ISTIO SERVICE MESH VERIFICATION")
    
    results = []
    
    print_subheader("Istio Installation Verification")
    
    # Check if Istio is installed
    kubectl_cmd = "kubectl get namespace istio-system"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        print_verification("Istio Namespace", True, "istio-system namespace exists", kubectl_cmd)
        results.append(True)
    else:
        print_verification("Istio Namespace", False, "istio-system namespace not found", kubectl_cmd, "Install Istio service mesh")
        results.append(False)
    
    # Check Istio components
    kubectl_cmd = "kubectl get pods -n istio-system"
    success, stdout, stderr = run_command(kubectl_cmd, 15)
    if success:
        istio_pods = stdout.count("Running")
        print_verification("Istio Components", istio_pods > 0, f"Found {istio_pods} running Istio pods", kubectl_cmd)
        results.append(istio_pods > 0)
    else:
        print_verification("Istio Components", False, "Cannot get Istio pods", kubectl_cmd, "Check Istio installation")
        results.append(False)
    
    print_subheader("Istio Sidecar Injection Verification")
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:2]:  # Check first 2 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check if namespace has Istio injection enabled
            kubectl_cmd = f"kubectl get namespace {ns} -o yaml | grep istio-injection"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and "enabled" in stdout:
                print_verification(f"Istio Injection - {tenant_id}", True, "Istio injection enabled", kubectl_cmd)
                results.append(True)
            else:
                remediation_cmd = f"kubectl label namespace {ns} istio-injection=enabled"
                print_verification(f"Istio Injection - {tenant_id}", False, "Istio injection not enabled", kubectl_cmd, remediation_cmd)
                results.append(False)
            
            # Check for Istio sidecars in pods
            kubectl_cmd = f"kubectl get pods -n {ns} -o jsonpath='{{.items[*].spec.containers[*].name}}' | grep istio-proxy"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and "istio-proxy" in stdout:
                print_verification(f"Istio Sidecars - {tenant_id}", True, "Istio sidecars present", kubectl_cmd)
                results.append(True)
            else:
                remediation_cmd = f"Restart pods to inject Istio sidecars"
                print_verification(f"Istio Sidecars - {tenant_id}", False, "No Istio sidecars found", kubectl_cmd, remediation_cmd)
                results.append(False)
    else:
        print_verification("Istio Sidecar Verification", False, "Cannot get tenant namespaces", kubectl_cmd, "Check cluster connectivity")
        results.extend([False] * 4)
    
    return results

def verification5_health_checks():
    """Verification 5: Health Check Implementation"""
    print_header("VERIFICATION 5: HEALTH CHECK IMPLEMENTATION")
    
    results = []
    
    print_subheader("Database Health Check Verification")
    
    # Create health check verification pod
    health_check_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: health-check-verification
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: mysql-client
    image: mysql:8.0
    command: ["sleep", "180"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 100m
        memory: 256Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(health_check_yaml)
        pod_file = f.name
    
    try:
        kubectl_cmd = f"kubectl apply -f {pod_file}"
        success, stdout, stderr = run_command(kubectl_cmd)
        if success:
            time.sleep(8)
            kubectl_cmd = "kubectl wait --for=condition=ready pod/health-check-verification --timeout=30s"
            success, stdout, stderr = run_command(kubectl_cmd)
            if success:
                db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
                
                # Test database connectivity health check
                kubectl_cmd = f"kubectl exec health-check-verification -- mysql -h {db_host} -P 3306 -u admin -e 'SELECT 1;'"
                success, stdout, stderr = run_command(kubectl_cmd, 15)
                print_verification("Database Health Check", success, "Database connectivity verified" if success else "Database connectivity failed", kubectl_cmd)
                results.append(success)
            else:
                print_verification("Health Check Pod Ready", False, "Pod not ready", kubectl_cmd, "Check pod status")
                results.append(False)
        else:
            print_verification("Health Check Pod Creation", False, "Cannot create pod", kubectl_cmd, "Check cluster resources")
            results.append(False)
    finally:
        run_command("kubectl delete pod health-check-verification --ignore-not-found=true")
        os.unlink(pod_file)
    
    print_subheader("Frontend/Backend API Health Endpoints")
    
    # Get tenant namespaces
    kubectl_cmd = "kubectl get namespaces --no-headers | grep tenant-"
    success, stdout, stderr = run_command(kubectl_cmd, 10)
    if success:
        tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        for ns in tenant_namespaces[:2]:  # Check first 2 namespaces
            tenant_id = ns.replace('tenant-', '')
            
            # Check for health check services
            kubectl_cmd = f"kubectl get service -n {ns} | grep health"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                health_service = stdout.split()[0]
                print_verification(f"Health Service - {tenant_id}", True, f"Health service found: {health_service}", kubectl_cmd)
                results.append(True)
            else:
                print_verification(f"Health Service - {tenant_id}", False, "No health service found", kubectl_cmd, "Create health check service")
                results.append(False)
            
            # Test frontend health endpoint
            kubectl_cmd = f"kubectl get pods -n {ns} | grep frontend"
            success, stdout, stderr = run_command(kubectl_cmd, 10)
            if success and stdout:
                frontend_pod = stdout.split()[0]
                
                kubectl_cmd = f"kubectl exec {frontend_pod} -n {ns} -- curl -s http://localhost/health 2>/dev/null || echo 'health_check_failed'"
                success, stdout, stderr = run_command(kubectl_cmd, 15)
                health_works = success and "health_check_failed" not in stdout
                print_verification(f"Frontend Health Endpoint - {tenant_id}", health_works, "Health endpoint accessible" if health_works else "Health endpoint failed", kubectl_cmd)
                results.append(health_works)
            else:
                print_verification(f"Frontend Health Endpoint - {tenant_id}", False, "No frontend pod for health check", kubectl_cmd, "Check frontend deployment")
                results.append(False)
    else:
        print_verification("API Health Endpoints", False, "Cannot get tenant namespaces", kubectl_cmd, "Check cluster connectivity")
        results.extend([False] * 4)
    
    return results

def main():
    """Main comprehensive verification function."""
    print("🔍 COMPREHENSIVE VERIFICATION AND REMEDIATION PROCESS")
    print("=" * 80)
    print(f"Verification started at: {datetime.now()}")
    
    all_results = []
    verification_results = {}
    
    # Execute all verifications
    print("\n🚀 EXECUTING COMPREHENSIVE VERIFICATIONS")
    
    # Verification 1: ECR Image Verification
    ver1_results = verification1_ecr_images()
    all_results.extend(ver1_results)
    verification_results['Verification 1 - ECR Images'] = ver1_results
    
    # Verification 2: Database Schema Verification
    ver2_results = verification2_database_schema()
    all_results.extend(ver2_results)
    verification_results['Verification 2 - Database Schema'] = ver2_results
    
    # Verification 3: SSL Certificate Configuration
    ver3_results = verification3_ssl_certificates()
    all_results.extend(ver3_results)
    verification_results['Verification 3 - SSL Certificates'] = ver3_results
    
    # Verification 4: Istio Service Mesh Verification
    ver4_results = verification4_istio_service_mesh()
    all_results.extend(ver4_results)
    verification_results['Verification 4 - Istio Service Mesh'] = ver4_results
    
    # Verification 5: Health Check Implementation
    ver5_results = verification5_health_checks()
    all_results.extend(ver5_results)
    verification_results['Verification 5 - Health Checks'] = ver5_results
    
    return all_results, verification_results

if __name__ == "__main__":
    all_results, verification_results = main()
    
    # Calculate and display verification results
    passed = sum(all_results)
    total = len(all_results)
    success_rate = (passed / total) * 100 if total > 0 else 0
    
    print_header("COMPREHENSIVE VERIFICATION SUMMARY")
    
    print(f"📊 OVERALL VERIFICATION RESULTS:")
    print(f"  Tests passed: {passed}/{total}")
    print(f"  Success rate: {success_rate:.1f}%")
    
    print(f"\n📋 VERIFICATION-BY-VERIFICATION BREAKDOWN:")
    for ver_name, results in verification_results.items():
        ver_passed = sum(results)
        ver_total = len(results)
        ver_rate = (ver_passed / ver_total) * 100 if ver_total > 0 else 0
        status = "✅" if ver_rate >= 70 else "⚠️" if ver_rate >= 50 else "❌"
        print(f"  {status} {ver_name}: {ver_passed}/{ver_total} ({ver_rate:.1f}%)")
    
    if success_rate >= 75:
        print(f"\n🎉 COMPREHENSIVE VERIFICATION SUCCESSFUL!")
        print(f"✅ System is {success_rate:.1f}% functional")
        sys.exit(0)
    else:
        print(f"\n⚠️ COMPREHENSIVE VERIFICATION ISSUES DETECTED")
        print(f"❌ System is {success_rate:.1f}% functional - Remediation required")
        sys.exit(1)
