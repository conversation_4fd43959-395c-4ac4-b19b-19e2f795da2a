#!/usr/bin/env python3
"""
Real Tenant Event Integration Script

This script modifies your existing tenant onboarding/offboarding scripts
to automatically register events with the Advanced Tenant Management System.
"""

import os
import sys
import json
import logging
import requests
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Any
import argparse

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealTenantEventIntegrator:
    """Integrates real tenant operations with the management system."""
    
    def __init__(self, api_base_url: str = "http://localhost:8081"):
        self.api_base_url = api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'RealTenantEventIntegrator/1.0'
        })
    
    def register_tenant_event(self, tenant_id: str, event_type: str, 
                            event_data: Dict[str, Any], initiated_by: str = "system") -> bool:
        """Register a tenant event with the management system."""
        try:
            payload = {
                'tenant_id': tenant_id,
                'event_type': event_type,
                'event_data': event_data,
                'initiated_by': initiated_by,
                'timestamp': datetime.utcnow().isoformat()
            }
            
            # Try to send to event API (if available)
            try:
                response = self.session.post(
                    f"{self.api_base_url}/api/tenants/{tenant_id}/events",
                    json=payload,
                    timeout=10
                )
                
                if response.status_code in [200, 201]:
                    logger.info(f"✅ Registered event {event_type} for tenant {tenant_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Event API returned {response.status_code}")
            except Exception as api_error:
                logger.warning(f"⚠️ Event API not available: {api_error}")
            
            # Fallback: Store event locally
            self._store_event_locally(tenant_id, event_type, event_data, initiated_by)
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to register event: {e}")
            return False
    
    def _store_event_locally(self, tenant_id: str, event_type: str, 
                           event_data: Dict[str, Any], initiated_by: str):
        """Store event locally as fallback."""
        try:
            events_dir = "/tmp/tenant-events"
            os.makedirs(events_dir, exist_ok=True)
            
            event_file = os.path.join(events_dir, f"{tenant_id}-events.json")
            
            # Load existing events
            events = []
            if os.path.exists(event_file):
                try:
                    with open(event_file, 'r') as f:
                        events = json.load(f)
                except:
                    events = []
            
            # Add new event
            new_event = {
                'tenant_id': tenant_id,
                'event_type': event_type,
                'event_data': event_data,
                'initiated_by': initiated_by,
                'timestamp': datetime.utcnow().isoformat()
            }
            events.append(new_event)
            
            # Save events
            with open(event_file, 'w') as f:
                json.dump(events, f, indent=2)
            
            logger.info(f"📁 Stored event locally: {event_file}")
            
        except Exception as e:
            logger.error(f"❌ Failed to store event locally: {e}")
    
    def onboard_tenant_with_tracking(self, tenant_id: str, tenant_name: str, 
                                   subdomain: str, tier: str = "standard") -> bool:
        """Onboard a tenant with event tracking."""
        
        # Register onboarding start event
        event_data = {
            'tenant_name': tenant_name,
            'subdomain': subdomain,
            'tier': tier,
            'components': ['database', 'frontend', 'backend', 'rabbitmq', 's3'],
            'status': 'started'
        }
        
        self.register_tenant_event(tenant_id, 'onboard', event_data, 'admin')
        
        try:
            # Execute the actual onboarding script
            onboarding_script = "tenant-management/scripts/advanced_tenant_onboard.py"
            
            cmd = [
                'python3', onboarding_script,
                '--tenant-id', tenant_id,
                '--tenant-name', tenant_name,
                '--subdomain', subdomain,
                '--debug'
            ]
            
            logger.info(f"🚀 Starting onboarding for tenant {tenant_id}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                # Onboarding successful
                success_data = {
                    'tenant_name': tenant_name,
                    'subdomain': subdomain,
                    'tier': tier,
                    'status': 'completed',
                    'components_deployed': ['database', 'frontend', 'backend', 'rabbitmq', 's3']
                }
                self.register_tenant_event(tenant_id, 'onboard_completed', success_data, 'admin')
                logger.info(f"✅ Successfully onboarded tenant {tenant_id}")
                return True
            else:
                # Onboarding failed
                error_data = {
                    'tenant_name': tenant_name,
                    'status': 'failed',
                    'error_message': result.stderr or result.stdout
                }
                self.register_tenant_event(tenant_id, 'onboard_failed', error_data, 'admin')
                logger.error(f"❌ Onboarding failed for tenant {tenant_id}")
                return False
                
        except subprocess.TimeoutExpired:
            timeout_data = {
                'tenant_name': tenant_name,
                'status': 'timeout',
                'error_message': 'Onboarding timeout after 30 minutes'
            }
            self.register_tenant_event(tenant_id, 'onboard_timeout', timeout_data, 'admin')
            logger.error(f"⏰ Onboarding timeout for tenant {tenant_id}")
            return False
        except Exception as e:
            error_data = {
                'tenant_name': tenant_name,
                'status': 'error',
                'error_message': str(e)
            }
            self.register_tenant_event(tenant_id, 'onboard_error', error_data, 'admin')
            logger.error(f"💥 Onboarding error for tenant {tenant_id}: {e}")
            return False
    
    def offboard_tenant_with_tracking(self, tenant_id: str, backup: bool = True) -> bool:
        """Offboard a tenant with event tracking."""
        
        # Register offboarding start event
        event_data = {
            'backup_requested': backup,
            'components': ['database', 'frontend', 'backend', 'rabbitmq', 's3'],
            'status': 'started'
        }
        
        self.register_tenant_event(tenant_id, 'offboard', event_data, 'admin')
        
        try:
            # Execute the actual offboarding script
            offboarding_script = "tenant-management/scripts/advanced_tenant_offboard.py"
            
            cmd = [
                'python3', offboarding_script,
                '--tenant-id', tenant_id,
                '--debug'
            ]
            
            if backup:
                cmd.extend(['--backup'])
            
            logger.info(f"🗑️ Starting offboarding for tenant {tenant_id}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=1800  # 30 minutes timeout
            )
            
            if result.returncode == 0:
                # Offboarding successful
                success_data = {
                    'backup_created': backup,
                    'status': 'completed',
                    'components_removed': ['database', 'frontend', 'backend', 'rabbitmq', 's3']
                }
                self.register_tenant_event(tenant_id, 'offboard_completed', success_data, 'admin')
                logger.info(f"✅ Successfully offboarded tenant {tenant_id}")
                return True
            else:
                # Offboarding failed
                error_data = {
                    'status': 'failed',
                    'error_message': result.stderr or result.stdout
                }
                self.register_tenant_event(tenant_id, 'offboard_failed', error_data, 'admin')
                logger.error(f"❌ Offboarding failed for tenant {tenant_id}")
                return False
                
        except subprocess.TimeoutExpired:
            timeout_data = {
                'status': 'timeout',
                'error_message': 'Offboarding timeout after 30 minutes'
            }
            self.register_tenant_event(tenant_id, 'offboard_timeout', timeout_data, 'admin')
            logger.error(f"⏰ Offboarding timeout for tenant {tenant_id}")
            return False
        except Exception as e:
            error_data = {
                'status': 'error',
                'error_message': str(e)
            }
            self.register_tenant_event(tenant_id, 'offboard_error', error_data, 'admin')
            logger.error(f"💥 Offboarding error for tenant {tenant_id}: {e}")
            return False

def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description='Real Tenant Event Integration')
    parser.add_argument('action', choices=['onboard', 'offboard', 'test'], 
                       help='Action to perform')
    parser.add_argument('--tenant-id', required=True, help='Tenant ID')
    parser.add_argument('--tenant-name', help='Tenant name (for onboarding)')
    parser.add_argument('--subdomain', help='Subdomain (for onboarding)')
    parser.add_argument('--tier', default='standard', choices=['basic', 'standard', 'premium'],
                       help='Tenant tier (for onboarding)')
    parser.add_argument('--backup', action='store_true', help='Create backup (for offboarding)')
    parser.add_argument('--api-url', default='http://localhost:8081',
                       help='API base URL')
    
    args = parser.parse_args()
    
    # Initialize integrator
    integrator = RealTenantEventIntegrator(args.api_url)
    
    if args.action == 'onboard':
        if not args.tenant_name or not args.subdomain:
            logger.error("❌ Tenant name and subdomain are required for onboarding")
            sys.exit(1)
        
        success = integrator.onboard_tenant_with_tracking(
            args.tenant_id, args.tenant_name, args.subdomain, args.tier
        )
        
        sys.exit(0 if success else 1)
    
    elif args.action == 'offboard':
        success = integrator.offboard_tenant_with_tracking(
            args.tenant_id, args.backup
        )
        
        sys.exit(0 if success else 1)
    
    elif args.action == 'test':
        # Test event registration
        test_data = {
            'test': True,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        success = integrator.register_tenant_event(
            args.tenant_id, 'test_event', test_data, 'test_user'
        )
        
        if success:
            logger.info("✅ Test event registration successful")
            sys.exit(0)
        else:
            logger.error("❌ Test event registration failed")
            sys.exit(1)

if __name__ == '__main__':
    main()
